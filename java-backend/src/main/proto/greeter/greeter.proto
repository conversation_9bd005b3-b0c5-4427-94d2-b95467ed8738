syntax = "proto3";

package monkeyfx.api.v1.greeter;

import "api-protos/v1/common/common.proto";

option java_multiple_files = true;
option java_package = "com.monkeyfx.grpc.api.v1.greeter";
option java_outer_classname = "<PERSON>reeterProto";
option go_package = "github.com/monkeyfx/electron-go-grpc-demo/gen/go/api/v1/greeter";

service Greeter {
  rpc <PERSON><PERSON> (HelloRequest) returns (HelloReply);
}

message HelloRequest {
  string name = 1;
}

message HelloReply {
  string message = 1;
  string original_request = 2;
  string server_id = 3;
  string timestamp = 4;
}