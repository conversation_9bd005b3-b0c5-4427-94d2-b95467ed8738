#!/usr/bin/env python3
"""
前端日志系统迁移脚本
自动将前端的console调用迁移到新的统一日志系统

运行方式：
python migrate_frontend_logging.py
"""

import os
import re
from pathlib import Path
from typing import List, Dict, Tuple

def analyze_console_usage(file_path: Path) -> Dict[str, int]:
    """分析文件中的console使用情况"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        stats = {
            'console.log': len(re.findall(r'console\.log\(', content)),
            'console.error': len(re.findall(r'console\.error\(', content)),
            'console.warn': len(re.findall(r'console\.warn\(', content)),
            'console.info': len(re.findall(r'console\.info\(', content)),
            'console.debug': len(re.findall(r'console\.debug\(', content)),
        }
        
        return stats
    except Exception:
        return {}

def migrate_frontend_file(file_path: Path) -> Tu<PERSON>[bool, int]:
    """迁移单个前端文件的日志系统"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        changes_count = 0
        
        # 检查是否需要添加logger导入
        needs_logger_import = False
        if re.search(r'console\.(log|error|warn|info|debug)\(', content):
            needs_logger_import = True
        
        # 1. 添加logger导入（针对.js文件）
        if needs_logger_import and file_path.suffix == '.js':
            if 'import logger from' not in content and 'const logger =' not in content:
                # 查找合适的导入位置
                import_pattern = r'(import.*from.*[\'"].*[\'"];?\s*\n)'
                imports = re.findall(import_pattern, content)
                
                if imports:
                    # 在最后一个import后添加logger导入
                    last_import = imports[-1]
                    logger_import = "import logger from '../utils/logger.js';\n"
                    content = content.replace(last_import, last_import + logger_import)
                    changes_count += 1
                else:
                    # 在文件开头添加
                    logger_import = "import logger from '../utils/logger.js';\n\n"
                    content = logger_import + content
                    changes_count += 1
        
        # 2. 替换console调用
        console_replacements = [
            # 基础替换
            (r'console\.log\(([^)]+)\)', r'logger.info(\1)'),
            (r'console\.error\(([^)]+)\)', r'logger.error(\1)'),
            (r'console\.warn\(([^)]+)\)', r'logger.warn(\1)'),
            (r'console\.info\(([^)]+)\)', r'logger.info(\1)'),
            (r'console\.debug\(([^)]+)\)', r'logger.debug(\1)'),
            
            # 特殊情况：错误处理
            (r'console\.error\(\s*[\'"`]([^\'"`]+)[\'"`]\s*,\s*([^)]+)\)', 
             r'logger.error(\'\1\', { error: \2 })'),
             
            # IPC处理器错误
            (r'console\.error\(\s*[\'"`]\[([^\]]+)\]\s*([^\'"`]+)[\'"`]\s*,\s*([^)]+)\)',
             r'logger.error(\'[\1] \2\', { error: \3, module: \'\1\' })'),
        ]
        
        for pattern, replacement in console_replacements:
            old_content = content
            content = re.sub(pattern, replacement, content)
            if content != old_content:
                changes_count += len(re.findall(pattern, old_content))
        
        # 3. 针对Vue文件的特殊处理
        if file_path.suffix == '.vue':
            # 在script标签中添加logger导入
            if needs_logger_import and 'import logger from' not in content:
                script_pattern = r'(<script[^>]*>)\s*\n'
                match = re.search(script_pattern, content)
                if match:
                    script_start = match.group(1)
                    logger_import = f"{script_start}\nimport logger from '@/utils/logger.js';\n"
                    content = content.replace(script_start, logger_import)
                    changes_count += 1
        
        # 4. 保存修改后的内容
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            return True, changes_count
        else:
            return False, 0
            
    except Exception as e:
        print(f"❌ 迁移失败 {file_path}: {e}")
        return False, 0

def scan_frontend_files() -> List[Path]:
    """扫描需要迁移的前端文件"""
    frontend_dirs = [
        Path("electron-app/src/main-process"),
        Path("electron-app/src/renderer"),
        Path("electron-app/src/preload")
    ]
    
    # 排除已经是日志系统的文件
    exclude_patterns = [
        "logger.js",
        "main-logger.js",
        "test_frontend_logging.js",
        "__pycache__",
        "node_modules",
        ".git"
    ]
    
    files_to_migrate = []
    for frontend_dir in frontend_dirs:
        if frontend_dir.exists():
            for pattern in ["**/*.js", "**/*.vue", "**/*.ts"]:
                for file_path in frontend_dir.glob(pattern):
                    # 检查是否应该排除
                    should_exclude = False
                    for exclude_pattern in exclude_patterns:
                        if exclude_pattern in str(file_path):
                            should_exclude = True
                            break
                    
                    if not should_exclude:
                        files_to_migrate.append(file_path)
    
    return files_to_migrate

def analyze_frontend_usage() -> Dict[str, any]:
    """分析前端console使用情况"""
    files = scan_frontend_files()
    
    stats = {
        'total_files': len(files),
        'files_with_console': 0,
        'total_console_calls': 0,
        'by_type': {
            'console.log': 0,
            'console.error': 0,
            'console.warn': 0,
            'console.info': 0,
            'console.debug': 0,
        },
        'by_directory': {}
    }
    
    for file_path in files:
        file_stats = analyze_console_usage(file_path)
        total_calls = sum(file_stats.values())
        
        if total_calls > 0:
            stats['files_with_console'] += 1
            stats['total_console_calls'] += total_calls
            
            # 按类型统计
            for call_type, count in file_stats.items():
                stats['by_type'][call_type] += count
            
            # 按目录统计
            dir_name = str(file_path.parent.relative_to('electron-app/src'))
            if dir_name not in stats['by_directory']:
                stats['by_directory'][dir_name] = 0
            stats['by_directory'][dir_name] += total_calls
    
    return stats

def create_vue_integration_example():
    """创建Vue组件日志集成示例"""
    example_content = '''<template>
  <div class="example-component">
    <h2>日志集成示例组件</h2>
    <button @click="handleUserAction">测试用户操作日志</button>
    <button @click="handleApiCall">测试API调用日志</button>
    <button @click="handleError">测试错误日志</button>
  </div>
</template>

<script>
import logger from '@/utils/logger.js';

export default {
  name: 'LoggingExampleComponent',
  
  data() {
    return {
      componentTraceId: null
    };
  },
  
  created() {
    // 组件创建时记录日志
    this.componentTraceId = logger.generateTraceId();
    logger.setTraceId(this.componentTraceId);
    
    logger.logVueComponent(this.$options.name, 'created', {
      trace_id: this.componentTraceId
    });
  },
  
  mounted() {
    // 组件挂载时记录日志
    logger.logVueComponent(this.$options.name, 'mounted', {
      trace_id: this.componentTraceId
    });
  },
  
  beforeDestroy() {
    // 组件销毁前记录日志
    logger.logVueComponent(this.$options.name, 'beforeDestroy', {
      trace_id: this.componentTraceId
    });
  },
  
  methods: {
    handleUserAction() {
      // 用户操作日志
      logger.logUserAction('button_click', {
        component: this.$options.name,
        action: 'test_user_action',
        trace_id: this.componentTraceId
      });
    },
    
    async handleApiCall() {
      // API调用日志示例
      const operation = logger.createOperationContext('api_test_call', this.componentTraceId);
      
      try {
        operation.log('INFO', 'Starting API call test');
        
        // 模拟API调用
        const startTime = Date.now();
        await new Promise(resolve => setTimeout(resolve, 1000));
        const duration = Date.now() - startTime;
        
        logger.logApiCall('/api/test', 'GET', duration, 200, {
          trace_id: this.componentTraceId
        });
        
        operation.complete('success');
        
      } catch (error) {
        operation.error(error);
        logger.logVueError(error, this.$options.name, {
          trace_id: this.componentTraceId
        });
      }
    },
    
    handleError() {
      try {
        // 故意制造错误
        throw new Error('This is a test error for logging demonstration');
      } catch (error) {
        // 错误日志示例
        logger.logVueError(error, this.$options.name, {
          trace_id: this.componentTraceId,
          user_action: 'test_error_handling'
        });
      }
    }
  },
  
  // Vue错误处理钩子
  errorCaptured(err, instance, info) {
    logger.logVueError(err, this.$options.name, {
      trace_id: this.componentTraceId,
      error_info: info,
      child_component: instance.$options.name
    });
    
    return false; // 继续传播错误
  }
};
</script>

<style scoped>
.example-component {
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
  margin: 10px;
}

button {
  margin: 5px;
  padding: 10px 15px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

button:hover {
  background: #0056b3;
}
</style>'''
    
    example_path = Path("electron-app/src/renderer/components/LoggingExample.vue")
    
    try:
        with open(example_path, 'w', encoding='utf-8') as f:
            f.write(example_content)
        print(f"✅ 已创建Vue日志集成示例: {example_path}")
        return True
    except Exception as e:
        print(f"❌ 创建Vue示例失败: {e}")
        return False

def main():
    print("🔄 开始前端日志系统迁移")
    
    # 分析当前使用情况
    print("\n📊 分析当前console使用情况...")
    stats = analyze_frontend_usage()
    
    print(f"   总文件数: {stats['total_files']}")
    print(f"   使用console的文件: {stats['files_with_console']}")
    print(f"   总console调用数: {stats['total_console_calls']}")
    
    print("\n📈 按类型统计:")
    for call_type, count in stats['by_type'].items():
        if count > 0:
            print(f"   {call_type}: {count}")
    
    print("\n📂 按目录统计:")
    for directory, count in stats['by_directory'].items():
        if count > 0:
            print(f"   {directory}: {count}")
    
    # 获取需要迁移的文件
    files_to_migrate = scan_frontend_files()
    
    print(f"\n🎯 准备迁移 {len(files_to_migrate)} 个文件...")
    
    # 执行迁移
    migrated_count = 0
    total_changes = 0
    
    for file_path in files_to_migrate:
        migrated, changes = migrate_frontend_file(file_path)
        if migrated:
            migrated_count += 1
            total_changes += changes
            print(f"✅ 已迁移: {file_path} ({changes} 处修改)")
        # 只显示有修改的文件，避免输出过多
    
    print(f"\n✅ 前端迁移完成！")
    print(f"   成功迁移: {migrated_count} 个文件")
    print(f"   总修改数: {total_changes} 处")
    print(f"   总处理: {len(files_to_migrate)} 个文件")
    
    # 创建Vue集成示例
    print("\n📝 创建Vue日志集成示例...")
    create_vue_integration_example()
    
    # 迁移后分析
    print("\n📊 迁移后状态分析...")
    final_stats = analyze_frontend_usage()
    print(f"   剩余console调用: {final_stats['total_console_calls']}")
    
    if final_stats['total_console_calls'] < stats['total_console_calls']:
        reduction = stats['total_console_calls'] - final_stats['total_console_calls']
        print(f"   成功减少: {reduction} 个console调用")
    
    print("\n💡 迁移建议:")
    print("   1. 检查剩余的console调用是否需要保留（如调试代码）")
    print("   2. 测试迁移后的日志功能")
    print("   3. 在Vue项目中使用LoggingExample.vue作为参考")
    print("   4. 配置生产环境的日志级别")

if __name__ == "__main__":
    main() 