#!/bin/bash
# proto_sync.sh
# 这个脚本负责将集中管理的proto文件编译到各个服务中，并执行后续构建步骤。
# 重要: 请在运行此脚本前确保已激活目标服务所需的环境 (如 Conda, NVM)。

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 全局变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
PROTO_DIR_RELATIVE="api-protos/v1" # Renamed for clarity
PROTO_BASE_DIR="$PROJECT_ROOT/$PROTO_DIR_RELATIVE"

# Define specific proto files - adjust as services evolve
COMMON_PROTO_FILES=("$PROTO_BASE_DIR/common/common.proto")
GREETER_PROTO_FILES=("$PROTO_BASE_DIR/greeter/greeter.proto")
SUBTITLER_PROTO_FILES=("$PROTO_BASE_DIR/subtitler/subtitler.proto")
AI_CONFIG_PROTO_FILES=("$PROTO_BASE_DIR/ai_config/ai_config_service.proto")
# Add buf/validate proto for ProtoValidate v2.0 support
VALIDATE_PROTO_FILES=("$PROJECT_ROOT/api-protos/buf/validate/validate.proto")

# Proto files needed by Python backend (Subtitler service + AI Config + Common + Greeter + Validate)
PYTHON_PROTOS_ABSOLUTE=("${COMMON_PROTO_FILES[@]}" "${SUBTITLER_PROTO_FILES[@]}" "${GREETER_PROTO_FILES[@]}" "${AI_CONFIG_PROTO_FILES[@]}" "${VALIDATE_PROTO_FILES[@]}")
# Proto files needed by Go backend (Greeter service + Common)
GO_PROTOS_ABSOLUTE=("${COMMON_PROTO_FILES[@]}" "${GREETER_PROTO_FILES[@]}")
# Proto files needed by Java backend (Greeter service + Common - assuming, adjust if different)
JAVA_PROTOS_ABSOLUTE=("${COMMON_PROTO_FILES[@]}" "${GREETER_PROTO_FILES[@]}")
# Proto files needed by Electron frontend (all services + Common + Validate for ProtoValidate)
ELECTRON_PROTOS_ABSOLUTE=("${COMMON_PROTO_FILES[@]}" "${GREETER_PROTO_FILES[@]}" "${SUBTITLER_PROTO_FILES[@]}" "${AI_CONFIG_PROTO_FILES[@]}" "${VALIDATE_PROTO_FILES[@]}")

# 服务目录定义
PYTHON_SERVICE_PATH="backend"
GO_SERVICE_PATH="go-backend"
JAVA_SERVICE_PATH="java-backend"
ELECTRON_APP_PATH="electron-app"


# --- Helper Functions ---
function log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
function log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
function log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
function log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

function show_usage() {
    echo "用法: $0 [选项]"
    echo "选项:"
    echo "  --all                  编译所有服务 (默认)"
    echo "  --python               仅编译和构建Python服务"
    echo "  --go                   仅编译和构建Go服务"
    echo "  --java                 仅编译和构建Java服务"
    echo "  --electron             仅编译和构建Electron前端"
    echo "  --force                强制重新编译，即使没有变化"
    echo "  --proto-only           仅生成protobuf文件，跳过打包和构建步骤"
    echo "  --clean-pex-cache      清理PEX缓存 (自动包含在Python构建中)"
    echo "  --help                 显示帮助信息"
    exit 1
}

function check_protoc() {
    if ! command -v protoc &> /dev/null; then
        log_error "未找到protoc编译器。请先安装Protocol Buffers。"
        echo "安装说明: https://grpc.io/docs/protoc-installation/"
        exit 1
    fi
}

# --- Service Specific Build Functions ---

# Python Service
function clean_pex_cache() {
    local pex_cache_dir="$HOME/Library/Caches/pex"
    if [[ -d "$pex_cache_dir" ]]; then
        log_info "Python: 清理PEX缓存 ($pex_cache_dir)..."
        rm -rf "$pex_cache_dir"
        log_success "Python: PEX缓存清理完成。"
    else
        log_info "Python: PEX缓存目录不存在，无需清理。"
    fi
}

function handle_python_service() {
    if [[ "$BUILD_PYTHON" != "true" ]]; then return; fi
    log_info "--- 生成Python protobuf文件 ($PYTHON_SERVICE_PATH) ---"
    
    local service_dir="$PROJECT_ROOT/$PYTHON_SERVICE_PATH"

    # Python gRPC工具将在Conda环境中调用，此处不作全局检查

    # Python service path relative to PROJECT_ROOT
    local py_service_path_relative="$PYTHON_SERVICE_PATH" # "backend"

    local conda_env_name="electron"
    log_info "Python: 使用 Conda 环境 '$conda_env_name' 执行protoc编译..."

    if ! conda run -n "$conda_env_name" python -m grpc_tools.protoc --version &> /dev/null; then
        log_error "Python: Conda 环境 '$conda_env_name' 中未找到或无法执行 'python -m grpc_tools.protoc'。"
        log_error "请确保环境 '$conda_env_name' 已创建并已安装 'grpcio-tools' (pip install grpcio-tools)。"
        return 1 # Already in PROJECT_ROOT or will be cd'd back by caller
    fi

    # Convert absolute proto paths to relative paths for protoc (relative to $PROJECT_ROOT)
    local PYTHON_PROTOS_RELATIVE=()
    for proto_abs_path in "${PYTHON_PROTOS_ABSOLUTE[@]}"; do
        PYTHON_PROTOS_RELATIVE+=("${proto_abs_path#$PROJECT_ROOT/}")
    done

    log_info "Python: Generating stubs directly into project root structure ($PROJECT_ROOT/api_protos/v1/...)"
    # Ensure the target base directory exists (e.g., $PROJECT_ROOT/api_protos/v1)
    # protoc will create subdirs like common, greeter within this based on proto paths
    mkdir -p "$PROJECT_ROOT/api_protos/v1" # Create if not exists, from project root

    # Run protoc from project root, outputting to project root
    # This should make generated imports like 'from api_protos.v1.common import ...'

    log_info "Python: Ensuring __init__.py files exist for protoc generated packages in project root..."
    mkdir -p "$PROJECT_ROOT/api_protos/v1" # Ensure base output directory exists
    touch "$PROJECT_ROOT/api_protos/__init__.py"
    touch "$PROJECT_ROOT/api_protos/v1/__init__.py"
    # Ensure __init__.py files for all service subdirectories
    mkdir -p "$PROJECT_ROOT/api_protos/v1/common" && touch "$PROJECT_ROOT/api_protos/v1/common/__init__.py"
    mkdir -p "$PROJECT_ROOT/api_protos/v1/greeter" && touch "$PROJECT_ROOT/api_protos/v1/greeter/__init__.py"
    mkdir -p "$PROJECT_ROOT/api_protos/v1/subtitler" && touch "$PROJECT_ROOT/api_protos/v1/subtitler/__init__.py"
    mkdir -p "$PROJECT_ROOT/api_protos/v1/ai_config" && touch "$PROJECT_ROOT/api_protos/v1/ai_config/__init__.py"
    # Ensure buf/validate directory for protovalidate
    mkdir -p "$PROJECT_ROOT/api_protos/buf" && touch "$PROJECT_ROOT/api_protos/buf/__init__.py"
    mkdir -p "$PROJECT_ROOT/api_protos/buf/validate" && touch "$PROJECT_ROOT/api_protos/buf/validate/__init__.py"
    log_info "Python: __init__.py files ensured in $PROJECT_ROOT/api_protos/ and all service subdirectories"

    # First generate standard protobuf and gRPC code
    (
      cd "$PROJECT_ROOT" && \
      conda run -n "$conda_env_name" python -m grpc_tools.protoc \
          --proto_path="$PROJECT_ROOT" \
          --python_out="." \
          --grpc_python_out="." \
          "${PYTHON_PROTOS_RELATIVE[@]}"
    )
    
    # Note: buf/validate.proto is already included in the main protoc command above

    log_info "Python: Copying generated stubs from $PROJECT_ROOT/api_protos to $service_dir/api_protos..."
    # Ensure target base directory in backend exists and is clean
    mkdir -p "$service_dir/api_protos"
    # Remove old content from backend/api_protos to ensure it's a fresh copy
    # Be careful with 'rm -rf path/*' vs 'rm -rf path'. We want to clear contents, not remove api_protos itself if it's a symlink or special.
    # A safer approach is to remove specific subdirectories if they exist, or just let cp -R overwrite.
    # For simplicity and assuming api_protos in backend is meant to be a mirror of the generated stubs:
    rm -rf "$service_dir/api_protos/"*

    # Copy the generated 'api_protos/v1', 'api_protos/buf' and 'api_protos/__init__.py' from project root to backend
    if [ -d "$PROJECT_ROOT/api_protos/v1" ]; then
        cp -R "$PROJECT_ROOT/api_protos/v1" "$service_dir/api_protos/"
        log_info "Python: Copied $PROJECT_ROOT/api_protos/v1 to $service_dir/api_protos/"
    else
        log_warning "Python: Source directory $PROJECT_ROOT/api_protos/v1 not found for copying."
    fi
    if [ -d "$PROJECT_ROOT/api_protos/buf" ]; then
        cp -R "$PROJECT_ROOT/api_protos/buf" "$service_dir/api_protos/"
        log_info "Python: Copied $PROJECT_ROOT/api_protos/buf to $service_dir/api_protos/"
    else
        log_warning "Python: Source directory $PROJECT_ROOT/api_protos/buf not found for copying."
    fi
    if [ -f "$PROJECT_ROOT/api_protos/__init__.py" ]; then
        cp "$PROJECT_ROOT/api_protos/__init__.py" "$service_dir/api_protos/"
        log_info "Python: Copied $PROJECT_ROOT/api_protos/__init__.py to $service_dir/api_protos/"
    else
        log_warning "Python: Source file $PROJECT_ROOT/api_protos/__init__.py not found for copying."
    fi
    log_success "Python: Successfully synchronized stubs to $service_dir/api_protos/"

    # 如果指定了 --proto-only，跳过 PEX 打包
    if [[ "$PROTO_ONLY" == "true" ]]; then
        log_info "Python: --proto-only 模式，跳过 PEX 打包步骤"
        log_success "Python: protobuf 文件生成完成。"
        return 0
    fi

    log_info "Python: PEX打包 (from $PROJECT_ROOT)..."
    # PEX command will run from $PROJECT_ROOT
    # Requirements file is at $PROJECT_ROOT/backend/requirements.txt
    # Main module is backend.server:serve
    # Output is $PROJECT_ROOT/dist/server.pex
    (
      cd "$service_dir" && \
      log_info "Python: Downloading dependencies to local 'wheels' directory (./wheels)..." && \
      pip download -r "requirements.txt" -d "wheels" && \
      log_info "Python: Setting PEX_PIP_ARGS to use local 'wheels' directory..." && \
      export PEX_PIP_ARGS="--no-index --find-links=./wheels" && \
      log_info "Python: Building PEX file (server.pex)..." && \
      conda run -n "$conda_env_name" pex -D . \
          -r "requirements.txt" \
          -m "server:serve" \
          -o "../dist/server.pex" && \
      log_info "Python: Cleaning up 'wheels' directory..." && \
      rm -rf "wheels" # Clean up wheels
    )
    # No need to cd back to $PROJECT_ROOT as PEX part is in subshell or already there.
    log_success "Python: 服务处理完成。"
}

# Go Service
function handle_go_service() {
    if [[ "$BUILD_GO" != "true" ]]; then return; fi
    log_info "--- 处理Go服务 ($GO_SERVICE_PATH) ---"

    local service_dir="$PROJECT_ROOT/$GO_SERVICE_PATH"
    # Go的输出路径由 .proto 文件中的 `option go_package` 和 protoc 的 `--go_out` 结合决定。
    # 如果 go_package = "./pb" (在api-protos/v1/services.proto中修改), 且 --go_out=".", 则输出到 ./pb
    # 如果 go_package = "github.com/your-org/your-project/go-backend/gen/go/api/v1",
    # 且 --go_out=".", paths=source_relative, 则输出到 ./gen/go/api/v1

    # 检查Go protoc插件
    if ! command -v protoc-gen-go &> /dev/null || ! command -v protoc-gen-go-grpc &> /dev/null; then
        log_warning "Go: 未找到Go protoc插件 (protoc-gen-go, protoc-gen-go-grpc)。请运行 'go install google.golang.org/protobuf/cmd/protoc-gen-go@latest' 和 'go install google.golang.org/grpc/cmd/protoc-gen-go-grpc@latest'。"
        # return 1
    fi
    
    log_info "Go: 切换到目录 $service_dir"
    cd "$service_dir"

    log_info "Go: 执行protoc编译..."
    
    local go_module_name
    if [ -f "./go.mod" ]; then # We are already in $service_dir
        go_module_name=$(grep -E '^module\s+' "./go.mod" | awk '{print $2}')
    else
        log_error "Go: 未找到 ./go.mod 文件。无法确定Go模块名。"
        cd "$PROJECT_ROOT"; return 1
    fi
    if [ -z "$go_module_name" ]; then
        log_error "Go: 无法从 ./go.mod 文件中提取模块名。"
        cd "$PROJECT_ROOT"; return 1
    fi
    log_info "Go: 使用模块名 '$go_module_name' 进行编译。"

    # --go_out="." 指示输出到当前目录 (即 $service_dir)
    # protoc-gen-go 会根据相对的 go_package ("./gen/go/api/v1") 在此目录下创建子目录结构
    # --go_opt=module=... 告诉 protoc-gen-go 当前 .proto 文件属于哪个 Go 模块，
    # 这对于正确解析相对的 go_package 并生成正确的导入路径元数据至关重要。
    # 移除 module 选项，让 protoc-gen-go 从当前目录的 go.mod 文件自动推断模块路径。
    # paths 选项未指定，默认为 paths=import。
    local GO_PROTOS_RELATIVE=()
    for proto_abs_path in "${GO_PROTOS_ABSOLUTE[@]}"; do
        GO_PROTOS_RELATIVE+=("${proto_abs_path#$PROJECT_ROOT/}")
    done

    # Generate standard protobuf and gRPC code
    protoc \
        --proto_path="$PROJECT_ROOT" \
        --go_out="." \
        --go-grpc_out="." \
        --go_opt=module="$go_module_name" \
        --go-grpc_opt=module="$go_module_name" \
        "${GO_PROTOS_RELATIVE[@]}"
    
    # Generate validation code using protoc-gen-validate
    log_info "Go: Generating validation code using protoc-gen-validate..."
    if command -v protoc-gen-validate &> /dev/null; then
        protoc \
            --proto_path="$PROJECT_ROOT" \
            --validate_out="lang=go:." \
            --validate_opt=module="$go_module_name" \
            "${GO_PROTOS_RELATIVE[@]}"
        log_success "Go: 验证代码生成完成。"
    else
        log_warning "Go: protoc-gen-validate插件未找到，跳过验证代码生成。"
    fi
    log_success "Go: protoc编译完成。"

    # 如果指定了 --proto-only，跳过构建步骤
    if [[ "$PROTO_ONLY" == "true" ]]; then
        log_info "Go: --proto-only 模式，跳过构建步骤"
        cd "$PROJECT_ROOT"
        log_success "Go: protobuf 文件生成完成。"
        return 0
    fi

    log_info "Go: 执行go mod tidy..."
    go mod tidy
    log_success "Go: go mod tidy完成。"

    log_info "Go: 执行go build -o ../dist/go_grpc_server ."
    mkdir -p ../dist
    go build -o ../dist/go_grpc_server .
    log_success "Go: go build完成。"

    cd "$PROJECT_ROOT"
    log_success "Go: 服务处理完成。"
}

# Java Service
function handle_java_service() {
    if [[ "$BUILD_JAVA" != "true" ]]; then return; fi
    log_info "--- 处理Java服务 ($JAVA_SERVICE_PATH) ---"
    local service_dir="$PROJECT_ROOT/$JAVA_SERVICE_PATH"
    # Java的输出基于 java_package, protoc 会在 --java_out 指定的目录下创建包结构
    local java_out_dir="$service_dir/src/main/java" # Standard Maven structure

    log_info "Java: 切换到目录 $service_dir"
    cd "$service_dir"

    local java_proto_src_base="$PROTO_BASE_DIR"
    local java_proto_target_base="src/main/proto" # Standard Maven proto location

    log_info "Java: 清理旧的 proto 文件 (如果存在于 $java_proto_target_base)..."
    rm -rf "$java_proto_target_base"
    
    log_info "Java: 复制所需的 proto 文件到 $java_proto_target_base..."
    # Create subdirectories in src/main/proto to match the source structure for correct import resolution
    # e.g., common.proto goes into src/main/proto/common/common.proto
    # greeter.proto goes into src/main/proto/greeter/greeter.proto
    
    # Copy common.proto
    mkdir -p "$java_proto_target_base/common"
    # Copy common.proto
    mkdir -p "$java_proto_target_base/common"
    cp "$java_proto_src_base/common/common.proto" "$java_proto_target_base/common/"
    
    # Copy greeter.proto
    mkdir -p "$java_proto_target_base/greeter"
    cp "$java_proto_src_base/greeter/greeter.proto" "$java_proto_target_base/greeter/"
    
    # Copy subtitler.proto (if needed by Java, currently JAVA_PROTOS_ABSOLUTE doesn't include it)
    # Ensure JAVA_PROTOS_ABSOLUTE is updated if Subtitler is needed for Java
    # For now, assuming only common and greeter are needed as per JAVA_PROTOS_ABSOLUTE
    # if [[ " ${JAVA_PROTOS_ABSOLUTE[@]} " =~ " ${SUBTITLER_PROTO_FILES[0]} " ]]; then
    #   mkdir -p "$java_proto_target_base/subtitler"
    #   cp "$java_proto_src_base/subtitler/subtitler.proto" "$java_proto_target_base/subtitler/"
    # fi

    log_success "Java: Proto 文件复制完成到 $java_proto_target_base"

    # 如果指定了 --proto-only，跳过 Maven 构建
    if [[ "$PROTO_ONLY" == "true" ]]; then
        log_info "Java: --proto-only 模式，跳过 Maven 构建步骤"
        cd "$PROJECT_ROOT"
        log_success "Java: protobuf 文件准备完成。"
        return 0
    fi

    log_info "Java: Temporarily adjusting import paths in copied .proto files for Maven compilation..."
    # This is a HACK because Maven plugin likely uses src/main/proto as proto_path.
    # We need to change 'import "api-protos/v1/common/common.proto";' back to 'import "common/common.proto";'
    # within the files copied to $java_proto_target_base.
    
    # Example for greeter.proto, extend if other protos in java_proto_target_base also import common.proto
    local copied_greeter_proto="$java_proto_target_base/greeter/greeter.proto"
    if [ -f "$copied_greeter_proto" ]; then
        sed -i.bak 's|import "api-protos/v1/common/common.proto";|import "common/common.proto";|g' "$copied_greeter_proto"
        log_info "Java: Adjusted imports in $copied_greeter_proto"
    fi
    # Add similar sed commands for other .proto files if they are copied and import common.proto

    log_info "Java: 执行 'mvn clean package' (protobuf-maven-plugin 将处理代码生成)..."
    log_warning "Java: 确保已安装Maven和JDK。"
    # 构建标准的 Spring Boot JAR。如果您需要 native image, 请确保 GraalVM 配置正确或稍后单独构建。
    (mvn clean package -Pnative && \
        log_success "Java: Maven 构建完成 (标准 JAR)。" && \
        log_info "Java: 清理已复制的 proto 文件从 '$java_proto_target_base'..." && \
        rm -rf "$java_proto_target_base" && \
        log_success "Java: 已复制的 proto 文件清理完成。" && \
        # Restore .bak files if you want to keep original imports in src/main/proto after build, though usually not needed as they are copies.
        # find "$java_proto_target_base" -name "*.proto.bak" -exec sh -c 'mv "$1" "${1%.bak}"' _ {} \;
        find "$java_proto_target_base" -name "*.proto.bak" -delete # Just delete .bak files
        log_info "Java: Cleaned up .bak files from temporary import adjustment."
        ) || \
    (log_error "Java: Maven 构建或 proto 文件清理失败。" && cd "$PROJECT_ROOT" && exit 1)
    
    # Ensure dist directory exists at project root
    mkdir -p "$PROJECT_ROOT/dist"
    mv target/java-grpc-backend "$PROJECT_ROOT/dist/" # 将构建的 JAR 移动到 dist 目录
    log_info "Java: 将构建的 JAR 移动到 dist 目录。"
    cd "$PROJECT_ROOT"
    log_success "Java: 服务处理完成。"
}

# Electron App
function handle_electron_app() {
    if [[ "$BUILD_ELECTRON" != "true" ]]; then return; fi
    log_info "--- 处理Electron应用 ($ELECTRON_APP_PATH) ---"
    
    local app_dir="$PROJECT_ROOT/$ELECTRON_APP_PATH"
    # JS输出目录，与 index.html 中的引用路径一致
    local js_out_base_dir="$app_dir/src/gen/js" # This is the base for all JS generated files
    # The actual output structure within js_out_base_dir will be determined by protoc based on package/original .proto paths
    # For example, common.proto might go to src/gen/js/common/common_pb.js if paths=source_relative is used or similar.
    # Or it might be flat if paths=import is used and js_out is just one dir.
    # For grpc_tools_node_protoc, it typically creates a flat structure in the output dir.
    
    local original_dir
    original_dir=$(pwd) # Save current directory
    
    log_info "Electron: 切换到应用目录 $app_dir"
    cd "$app_dir"

    # Target base directory for generated JS files, relative to current directory (app_dir)
    local target_js_gen_dir_relative="src/gen/js"

    log_info "Electron: 确保输出目录存在: $target_js_gen_dir_relative"
    mkdir -p "$target_js_gen_dir_relative"

    log_info "Electron: 执行protoc编译 (Node.js gRPC) 使用 npx grpc_tools_node_protoc..."
    log_warning "Electron: 确保 'grpc-tools' 是 electron-app/package.json 中的依赖项。"

    # Generate Node.js gRPC client code
    log_info "Electron: 生成 Node.js gRPC 客户端代码..."
    local ELECTRON_PROTOS_RELATIVE=()
    for proto_abs_path in "${ELECTRON_PROTOS_ABSOLUTE[@]}"; do
        ELECTRON_PROTOS_RELATIVE+=("${proto_abs_path#$PROJECT_ROOT/}")
    done

    ./node_modules/.bin/grpc_tools_node_protoc \
        --js_out=import_style=commonjs,binary:"./$target_js_gen_dir_relative" \
        --grpc_out=grpc_js:"./$target_js_gen_dir_relative" \
        --proto_path="$PROJECT_ROOT" \
        "${ELECTRON_PROTOS_RELATIVE[@]}"

    local exit_code=$?
    if [ $exit_code -ne 0 ]; then
        log_error "Electron: 生成 Node.js gRPC 客户端代码失败，退出码: $exit_code"
        cd "$original_dir"
        return 1
    else
        log_success "Electron: Node.js gRPC 客户端代码生成完成。"
    fi

    cd "$original_dir"
    log_success "Electron: 应用处理完成。"
}


# --- Timestamp and Rebuild Logic ---
function need_rebuild_check() {
    local timestamp_file="$PROTO_BASE_DIR/.proto_timestamp" # Timestamp file remains in the base proto dir

    if [[ "$FORCE_REBUILD" == "true" ]]; then
        log_info "强制重新编译所有选定服务..."
        return 0
    fi
    
    # Check if any .proto files exist in the new structure
    local proto_files_count=$(find "$PROTO_BASE_DIR" -name "*.proto" -type f | wc -l)
    if [[ "$proto_files_count" -eq 0 ]]; then
        log_error "在 $PROTO_BASE_DIR 中未找到任何 .proto 文件。"
        exit 1 # Critical error
    fi

    if [[ ! -f "$timestamp_file" ]]; then
        log_info "时间戳文件 $timestamp_file 未找到，需要编译。"
        return 0
    fi
    
    # Find the modification time of the newest .proto file
    local newest_proto_time=0
    # Using a loop for wider compatibility (macOS stat doesn't have -c %Y directly without gstat)
    # However, the original script used `stat -c %Y` or `stat -f %m`, so we assume one works.
    # For simplicity and to match original script's platform assumption:
    while IFS= read -r -d $'\0' proto_file; do
        current_file_time=$(stat -c %Y "$proto_file" 2>/dev/null || stat -f %m "$proto_file")
        if [[ "$current_file_time" -gt "$newest_proto_time" ]]; then
            newest_proto_time=$current_file_time
        fi
    done <<< "$(find "$PROTO_BASE_DIR" -name "*.proto" -type f -print0)"

    if [[ "$newest_proto_time" -eq 0 ]]; then
        log_warning "无法确定 $PROTO_BASE_DIR 中 .proto 文件的修改时间。将强制检查时间戳文件。"
    fi
    
    local timestamp_time=$(stat -c %Y "$timestamp_file" 2>/dev/null || stat -f %m "$timestamp_file")
    
    if [[ $newest_proto_time -gt $timestamp_time ]]; then
        log_info "一个或多个Proto文件已更新，需要重新编译。"
        return 0
    fi
    
    log_info "所有Proto文件无变更，跳过编译。"
    return 1
}

function update_proto_timestamp() {
    local timestamp_file="$PROTO_BASE_DIR/.proto_timestamp"
    log_info "更新时间戳: $timestamp_file"
    date > "$timestamp_file"
}


# --- Main Function ---
function main() {
    log_info "开始Proto同步与构建流程..."
    log_warning "重要: 请确保在运行此脚本前，已激活目标服务所需的环境 (如 Conda, NVM)。"
    
    BUILD_PYTHON="false"
    BUILD_GO="false"
    BUILD_JAVA="false"
    BUILD_ELECTRON="false"
    FORCE_REBUILD="false"
    CLEAN_PEX_CACHE="false"
    PROTO_ONLY="false"
    
    if [[ $# -eq 0 ]]; then
        log_info "未指定特定服务，默认编译所有服务。"
        log_info "清除dist目录..."
        rm -rf "$PROJECT_ROOT/dist" # 清除旧的构建文件
        BUILD_PYTHON="true"; BUILD_GO="true"; BUILD_JAVA="true"; BUILD_ELECTRON="true"
    fi

    while [[ $# -gt 0 ]]; do
        case $1 in
            --python) BUILD_PYTHON="true"; shift ;;
            --go) BUILD_GO="true"; shift ;;
            --java) BUILD_JAVA="true"; shift ;;
            --electron) BUILD_ELECTRON="true"; shift ;;
            --all) BUILD_PYTHON="true"; BUILD_GO="true"; BUILD_JAVA="true"; BUILD_ELECTRON="true"; shift ;;
            --force) FORCE_REBUILD="true"; shift ;;
            --proto-only) PROTO_ONLY="true"; shift ;;
            --clean-pex-cache) CLEAN_PEX_CACHE="true"; shift ;;
            --help) show_usage ;;
            *) log_error "未知参数: $1"; show_usage ;;
        esac
    done

    # If --force is used without specific services, assume all services
    if [[ "$FORCE_REBUILD" == "true" ]] && \
       [[ "$BUILD_PYTHON" != "true" ]] && \
       [[ "$BUILD_GO" != "true" ]] && \
       [[ "$BUILD_JAVA" != "true" ]] && \
       [[ "$BUILD_ELECTRON" != "true" ]]; then
        log_info "--force指定，但未指定特定服务。默认编译所有服务。"
        BUILD_PYTHON="true"; BUILD_GO="true"; BUILD_JAVA="true"; BUILD_ELECTRON="true"
        log_info "清除dist目录 (因为 --force 暗示全部重新构建)..."
        rm -rf "$PROJECT_ROOT/dist"
    fi
    
    check_protoc # Basic check for protoc

    # 如果是 proto-only 模式，显示特殊提示
    if [[ "$PROTO_ONLY" == "true" ]]; then
        log_info "🔧 Proto-only 模式：仅生成 protobuf 文件，跳过打包和构建步骤"
    fi

    if ! need_rebuild_check && [[ "$FORCE_REBUILD" != "true" ]] && [[ "$CLEAN_PEX_CACHE" != "true" ]]; then
        log_info "无需执行任何操作。"
        exit 0
    fi

    # 如果指定了清理PEX缓存选项，则执行清理
    if [[ "$CLEAN_PEX_CACHE" == "true" ]]; then
        clean_pex_cache
    fi

    local any_service_built=false
    if [[ "$BUILD_PYTHON" == "true" ]]; then handle_python_service && any_service_built=true; fi
    if [[ "$BUILD_GO" == "true" ]]; then handle_go_service && any_service_built=true; fi
    if [[ "$BUILD_JAVA" == "true" ]]; then handle_java_service && any_service_built=true; fi
    if [[ "$BUILD_ELECTRON" == "true" ]]; then handle_electron_app && any_service_built=true; fi

    if [[ "$any_service_built" == "true" || "$FORCE_REBUILD" == "true" ]]; then
        update_proto_timestamp
        log_success "Proto同步与构建流程处理完毕!"
    else
        log_info "没有服务被构建 (可能是因为它们不是最新的且未指定 --force)。"
    fi
}

main "$@"