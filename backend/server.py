from concurrent import futures
import grpc
import time
import sys
import datetime

# 强制输出不缓冲 - 解决PyCharm输出问题
sys.stdout.reconfigure(line_buffering=True)
sys.stderr.reconfigure(line_buffering=True)

# 设置环境变量来抑制gRPC警告
import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

os.environ['GRPC_VERBOSITY'] = 'ERROR'
os.environ['GRPC_TRACE'] = ''

# 添加即时输出测试
# 早期启动消息，日志系统还未初始化
print("🚀 Python后端服务启动中...", flush=True)

# 导入新的通用日志系统
from utils.universal_logging import setup_universal_logging, get_logger
from interceptors.logging_interceptor import create_logging_interceptors
from interceptors.validation_interceptor import create_validation_interceptors

# 设置符合通用日志标准的日志系统
# 早期启动消息，日志系统还未初始化
print("📝 正在设置通用日志系统...", flush=True)

# 抑制absl日志警告
import logging
logging.getLogger('absl').setLevel(logging.ERROR)

# 初始化通用日志系统
setup_universal_logging()
# 早期启动消息，日志系统还未初始化
print("✅ 通用日志系统设置完成", flush=True)

# 获取日志器
logger = get_logger("server")
logger.info("日志器获取完成", module="grpc-server", operation="logger_init")

# 导入新生成的 gRPC Python 代码
# Greeter service
from api_protos.v1.greeter import greeter_pb2
from api_protos.v1.greeter import greeter_pb2_grpc
# Subtitler service (needed for add_SubtitlerServicer_to_server)
from api_protos.v1.subtitler import subtitler_pb2, subtitler_pb2_grpc
from api_protos.v1.ai_config import ai_config_service_pb2, ai_config_service_pb2_grpc
# Health check service
from grpc_health.v1 import health, health_pb2_grpc, health_pb2
from grpc_reflection.v1alpha import reflection

# 导入新的服务实现
from subtitle_service import SubtitleServiceImpl
from ai_config_service import AIConfigurationServiceServicer
from subtitle.error_handler import handle_grpc_errors

class GreeterServicer(greeter_pb2_grpc.GreeterServicer): # 修改基类
    @handle_grpc_errors
    def SayHello(self, request, context): # 方法签名不变
        logger.info(f"收到SayHello请求", extra={'request_name': request.name, 'operation': 'SayHello'})
        
        original_name = request.name
        server_id_str = "Python Server" 
        current_timestamp_str = datetime.datetime.now().isoformat()

        # 您可以自定义问候消息的格式
        response_message_str = f"Hello {original_name} from {server_id_str}!"
       
        # 使用新的 greeter_pb2 来创建 Reply 对象
        reply = greeter_pb2.HelloReply(
            message=response_message_str,
            original_request=original_name,
            server_id=server_id_str,
            timestamp=current_timestamp_str
        )
        logger.info(f"准备响应", extra={
            'operation': 'SayHello',
            'response_message': reply.message,
            'original_request': reply.original_request,
            'server_id': reply.server_id,
            'timestamp': reply.timestamp
        })
        return reply

def serve():
    logger.info("创建gRPC服务器", module="grpc-server", operation="server_create")
    
    # 创建拦截器链 - 重新启用验证拦截器
    logging_interceptors = create_logging_interceptors()
    validation_interceptors = create_validation_interceptors()
    
    # 合并拦截器 - 验证拦截器先执行，然后是日志拦截器
    interceptors = validation_interceptors + logging_interceptors
    
    logger.info(
        "拦截器链设置完成",
        module="grpc-server",
        operation="interceptors_setup",
        interceptor_count=len(interceptors),
        interceptor_types=[type(i).__name__ for i in interceptors]
    )
    
    # 创建带有拦截器和优化配置的gRPC服务器
    options = [
        ('grpc.keepalive_time_ms', 300000),  # 5 minutes
        ('grpc.keepalive_timeout_ms', 60000),  # 1 minute
        ('grpc.keepalive_permit_without_calls', False),
        ('grpc.http2.min_time_between_pings_ms', 300000),  # 5 minutes
        ('grpc.http2.max_pings_without_data', 3),
        ('grpc.http2.min_ping_interval_without_data_ms', 300000),  # 5 minutes
        ('grpc.max_connection_idle_ms', 3600000),  # 1 hour
        ('grpc.max_message_length', 100 * 1024 * 1024),  # 100MB
        ('grpc.max_receive_message_length', 100 * 1024 * 1024),  # 100MB
        ('grpc.max_send_message_length', 100 * 1024 * 1024),  # 100MB
    ]
    
    server = grpc.server(
        futures.ThreadPoolExecutor(max_workers=10),
        interceptors=interceptors,
        options=options
    )
    
    logger.info("注册gRPC服务", module="grpc-server", operation="service_register")
    # 使用新的 greeter_pb2_grpc 来注册 Greeter 服务
    greeter_pb2_grpc.add_GreeterServicer_to_server(GreeterServicer(), server)
    # 注册 Subtitler 服务
    subtitle_service_impl = SubtitleServiceImpl()
    logger.debug("SubtitleServiceImpl实例创建成功", module="grpc-server", service="SubtitleService")
    subtitler_pb2_grpc.add_SubtitlerServicer_to_server(subtitle_service_impl, server)
    logger.info("Subtitler服务注册完成", module="grpc-server", service="SubtitleService")
    ai_config_service_pb2_grpc.add_AIConfigurationServiceServicer_to_server(AIConfigurationServiceServicer(), server)
    logger.info("AI配置服务注册完成", module="grpc-server", service="AIConfigService")

    # 注册健康检查服务
    health_servicer = health.HealthServicer()
    health_pb2_grpc.add_HealthServicer_to_server(health_servicer, server)
    # 设置服务健康状态
    health_servicer.set("", health_pb2.HealthCheckResponse.SERVING)
    health_servicer.set("monkeyfx.api.v1.subtitler.Subtitler", health_pb2.HealthCheckResponse.SERVING)
    health_servicer.set("monkeyfx.api.v1.ai_config.AIConfigurationService", health_pb2.HealthCheckResponse.SERVING)
    logger.info("💚 健康检查服务已启用", module="grpc-server", service="HealthService")

    # 注册 gRPC 反射服务
    service_names = (
        subtitler_pb2.DESCRIPTOR.services_by_name['Subtitler'].full_name,
        ai_config_service_pb2.DESCRIPTOR.services_by_name['AIConfigurationService'].full_name,
        greeter_pb2.DESCRIPTOR.services_by_name['Greeter'].full_name,
        reflection.SERVICE_NAME,
        health.SERVICE_NAME,
    )
    reflection.enable_server_reflection(service_names, server)
    logger.info("🔍 gRPC反射已启用", module="grpc-server", service="ReflectionService")

    port = '50051'
    server.add_insecure_port(f'[::]:{port}')
    logger.info("服务器绑定端口", module="grpc-server", port=port, operation="port_bind")
    
    logger.info(f"gRPC服务器启动中", extra={'operation': 'server_start', 'port': port})
    logger.info("启动gRPC服务器", module="grpc-server", port=port, operation="server_start")
    server.start()
    logger.info(f"gRPC服务器已启动并监听", extra={'operation': 'server_started', 'port': port})
    logger.info("gRPC服务器启动成功", module="grpc-server", port=port, status="listening")
    
    try:
        while True:
            time.sleep(86400)  # 一天
    except KeyboardInterrupt:
        logger.info("gRPC服务器停止中", extra={'operation': 'server_stop'})
        server.stop(0)
        logger.info("gRPC服务器已停止", extra={'operation': 'server_stopped'})

if __name__ == '__main__':
    serve()