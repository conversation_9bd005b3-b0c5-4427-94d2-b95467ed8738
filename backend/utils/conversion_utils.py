import base64
from google.protobuf import descriptor as _descriptor
from enum import Enum
from typing import Type, TypeVar, Any as Typing<PERSON>ny, Dict as TypingDict, get_origin, Union 
from google.protobuf.message import Message
from google.protobuf.json_format import MessageToDict, ParseDict, ParseError
from google.protobuf.any_pb2 import Any as ProtoAny
from google.protobuf.struct_pb2 import Struct as ProtoStruct
from pydantic import BaseModel, ValidationError
# Potentially: from datetime import datetime
# Potentially: from google.protobuf.timestamp_pb2 import Timestamp

class ProtoConversionError(Exception):
    """Base class for conversion errors."""
    def __init__(self, message, original_data=None, target_type=None, errors=None, details: str = None):
        super().__init__(message)
        self.original_data = original_data
        self.target_type = target_type
        self.errors = errors
        self.details = details

class ProtoToDtoConversionError(ProtoConversionError): pass
class DtoToProtoConversionError(ProtoConversionError): pass

BaseModelT = TypeVar('BaseModelT', bound=BaseModel)

def _safe_message_to_dict(proto_message: Message, **kwargs) -> dict:
    """
    安全的 MessageToDict 包装函数，兼容不同版本的 protobuf 库
    """
    try:
        return MessageToDict(proto_message, **kwargs)
    except TypeError as e:
        # 如果参数不支持，移除不支持的参数重试
        if 'always_print_fields_with_no_presence' in str(e):
            kwargs_copy = kwargs.copy()
            kwargs_copy.pop('always_print_fields_with_no_presence', None)
            return MessageToDict(proto_message, **kwargs_copy)
        else:
            raise

def _convert_nested_enums(data_dict: dict) -> dict:
    """递归转换嵌套字典中的枚举值 - 优化版本"""
    from data_models.dtos.dto_common import OperationStatusEnum
    
    # Protobuf枚举值到DTO枚举值的映射
    status_mapping = {
        0: OperationStatusEnum.UNSPECIFIED,
        1: OperationStatusEnum.SUCCESS,
        2: OperationStatusEnum.ERROR,
        3: OperationStatusEnum.PARTIAL_SUCCESS,
        4: OperationStatusEnum.IN_PROGRESS,
        5: OperationStatusEnum.SUCCESS_WITH_WARNINGS
    }
    
    result = {}
    for key, value in data_dict.items():
        if key == 'status' and isinstance(value, int):
            # 转换status字段的整数值为枚举字符串值
            result[key] = status_mapping.get(value, OperationStatusEnum.UNSPECIFIED)
        elif isinstance(value, dict):
            result[key] = _convert_nested_enums(value)
        elif isinstance(value, list):
            result[key] = [
                _convert_nested_enums(item) if isinstance(item, dict) else item 
                for item in value
            ]
        else:
            result[key] = value
    return result

def _deep_enum_conversion(data: any, enum_fields_map: dict = None) -> any:
    """
    深度枚举转换 - 通用版本
    
    Args:
        data: 要转换的数据（可以是dict, list, 或其他类型）
        enum_fields_map: 枚举字段映射，格式为 {field_name: {int_value: enum_value}}
    """
    if enum_fields_map is None:
        from data_models.dtos.dto_common import OperationStatusEnum
        # 支持多种枚举值模式
        enum_fields_map = {
            'status': {
                0: OperationStatusEnum.UNSPECIFIED,
                1: OperationStatusEnum.SUCCESS,
                2: OperationStatusEnum.ERROR,
                3: OperationStatusEnum.PARTIAL_SUCCESS,
                4: OperationStatusEnum.IN_PROGRESS,
                5: OperationStatusEnum.SUCCESS_WITH_WARNINGS,
                # 添加字符串值支持
                'OPERATION_STATUS_UNSPECIFIED': OperationStatusEnum.UNSPECIFIED,
                'OPERATION_STATUS_SUCCESS': OperationStatusEnum.SUCCESS,
                'OPERATION_STATUS_ERROR': OperationStatusEnum.ERROR,
                'OPERATION_STATUS_PARTIAL_SUCCESS': OperationStatusEnum.PARTIAL_SUCCESS,
                'OPERATION_STATUS_IN_PROGRESS': OperationStatusEnum.IN_PROGRESS,
                'OPERATION_STATUS_SUCCESS_WITH_WARNINGS': OperationStatusEnum.SUCCESS_WITH_WARNINGS
            }
        }
    
    if isinstance(data, dict):
        from data_models.dtos.dto_common import OperationStatusEnum
        result = {}
        for key, value in data.items():
            if key in enum_fields_map and (isinstance(value, int) or isinstance(value, str)):
                # 转换枚举字段，支持整数和字符串
                result[key] = enum_fields_map[key].get(
                    value, 
                    OperationStatusEnum.UNSPECIFIED  # 明确的默认值
                )
            else:
                # 递归处理嵌套数据
                result[key] = _deep_enum_conversion(value, enum_fields_map)
        
        # 为缺失的status字段提供默认值
        if 'status' not in result and 'status' in enum_fields_map:
            result['status'] = OperationStatusEnum.UNSPECIFIED
            
        return result
    elif isinstance(data, list):
        return [_deep_enum_conversion(item, enum_fields_map) for item in data]
    else:
        return data

def convert_proto_to_dto(proto_message: Message, dto_class: Type[BaseModelT]) -> BaseModelT:
    """
    Converts a Protobuf message to a Pydantic DTO.
    Handles potential aliasing in DTOs where DTO field name might differ from Proto field name.
    """
    try:
        # Add debug logging for AI configuration conversion
        if dto_class.__name__ == 'UpdateAIConfigurationsRequestDto':
            print(f"🔍 [DEBUG] convert_proto_to_dto: proto_message type: {type(proto_message)}")
            print(f"🔍 [DEBUG] convert_proto_to_dto: proto_message.configs count: {len(proto_message.configs) if hasattr(proto_message, 'configs') else 'N/A'}")
            if hasattr(proto_message, 'configs') and proto_message.configs:
                first_config = proto_message.configs[0]
                print(f"🔍 [DEBUG] convert_proto_to_dto: first config provider_id: {first_config.provider_id if hasattr(first_config, 'provider_id') else 'N/A'}")
        
        proto_dict_from_message = _safe_message_to_dict(
            proto_message,
            preserving_proto_field_name=True,
            use_integers_for_enums=True, # Ensures enums are ints for Pydantic
            always_print_fields_with_no_presence=True # Include False boolean values
        )
        
        # Add debug logging for AI configuration conversion
        if dto_class.__name__ == 'UpdateAIConfigurationsRequestDto':
            print(f"🔍 [DEBUG] convert_proto_to_dto: proto_dict_from_message: {proto_dict_from_message}")
            print(f"🔍 [DEBUG] convert_proto_to_dto: proto_dict keys: {list(proto_dict_from_message.keys())}")
        
        dict_for_dto_validation = {}

        for dto_field_name, dto_field_model in dto_class.model_fields.items():
            proto_field_name = dto_field_model.alias if dto_field_model.alias else dto_field_name
            
            # Add debug logging for AI configuration conversion
            if dto_class.__name__ == 'UpdateAIConfigurationsRequestDto':
                print(f"🔍 [DEBUG] convert_proto_to_dto: processing DTO field '{dto_field_name}' -> proto field '{proto_field_name}'")
                print(f"🔍 [DEBUG] convert_proto_to_dto: field present in proto_dict: {proto_field_name in proto_dict_from_message}")
            
            if proto_field_name not in proto_dict_from_message:
                continue

            value_from_proto = proto_dict_from_message[proto_field_name]
            processed_value = value_from_proto
            
            # Add debug logging for AI configuration conversion
            if dto_class.__name__ == 'UpdateAIConfigurationsRequestDto':
                print(f"🔍 [DEBUG] convert_proto_to_dto: value_from_proto for '{proto_field_name}': {value_from_proto}")
            
            expected_field_types = []
            is_optional_str_field = False
            annotation_origin = get_origin(dto_field_model.annotation)
            
            if annotation_origin is Union:
                is_str_in_union = False
                is_none_in_union = False
                for arg in getattr(dto_field_model.annotation, '__args__', []):
                    if arg is type(None):
                        is_none_in_union = True
                    else:
                        expected_field_types.append(arg)
                        if arg is str:
                            is_str_in_union = True
                if is_str_in_union and is_none_in_union:
                    is_optional_str_field = True
            elif dto_field_model.annotation is not type(None):
                expected_field_types.append(dto_field_model.annotation)

            is_pydantic_model_expected = any(
                isinstance(eft, type) and issubclass(eft, BaseModel) for eft in expected_field_types
            )

            if is_pydantic_model_expected and \
               hasattr(value_from_proto, 'DESCRIPTOR') and \
               not isinstance(value_from_proto, dict):
                try:
                    processed_value = MessageToDict(
                        value_from_proto,
                        preserving_proto_field_name=True,
                        use_integers_for_enums=True
                    )
                except Exception:
                    pass
            
            # 处理可选字符串字段：空字符串转为None
            if is_optional_str_field and processed_value == "":
                processed_value = None
            
            # 特别处理trace_id字段：确保有效性
            if dto_field_name == 'trace_id' and (not processed_value or processed_value == ""):
                try:
                    from utils.trace_id_manager import ensure_trace_id
                    processed_value = ensure_trace_id(processed_value, f"convert_proto_to_dto-{dto_class.__name__}")
                except ImportError:
                    import uuid
                    processed_value = str(uuid.uuid4())
            
            if isinstance(processed_value, dict) and \
               processed_value.get('@type') == 'type.googleapis.com/google.protobuf.Struct' and \
               'value' in processed_value:
                processed_value = processed_value['value']
            elif any(eft == bytes for eft in expected_field_types) and isinstance(processed_value, str):
                try:
                    processed_value = base64.b64decode(processed_value)
                except Exception:
                    pass
                # Enum handling: Convert integer enum values to enum members
                if any(isinstance(eft, type) and issubclass(eft, Enum) for eft in expected_field_types) and isinstance(processed_value, int):
                    for eft in expected_field_types:
                        if isinstance(eft, type) and issubclass(eft, Enum):
                            try:
                                # Convert integer to enum member by value
                                processed_value = eft(processed_value)
                                break
                            except ValueError:
                                # If conversion fails, try to find by name or keep as int
                                pass
                
                # Handle list/dict recursively for nested enum conversions
                # 使用新的深度枚举转换机制
                processed_value = _deep_enum_conversion(processed_value)

            dict_for_dto_validation[dto_field_name] = processed_value
        
        # Add debug logging for AI configuration conversion
        if dto_class.__name__ == 'UpdateAIConfigurationsRequestDto':
            print(f"🔍 [DEBUG] convert_proto_to_dto: final dict_for_dto_validation: {dict_for_dto_validation}")
        
        dto_instance = dto_class.model_validate(dict_for_dto_validation)
        
        # Add debug logging for AI configuration conversion
        if dto_class.__name__ == 'UpdateAIConfigurationsRequestDto':
            print(f"🔍 [DEBUG] convert_proto_to_dto: final DTO configs count: {len(dto_instance.configs) if hasattr(dto_instance, 'configs') else 'N/A'}")
        
        return dto_instance

    except ValidationError as e:
        original_data_for_error = "Could not serialize proto_message for error"
        try:
            original_data_for_error = MessageToDict(proto_message, preserving_proto_field_name=True) if proto_message else None
        except: 
            pass 
        raise ProtoToDtoConversionError(
            f"Error converting Proto message {type(proto_message).__name__} to DTO {dto_class.__name__}",
            original_data=original_data_for_error,
            target_type=dto_class,
            errors=e.errors(), 
            details=str(e)
        ) from e 
    except Exception as e:
        raise ProtoToDtoConversionError(
            f"Unexpected error converting Proto message {type(proto_message).__name__} to DTO {dto_class.__name__}: {str(e)}",
            original_data=str(proto_message), 
            target_type=dto_class,
            details=str(e)
        ) from e

MessageT = TypeVar('MessageT', bound=Message)

def convert_dto_to_proto(dto_instance: BaseModel, proto_class: Type[MessageT]) -> MessageT:
    """
    Converts a Pydantic DTO to a Protobuf message.
    Uses by_alias=True in model_dump, so keys in the dumped dict are proto field names.
    """
    try:
        # 重要修复：使用mode='json'确保枚举被正确序列化为字符串
        dto_dict_for_proto = dto_instance.model_dump(by_alias=True, exclude_none=True, mode='json')
        proto_message = proto_class()
        prepared_any_fields: TypingDict[str, ProtoAny] = {}
        final_dict_for_parse_dict = {}

        # 🔧 修复：智能处理data字段与oneof字段的冲突
        # 检查ProgressUpdate类型的特殊情况
        has_oneof_field_data = False
        has_data_field = 'data' in dto_dict_for_proto and dto_dict_for_proto['data'] is not None
        oneof_fields_to_exclude = set()
        
        if hasattr(proto_class, 'DESCRIPTOR') and 'final_result' in [f.name for f in proto_class.DESCRIPTOR.oneofs]:
            # 这是ProgressUpdate类型，检查是否有oneof字段的数据
            potential_oneof_fields = {
                'video_to_audio_response',
                'audio_to_text_response', 
                'generate_subtitles_response',
                'translate_subtitles_response',
                'process_video_to_translated_subtitles_response'
            }
            
            for field_name in potential_oneof_fields:
                if field_name in dto_dict_for_proto and dto_dict_for_proto[field_name] is not None:
                    has_oneof_field_data = True
                    oneof_fields_to_exclude.add(field_name)
                    print(f"🔧 [convert_dto_to_proto] 发现oneof字段数据: {field_name}", flush=True)
            
            # 🔧 关键修复：如果同时有data字段和oneof字段数据，优先使用oneof字段，排除data字段
            if has_data_field and has_oneof_field_data:
                print(f"🔧 [convert_dto_to_proto] 检测到data字段与oneof字段冲突，优先使用oneof字段，排除data字段", flush=True)
                # 从ParseDict处理中排除data字段，避免冲突
                final_dict_for_parse_dict_copy = dto_dict_for_proto.copy()
                final_dict_for_parse_dict_copy.pop('data', None)
                dto_dict_for_proto = final_dict_for_parse_dict_copy
            elif has_data_field and not has_oneof_field_data:
                print(f"🔧 [convert_dto_to_proto] 只有data字段，无oneof字段冲突", flush=True)
            elif has_oneof_field_data and not has_data_field:
                print(f"🔧 [convert_dto_to_proto] 只有oneof字段，无data字段冲突", flush=True)

        for proto_field_name, value_from_dto in dto_dict_for_proto.items():
            # 🔧 修复：如果这是oneof字段，跳过ParseDict处理，让_handle_oneof_fields专门处理
            if proto_field_name in oneof_fields_to_exclude:
                print(f"🔧 [convert_dto_to_proto] 跳过oneof字段 {proto_field_name}，将由_handle_oneof_fields处理", flush=True)
                continue
                
            if proto_field_name not in proto_class.DESCRIPTOR.fields_by_name:
                final_dict_for_parse_dict[proto_field_name] = value_from_dto
                continue

            field_descriptor = proto_class.DESCRIPTOR.fields_by_name[proto_field_name]

            if field_descriptor.message_type and field_descriptor.message_type.full_name == ProtoAny.DESCRIPTOR.full_name:
                if value_from_dto is not None:
                    if not isinstance(value_from_dto, dict):
                        raise DtoToProtoConversionError(
                            f"DTO field '{proto_field_name}' (for Proto Any) must be a dictionary representing a Struct.",
                            original_data=dto_instance.model_dump(), target_type=proto_class
                        )
                    struct_payload = ProtoStruct()
                    ParseDict(value_from_dto, struct_payload)
                    any_message = ProtoAny()
                    any_message.Pack(struct_payload)
                    prepared_any_fields[proto_field_name] = any_message
            elif field_descriptor.type == _descriptor.FieldDescriptor.TYPE_BYTES:
                if isinstance(value_from_dto, bytes):
                    final_dict_for_parse_dict[proto_field_name] = base64.b64encode(value_from_dto).decode('utf-8')
                elif value_from_dto is not None:
                    final_dict_for_parse_dict[proto_field_name] = value_from_dto
            
            elif field_descriptor.type == _descriptor.FieldDescriptor.TYPE_MESSAGE and \
                 hasattr(field_descriptor.message_type, 'py_class') and \
                 field_descriptor.message_type.py_class is not None and \
                 isinstance(value_from_dto, dict) and \
                 not (field_descriptor.message_type.full_name == ProtoStruct.DESCRIPTOR.full_name or \
                      field_descriptor.message_type.full_name == ProtoAny.DESCRIPTOR.full_name or \
                      (hasattr(field_descriptor.message_type, 'GetOptions') and field_descriptor.message_type.GetOptions().map_entry)):

                nested_proto_class = field_descriptor.message_type.py_class
                nested_dto_class_annotation = None
                original_dto_field = None
                
                for model_field_name_iter, model_field_iter in dto_instance.__class__.model_fields.items():
                    if (model_field_iter.alias or model_field_name_iter) == proto_field_name:
                        original_dto_field = model_field_iter
                        break
                
                if original_dto_field:
                    annotation_to_check = original_dto_field.annotation
                    origin_type = get_origin(annotation_to_check)
                    if origin_type is Union: 
                        for arg_type in getattr(annotation_to_check, '__args__', []):
                            if isinstance(arg_type, type) and issubclass(arg_type, BaseModel):
                                nested_dto_class_annotation = arg_type
                                break
                    elif isinstance(annotation_to_check, type) and issubclass(annotation_to_check, BaseModel):
                        nested_dto_class_annotation = annotation_to_check
                
                if nested_dto_class_annotation and nested_proto_class:
                    try:
                        nested_dto_instance = nested_dto_class_annotation.model_validate(value_from_dto)
                        nested_proto_instance = convert_dto_to_proto(nested_dto_instance, nested_proto_class)
                        setattr(proto_message, proto_field_name, nested_proto_instance)
                        continue 
                    except Exception as e_recursive:
                        # If recursive conversion fails, do not add the problematic dict to final_dict_for_parse_dict.
                        # This field will be effectively unset or default in the resulting proto message.
                        # import logging # Placeholder for actual logging
                        # logging.warning(f"Recursive conversion for field '{proto_field_name}' failed: {e_recursive}. Field will be unset/default.")
                        pass # Do not add to final_dict_for_parse_dict
                        continue # Continue to the next field
                else: 
                    final_dict_for_parse_dict[proto_field_name] = value_from_dto
            else: 
                final_dict_for_parse_dict[proto_field_name] = value_from_dto
        
        if final_dict_for_parse_dict: 
            ParseDict(final_dict_for_parse_dict, proto_message, ignore_unknown_fields=True)

        for field_name, any_value in prepared_any_fields.items():
            setattr(proto_message, field_name, any_value)

        # 🔧 修复：特殊处理 oneof 字段 (如 ProgressUpdate 的 final_result)
        # 检查DTO是否有对应oneof字段的子字段，如果有，需要单独设置
        _handle_oneof_fields(dto_instance, proto_message, proto_class)

        return proto_message

    except ParseError as e:
        raise DtoToProtoConversionError(
            f"Error parsing dictionary to Proto message {proto_class.__name__} during DTO to Proto conversion.",
            original_data=dto_instance.model_dump(),
            target_type=proto_class,
            details=str(e)
        ) from e 
    except Exception as e:
        processed_dto_dict_str = "Could not serialize processed DTO dictionary for error"
        dict_snapshot = {} 
        try: 
            if 'dto_dict_for_proto' in locals():
                dict_snapshot = dto_dict_for_proto
            if 'final_dict_for_parse_dict' in locals(): 
                 dict_snapshot = final_dict_for_parse_dict
            processed_dto_dict_str = str(dict_snapshot)
        except: 
            pass

        raise DtoToProtoConversionError(
            f"Unexpected error converting DTO {type(dto_instance).__name__} to Proto message {proto_class.__name__}: {str(e)}. "
            f"Processed DTO dictionary snapshot: {processed_dto_dict_str}",
            original_data=dto_instance.model_dump(),
            target_type=proto_class,
            details=str(e)
        ) from e

def _handle_oneof_fields(dto_instance: BaseModel, proto_message: Message, proto_class: Type[MessageT]):
    """
    处理protobuf oneof字段的特殊设置逻辑
    
    Args:
        dto_instance: Pydantic DTO实例
        proto_message: 要设置字段的protobuf消息实例
        proto_class: protobuf消息类
    """
    try:
        # 获取DTO的字段数据
        dto_dict = dto_instance.model_dump(by_alias=True, exclude_none=True, mode='json')
        
        # 检查是否是ProgressUpdate类型，特殊处理final_result oneof字段
        if hasattr(proto_class, 'DESCRIPTOR') and 'final_result' in [f.name for f in proto_class.DESCRIPTOR.oneofs]:
            # 这是一个有final_result oneof的protobuf类
            oneof_fields = {
                'video_to_audio_response': 'VideoToAudioResponse',
                'audio_to_text_response': 'AudioToTextResponse', 
                'generate_subtitles_response': 'GenerateSubtitlesResponse',
                'translate_subtitles_response': 'TranslateSubtitlesResponse',
                'process_video_to_translated_subtitles_response': 'ProcessVideoToTranslatedSubtitlesResponse'
            }
            
            # 检查DTO中是否有这些oneof字段的任何一个
            for field_name, response_type in oneof_fields.items():
                field_value = dto_dict.get(field_name)
                if field_value is not None:
                    # 获取字段描述符
                    if field_name in proto_class.DESCRIPTOR.fields_by_name:
                        field_descriptor = proto_class.DESCRIPTOR.fields_by_name[field_name]
                        
                        if hasattr(field_descriptor.message_type, 'py_class'):
                            nested_proto_class = field_descriptor.message_type.py_class
                            
                            # 查找对应的DTO类
                            nested_dto_class = None
                            for model_field_name, model_field in dto_instance.__class__.model_fields.items():
                                if (model_field.alias or model_field_name) == field_name:
                                    annotation = model_field.annotation
                                    origin_type = get_origin(annotation)
                                    if origin_type is Union:
                                        for arg_type in getattr(annotation, '__args__', []):
                                            if isinstance(arg_type, type) and issubclass(arg_type, BaseModel):
                                                nested_dto_class = arg_type
                                                break
                                    elif isinstance(annotation, type) and issubclass(annotation, BaseModel):
                                        nested_dto_class = annotation
                                    break
                            
                            if nested_dto_class and nested_proto_class:
                                try:
                                    # 创建嵌套的DTO实例
                                    nested_dto_instance = nested_dto_class.model_validate(field_value)
                                    
                                    # 递归转换为protobuf
                                    nested_proto_instance = convert_dto_to_proto(nested_dto_instance, nested_proto_class)
                                    
                                    # 设置oneof字段
                                    setattr(proto_message, field_name, nested_proto_instance)
                                    
                                    # oneof字段只能设置一个，找到第一个就退出
                                    break
                                except Exception as e:
                                    # 记录错误但继续尝试其他字段
                                    print(f"⚠️ [_handle_oneof_fields] 设置oneof字段失败: {field_name}, 错误: {e}", flush=True)
                                    continue
    except Exception as e:
        # 记录错误但不影响主要转换流程
        print(f"⚠️ [_handle_oneof_fields] 处理oneof字段时发生异常: {e}", flush=True)