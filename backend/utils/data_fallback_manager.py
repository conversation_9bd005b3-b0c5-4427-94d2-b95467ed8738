"""
数据回退管理器

提供统一的数据回退机制，处理部分失败场景，确保系统在出现错误时能够提供有效的回退数据。
"""

import logging
# 导入新的统一日志系统
from utils.universal_logging import get_logger, ensure_trace_id, LogContext, set_trace_id
import time
from typing import Dict, Any, Optional, List, Union, Callable
from enum import Enum
from dataclasses import dataclass

from data_models.dtos.dto_common import OperationStatusEnum, ErrorDetailDto
from data_models.dtos.subtitler_dtos import (
    BackendVideoMetadata,
    BackendKeyframeExtractionResult,
    BackendAudioExtractionResult,
    BackendProgressUpdate,
    BackendStageSpecificErrorDetail
)

logger = get_logger(__name__)


class FallbackStrategy(Enum):
    """回退策略枚举"""
    NONE = "none"
    DEFAULT_VALUES = "default_values"
    CACHED_DATA = "cached_data"
    PARTIAL_SUCCESS = "partial_success"
    ESTIMATED_VALUES = "estimated_values"


@dataclass
class FallbackConfig:
    """回退配置"""
    strategy: FallbackStrategy
    timeout_seconds: float = 30.0
    retry_attempts: int = 3
    use_cache: bool = True
    provide_estimates: bool = True
    allow_partial_results: bool = True


class DataFallbackManager:
    """数据回退管理器"""
    
    def __init__(self):
        self.fallback_configs: Dict[str, FallbackConfig] = {
            'video_metadata': FallbackConfig(
                strategy=FallbackStrategy.ESTIMATED_VALUES,
                timeout_seconds=15.0,
                provide_estimates=True
            ),
            'keyframe_extraction': FallbackConfig(
                strategy=FallbackStrategy.PARTIAL_SUCCESS,
                timeout_seconds=30.0,
                allow_partial_results=True
            ),
            'audio_extraction': FallbackConfig(
                strategy=FallbackStrategy.DEFAULT_VALUES,
                timeout_seconds=45.0,
                retry_attempts=2
            ),
            'transcription': FallbackConfig(
                strategy=FallbackStrategy.CACHED_DATA,
                timeout_seconds=120.0,
                use_cache=True
            )
        }
        
        # 缓存数据存储
        self._cache: Dict[str, Dict[str, Any]] = {}
        
        # 默认值定义
        self._default_values = {
            'video_metadata': {
                'duration': 0.0,
                'resolution': "Unknown",
                'codec': "Unknown",
                'bitrate': 0,
                'framerate': 0.0
            },
            'keyframe_extraction': {
                'keyframe_timestamps': [],
                'preview_image_paths': [],
                'extracted_count': 0,
                'target_count': 10
            },
            'audio_extraction': {
                'audio_path': None,
                'duration': 0.0,
                'sample_rate': 44100,
                'channels': 2,
                'format': "wav"
            }
        }
    
    def set_fallback_config(self, stage_name: str, config: FallbackConfig):
        """设置特定阶段的回退配置"""
        self.fallback_configs[stage_name] = config
        logger.info(f"Updated fallback config for {stage_name}: {config}")
    
    def cache_data(self, stage_name: str, key: str, data: Any):
        """缓存数据"""
        if stage_name not in self._cache:
            self._cache[stage_name] = {}
        self._cache[stage_name][key] = {
            'data': data,
            'timestamp': time.time()
        }
        logger.debug(f"Cached data for {stage_name}:{key}")
    
    def get_cached_data(self, stage_name: str, key: str, max_age_seconds: float = 3600) -> Optional[Any]:
        """获取缓存数据"""
        if stage_name not in self._cache or key not in self._cache[stage_name]:
            return None
        
        cached_item = self._cache[stage_name][key]
        age = time.time() - cached_item['timestamp']
        
        if age <= max_age_seconds:
            logger.debug(f"Retrieved cached data for {stage_name}:{key} (age: {age:.1f}s)")
            return cached_item['data']
        else:
            logger.debug(f"Cached data for {stage_name}:{key} expired (age: {age:.1f}s)")
            return None
    
    def create_fallback_video_metadata(
        self, 
        error: Exception, 
        video_path: str,
        partial_data: Optional[Dict[str, Any]] = None
    ) -> BackendVideoMetadata:
        """创建视频元数据的回退版本"""
        config = self.fallback_configs.get('video_metadata', FallbackConfig(FallbackStrategy.DEFAULT_VALUES))
        
        # 尝试从缓存获取
        if config.use_cache:
            cached = self.get_cached_data('video_metadata', video_path)
            if cached:
                        return BackendVideoMetadata(
            **cached,
            error=BackendStageSpecificErrorDetail(
                stage_code="video_analysis",
                message="使用缓存的视频信息",
                details={"source": "cache", "description": "从缓存中恢复视频元数据"}
            ),
            is_fallback_data=True,
            fallback_reason="使用缓存数据"
        )
        
        # 构建回退数据
        fallback_data = self._default_values['video_metadata'].copy()
        
        # 如果有部分数据，使用部分数据
        if partial_data:
            fallback_data.update(partial_data)
        
        # 尝试基于文件进行估算
        if config.provide_estimates:
            try:
                import os
                if os.path.exists(video_path):
                    file_size = os.path.getsize(video_path)
                    # 简单估算：假设平均比特率
                    estimated_duration = max(file_size / (1024 * 1024), 1.0)  # 粗略估算
                    fallback_data['duration'] = estimated_duration
                    fallback_data['resolution'] = "估算值"
            except Exception as estimate_error:
                logger.warning(f"Failed to estimate video metadata: {estimate_error}")
        
        return BackendVideoMetadata(
            **fallback_data,
            error=BackendStageSpecificErrorDetail(
                stage_code="video_analysis",
                message="无法获取完整视频信息，使用默认值",
                details={"original_error": str(error), "fallback_strategy": "default_values"}
            ),
            is_fallback_data=True,
            fallback_reason=f"原始错误: {error}"
        )
    
    def create_fallback_keyframe_result(
        self, 
        error: Exception, 
        video_path: str,
        target_count: int = 10,
        partial_keyframes: Optional[List[float]] = None
    ) -> BackendKeyframeExtractionResult:
        """创建关键帧提取的回退版本"""
        config = self.fallback_configs.get('keyframe_extraction', FallbackConfig(FallbackStrategy.PARTIAL_SUCCESS))
        
        keyframes = partial_keyframes or []
        extracted_count = len(keyframes)
        
        fallback_reason = "关键帧提取失败"
        if partial_keyframes:
            fallback_reason = f"部分成功，获取了 {extracted_count}/{target_count} 个关键帧"
        
        return BackendKeyframeExtractionResult(
            keyframe_timestamps=keyframes,
            preview_image_paths=[],  # 暂时无法生成预览图
            extracted_count=extracted_count,
            target_count=target_count,
            error=BackendStageSpecificErrorDetail(
                stage_code="keyframe_extraction",
                message=fallback_reason,
                details={"original_error": str(error), "extracted_count": extracted_count, "target_count": target_count}
            ),
            is_fallback_data=True,
            fallback_reason=fallback_reason
        )
    
    def create_fallback_audio_result(
        self, 
        error: Exception, 
        video_path: str,
        partial_audio_path: Optional[str] = None
    ) -> BackendAudioExtractionResult:
        """创建音频提取的回退版本"""
        config = self.fallback_configs.get('audio_extraction', FallbackConfig(FallbackStrategy.DEFAULT_VALUES))
        
        fallback_data = self._default_values['audio_extraction'].copy()
        
        if partial_audio_path:
            fallback_data['audio_path'] = partial_audio_path
            fallback_reason = "音频提取部分成功"
        else:
            fallback_reason = "音频提取失败，无法继续"
        
        return BackendAudioExtractionResult(
            **fallback_data,
            error=BackendStageSpecificErrorDetail(
                stage_code="audio_extraction",
                message=fallback_reason,
                details={"original_error": str(error), "strategy": "fallback_values"}
            ),
            is_fallback_data=True,
            fallback_reason=fallback_reason
        )
    
    def create_enhanced_progress_update(
        self,
        stage_name: str,
        percentage: int,
        message: str,
        trace_id: str,
        error: Optional[Exception] = None,
        fallback_data: Optional[Union[
            BackendVideoMetadata,
            BackendKeyframeExtractionResult,
            BackendAudioExtractionResult
        ]] = None,
        status: OperationStatusEnum = OperationStatusEnum.IN_PROGRESS
    ) -> BackendProgressUpdate:
        """创建增强的进度更新，包含回退数据支持"""
        
        # 确定数据状态
        data_status = "COMPLETE"
        if error:
            if fallback_data and fallback_data.is_fallback_data:
                data_status = "PARTIAL_SUCCESS_WITH_FALLBACK"
            else:
                data_status = "STAGE_ERROR"
        
        # 构建进度更新
        return BackendProgressUpdate(
            trace_id=trace_id,
            stage_name=stage_name,
            percentage=percentage,
            message=message,
            status=status,
            error_detail=ErrorDetailDto(
                error_code="STAGE_ERROR" if error else "SUCCESS",
                technical_message=str(error) if error else "",
                user_message=message,
                context={"stage": stage_name}
            ) if error else None,
            # 注意：移除了 data 字段，现在使用 final_result
            final_result=fallback_data,
            final_result_type=type(fallback_data).__name__ if fallback_data else None,
            data_status=data_status
        )
    
    def should_use_fallback(self, stage_name: str, error: Exception) -> bool:
        """判断是否应该使用回退策略"""
        config = self.fallback_configs.get(stage_name)
        if not config or config.strategy == FallbackStrategy.NONE:
            return False
        
        # 根据错误类型决定是否回退
        error_type = type(error).__name__
        
        # 某些致命错误不应该回退
        fatal_errors = ['PermissionError', 'FileNotFoundError', 'MemoryError']
        if error_type in fatal_errors:
            logger.warning(f"Fatal error {error_type} detected, skipping fallback for {stage_name}")
            return False
        
        return True
    
    def clear_cache(self, stage_name: Optional[str] = None):
        """清理缓存"""
        if stage_name:
            if stage_name in self._cache:
                del self._cache[stage_name]
                logger.info(f"Cleared cache for {stage_name}")
        else:
            self._cache.clear()
            logger.info("Cleared all cache")


# 全局实例
fallback_manager = DataFallbackManager() 