# -*- coding: utf-8 -*-
"""
性能监控模块

提供性能监控装饰器、指标收集和性能分析功能。
"""
import time
import functools
import threading
import psutil
import gc
from typing import Dict, Any, Optional, Callable, List
from collections import defaultdict, deque
from dataclasses import dataclass, field
from datetime import datetime, timedelta
import logging
# 导入新的统一日志系统
from utils.universal_logging import get_logger, ensure_trace_id, LogContext, set_trace_id

from .logging_manager import get_logger, log_performance

logger = get_logger(__name__)


@dataclass
class PerformanceMetric:
    """性能指标数据类"""
    operation: str
    start_time: float
    end_time: float
    duration: float
    success: bool
    memory_before: float
    memory_after: float
    memory_delta: float
    cpu_percent: float
    thread_count: int
    error_message: Optional[str] = None
    extra_data: Dict[str, Any] = field(default_factory=dict)
    
    @property
    def timestamp(self) -> datetime:
        return datetime.fromtimestamp(self.start_time)


class PerformanceCollector:
    """性能指标收集器"""
    
    def __init__(self, max_metrics: int = 1000):
        self.max_metrics = max_metrics
        self.metrics: deque = deque(maxlen=max_metrics)
        self.operation_stats: Dict[str, Dict[str, Any]] = defaultdict(lambda: {
            "count": 0,
            "total_duration": 0.0,
            "avg_duration": 0.0,
            "min_duration": float('inf'),
            "max_duration": 0.0,
            "success_count": 0,
            "error_count": 0,
            "success_rate": 0.0
        })
        self._lock = threading.RLock()
    
    def add_metric(self, metric: PerformanceMetric):
        """添加性能指标"""
        with self._lock:
            self.metrics.append(metric)
            self._update_operation_stats(metric)
    
    def _update_operation_stats(self, metric: PerformanceMetric):
        """更新操作统计"""
        stats = self.operation_stats[metric.operation]
        
        stats["count"] += 1
        stats["total_duration"] += metric.duration
        stats["avg_duration"] = stats["total_duration"] / stats["count"]
        stats["min_duration"] = min(stats["min_duration"], metric.duration)
        stats["max_duration"] = max(stats["max_duration"], metric.duration)
        
        if metric.success:
            stats["success_count"] += 1
        else:
            stats["error_count"] += 1
        
        stats["success_rate"] = stats["success_count"] / stats["count"] * 100
    
    def get_operation_stats(self, operation: str = None) -> Dict[str, Any]:
        """获取操作统计"""
        with self._lock:
            if operation:
                return dict(self.operation_stats.get(operation, {}))
            return {op: dict(stats) for op, stats in self.operation_stats.items()}
    
    def get_recent_metrics(self, minutes: int = 10) -> List[PerformanceMetric]:
        """获取最近的性能指标"""
        cutoff_time = time.time() - (minutes * 60)
        with self._lock:
            return [m for m in self.metrics if m.start_time >= cutoff_time]
    
    def get_slow_operations(self, threshold_seconds: float = 5.0) -> List[PerformanceMetric]:
        """获取慢操作"""
        with self._lock:
            return [m for m in self.metrics if m.duration >= threshold_seconds]
    
    def clear_metrics(self):
        """清空指标"""
        with self._lock:
            self.metrics.clear()
            self.operation_stats.clear()


# 全局性能收集器
_performance_collector = PerformanceCollector()


def get_performance_collector() -> PerformanceCollector:
    """获取全局性能收集器"""
    return _performance_collector


def monitor_performance(
    operation_name: str = None,
    log_performance_data: bool = True,
    memory_threshold_mb: float = 100.0,
    duration_threshold_seconds: float = 5.0,
    include_system_metrics: bool = True
):
    """
    性能监控装饰器
    
    Args:
        operation_name: 操作名称，默认使用函数名
        log_performance_data: 是否记录性能日志
        memory_threshold_mb: 内存增长阈值(MB)，超过时记录警告
        duration_threshold_seconds: 执行时间阈值(秒)，超过时记录警告
        include_system_metrics: 是否包含系统指标
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            operation = operation_name or f"{func.__module__}.{func.__name__}"
            
            # 收集开始时的系统指标
            start_time = time.time()
            memory_before = 0.0
            cpu_percent = 0.0
            thread_count = 0
            
            if include_system_metrics:
                try:
                    process = psutil.Process()
                    memory_before = process.memory_info().rss / 1024 / 1024  # MB
                    cpu_percent = process.cpu_percent()
                    thread_count = process.num_threads()
                except Exception as e:
                    logger.warning(f"获取系统指标失败: {e}")
            
            # 执行函数
            success = True
            error_message = None
            result = None
            
            try:
                logger.debug(f"开始执行操作: {operation}")
                result = func(*args, **kwargs)
                logger.debug(f"操作执行成功: {operation}")
                return result
            except Exception as e:
                success = False
                error_message = str(e)
                logger.error(f"操作执行失败: {operation} - {error_message}")
                raise
            finally:
                # 收集结束时的指标
                end_time = time.time()
                duration = end_time - start_time
                
                memory_after = 0.0
                if include_system_metrics:
                    try:
                        process = psutil.Process()
                        memory_after = process.memory_info().rss / 1024 / 1024  # MB
                    except Exception:
                        pass
                
                memory_delta = memory_after - memory_before
                
                # 创建性能指标
                metric = PerformanceMetric(
                    operation=operation,
                    start_time=start_time,
                    end_time=end_time,
                    duration=duration,
                    success=success,
                    memory_before=memory_before,
                    memory_after=memory_after,
                    memory_delta=memory_delta,
                    cpu_percent=cpu_percent,
                    thread_count=thread_count,
                    error_message=error_message,
                    extra_data={
                        "args_count": len(args),
                        "kwargs_keys": list(kwargs.keys())
                    }
                )
                
                # 添加到收集器
                _performance_collector.add_metric(metric)
                
                # 记录性能日志
                if log_performance_data:
                    log_performance(
                        operation=operation,
                        duration=duration,
                        success=success,
                        memory_delta=memory_delta,
                        cpu_percent=cpu_percent,
                        thread_count=thread_count
                    )
                
                # 检查性能警告
                warnings = []
                if duration >= duration_threshold_seconds:
                    warnings.append(f"执行时间过长: {duration:.2f}s")
                
                if memory_delta >= memory_threshold_mb:
                    warnings.append(f"内存增长过多: {memory_delta:.2f}MB")
                
                if warnings:
                    logger.warning(f"性能警告 - {operation}: {'; '.join(warnings)}", extra={
                        "operation": operation,
                        "duration": duration,
                        "memory_delta": memory_delta,
                        "performance_warning": True
                    })
        
        return wrapper
    return decorator


def monitor_memory_usage(
    operation_name: str = None,
    threshold_mb: float = 50.0,
    force_gc: bool = False
):
    """
    内存使用监控装饰器
    
    Args:
        operation_name: 操作名称
        threshold_mb: 内存增长阈值(MB)
        force_gc: 是否强制垃圾回收
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            operation = operation_name or f"{func.__module__}.{func.__name__}"
            
            # 获取开始时的内存使用
            if force_gc:
                gc.collect()
            
            try:
                process = psutil.Process()
                memory_before = process.memory_info().rss / 1024 / 1024  # MB
            except Exception:
                memory_before = 0.0
            
            try:
                result = func(*args, **kwargs)
                return result
            finally:
                # 获取结束时的内存使用
                try:
                    process = psutil.Process()
                    memory_after = process.memory_info().rss / 1024 / 1024  # MB
                    memory_delta = memory_after - memory_before
                    
                    if memory_delta >= threshold_mb:
                        logger.warning(f"内存使用警告 - {operation}: 增长 {memory_delta:.2f}MB", extra={
                            "operation": operation,
                            "memory_before": memory_before,
                            "memory_after": memory_after,
                            "memory_delta": memory_delta,
                            "memory_warning": True
                        })
                    
                    if force_gc and memory_delta > 0:
                        gc.collect()
                        
                except Exception as e:
                    logger.error(f"内存监控失败: {e}")
        
        return wrapper
    return decorator


class PerformanceAnalyzer:
    """性能分析器"""
    
    def __init__(self, collector: PerformanceCollector = None):
        self.collector = collector or get_performance_collector()
    
    def analyze_performance(self, hours: int = 1) -> Dict[str, Any]:
        """分析性能数据"""
        cutoff_time = time.time() - (hours * 3600)
        recent_metrics = [m for m in self.collector.metrics if m.start_time >= cutoff_time]
        
        if not recent_metrics:
            return {"message": "没有性能数据", "metrics_count": 0}
        
        analysis = {
            "time_range": f"最近 {hours} 小时",
            "total_operations": len(recent_metrics),
            "unique_operations": len(set(m.operation for m in recent_metrics)),
            "success_rate": sum(1 for m in recent_metrics if m.success) / len(recent_metrics) * 100,
            "avg_duration": sum(m.duration for m in recent_metrics) / len(recent_metrics),
            "total_memory_delta": sum(m.memory_delta for m in recent_metrics),
            "slow_operations": [],
            "memory_intensive_operations": [],
            "error_operations": [],
            "top_operations": []
        }
        
        # 慢操作
        slow_ops = [m for m in recent_metrics if m.duration >= 5.0]
        analysis["slow_operations"] = [
            {
                "operation": m.operation,
                "duration": m.duration,
                "timestamp": m.timestamp.isoformat()
            }
            for m in sorted(slow_ops, key=lambda x: x.duration, reverse=True)[:10]
        ]
        
        # 内存密集操作
        memory_ops = [m for m in recent_metrics if m.memory_delta >= 50.0]
        analysis["memory_intensive_operations"] = [
            {
                "operation": m.operation,
                "memory_delta": m.memory_delta,
                "timestamp": m.timestamp.isoformat()
            }
            for m in sorted(memory_ops, key=lambda x: x.memory_delta, reverse=True)[:10]
        ]
        
        # 错误操作
        error_ops = [m for m in recent_metrics if not m.success]
        analysis["error_operations"] = [
            {
                "operation": m.operation,
                "error_message": m.error_message,
                "timestamp": m.timestamp.isoformat()
            }
            for m in error_ops[-10:]  # 最近10个错误
        ]
        
        # 热门操作
        operation_counts = defaultdict(int)
        for m in recent_metrics:
            operation_counts[m.operation] += 1
        
        analysis["top_operations"] = [
            {"operation": op, "count": count}
            for op, count in sorted(operation_counts.items(), key=lambda x: x[1], reverse=True)[:10]
        ]
        
        return analysis
    
    def generate_performance_report(self) -> str:
        """生成性能报告"""
        analysis = self.analyze_performance()
        
        report = f"""
# 性能分析报告

## 概览
- 时间范围: {analysis.get('time_range', 'N/A')}
- 总操作数: {analysis.get('total_operations', 0)}
- 唯一操作数: {analysis.get('unique_operations', 0)}
- 成功率: {analysis.get('success_rate', 0):.2f}%
- 平均执行时间: {analysis.get('avg_duration', 0):.2f}秒

## 慢操作 (>5秒)
"""
        
        for op in analysis.get('slow_operations', [])[:5]:
            report += f"- {op['operation']}: {op['duration']:.2f}秒\n"
        
        report += "\n## 内存密集操作 (>50MB)\n"
        for op in analysis.get('memory_intensive_operations', [])[:5]:
            report += f"- {op['operation']}: {op['memory_delta']:.2f}MB\n"
        
        report += "\n## 最近错误\n"
        for op in analysis.get('error_operations', [])[:5]:
            report += f"- {op['operation']}: {op['error_message']}\n"
        
        return report


# 便捷函数
def analyze_performance(hours: int = 1) -> Dict[str, Any]:
    """分析性能数据"""
    analyzer = PerformanceAnalyzer()
    return analyzer.analyze_performance(hours)


def generate_performance_report() -> str:
    """生成性能报告"""
    analyzer = PerformanceAnalyzer()
    return analyzer.generate_performance_report()


def get_operation_stats(operation: str = None) -> Dict[str, Any]:
    """获取操作统计"""
    return get_performance_collector().get_operation_stats(operation)
