# -*- coding: utf-8 -*-
"""
缓存管理模块

提供统一的缓存接口，支持Redis和文件缓存。
自动选择最佳缓存策略，提供缓存键生成、TTL管理等功能。
"""
import hashlib
import pickle
import json
import time
import logging
# 导入新的统一日志系统
from utils.universal_logging import get_logger, ensure_trace_id, LogContext, set_trace_id
import functools
from pathlib import Path
from typing import Optional, Any, Dict, Union, List
from abc import ABC, abstractmethod
import threading

logger = get_logger(__name__)

try:
    import redis
    REDIS_AVAILABLE = True
except ImportError:
    REDIS_AVAILABLE = False
    logger.warning("Redis不可用，将使用文件缓存")


class CacheBackend(ABC):
    """缓存后端抽象基类"""
    
    @abstractmethod
    def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        pass
    
    @abstractmethod
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """设置缓存值"""
        pass
    
    @abstractmethod
    def delete(self, key: str) -> bool:
        """删除缓存"""
        pass
    
    @abstractmethod
    def exists(self, key: str) -> bool:
        """检查缓存是否存在"""
        pass
    
    @abstractmethod
    def clear(self) -> bool:
        """清空所有缓存"""
        pass
    
    @abstractmethod
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        pass


class RedisBackend(CacheBackend):
    """Redis缓存后端"""
    
    def __init__(
        self, 
        host: str = "localhost", 
        port: int = 6379, 
        db: int = 0,
        password: Optional[str] = None,
        decode_responses: bool = False
    ):
        if not REDIS_AVAILABLE:
            raise ImportError("Redis不可用，请安装redis包")
        
        try:
            self.client = redis.Redis(
                host=host, 
                port=port, 
                db=db, 
                password=password,
                decode_responses=decode_responses,
                socket_connect_timeout=5,
                socket_timeout=5
            )
            # 测试连接
            self.client.ping()
            logger.info(f"Redis连接成功: {host}:{port}/{db}")
        except Exception as e:
            logger.error(f"Redis连接失败: {e}")
            raise
    
    def get(self, key: str) -> Optional[Any]:
        try:
            data = self.client.get(key)
            if data is None:
                return None
            return pickle.loads(data)
        except Exception as e:
            logger.error(f"Redis获取缓存失败 {key}: {e}")
            return None
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        try:
            data = pickle.dumps(value)
            if ttl:
                return self.client.setex(key, ttl, data)
            else:
                return self.client.set(key, data)
        except Exception as e:
            logger.error(f"Redis设置缓存失败 {key}: {e}")
            return False
    
    def delete(self, key: str) -> bool:
        try:
            return bool(self.client.delete(key))
        except Exception as e:
            logger.error(f"Redis删除缓存失败 {key}: {e}")
            return False
    
    def exists(self, key: str) -> bool:
        try:
            return bool(self.client.exists(key))
        except Exception as e:
            logger.error(f"Redis检查缓存失败 {key}: {e}")
            return False
    
    def clear(self) -> bool:
        try:
            return self.client.flushdb()
        except Exception as e:
            logger.error(f"Redis清空缓存失败: {e}")
            return False
    
    def get_stats(self) -> Dict[str, Any]:
        try:
            info = self.client.info()
            return {
                "backend": "redis",
                "connected_clients": info.get("connected_clients", 0),
                "used_memory": info.get("used_memory", 0),
                "used_memory_human": info.get("used_memory_human", "0B"),
                "keyspace_hits": info.get("keyspace_hits", 0),
                "keyspace_misses": info.get("keyspace_misses", 0),
                "total_commands_processed": info.get("total_commands_processed", 0)
            }
        except Exception as e:
            logger.error(f"Redis获取统计信息失败: {e}")
            return {"backend": "redis", "error": str(e)}


class FileBackend(CacheBackend):
    """文件缓存后端"""
    
    def __init__(self, cache_dir: Path, max_size: int = 1000):
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        self.max_size = max_size
        self._lock = threading.RLock()
        self._stats = {
            "hits": 0,
            "misses": 0,
            "sets": 0,
            "deletes": 0
        }
        logger.info(f"文件缓存初始化: {self.cache_dir}")
    
    def _get_cache_path(self, key: str) -> Path:
        """获取缓存文件路径"""
        safe_key = hashlib.md5(key.encode()).hexdigest()
        return self.cache_dir / f"{safe_key}.cache"
    
    def _get_meta_path(self, key: str) -> Path:
        """获取元数据文件路径"""
        safe_key = hashlib.md5(key.encode()).hexdigest()
        return self.cache_dir / f"{safe_key}.meta"
    
    def _is_expired(self, meta_path: Path) -> bool:
        """检查缓存是否过期"""
        try:
            if not meta_path.exists():
                return True
            
            with open(meta_path, 'r') as f:
                meta = json.load(f)
            
            ttl = meta.get('ttl')
            if ttl is None:
                return False  # 无TTL，永不过期
            
            created_time = meta.get('created_time', 0)
            return time.time() > (created_time + ttl)
        except Exception:
            return True
    
    def _cleanup_expired(self):
        """清理过期缓存"""
        try:
            for meta_file in self.cache_dir.glob("*.meta"):
                if self._is_expired(meta_file):
                    cache_file = meta_file.with_suffix('.cache')
                    meta_file.unlink(missing_ok=True)
                    cache_file.unlink(missing_ok=True)
        except Exception as e:
            logger.error(f"清理过期缓存失败: {e}")
    
    def _enforce_size_limit(self):
        """强制执行大小限制"""
        try:
            cache_files = list(self.cache_dir.glob("*.cache"))
            if len(cache_files) <= self.max_size:
                return
            
            # 按修改时间排序，删除最旧的文件
            cache_files.sort(key=lambda p: p.stat().st_mtime)
            files_to_delete = cache_files[:len(cache_files) - self.max_size]
            
            for cache_file in files_to_delete:
                meta_file = cache_file.with_suffix('.meta')
                cache_file.unlink(missing_ok=True)
                meta_file.unlink(missing_ok=True)
                
            logger.info(f"清理了 {len(files_to_delete)} 个旧缓存文件")
        except Exception as e:
            logger.error(f"强制大小限制失败: {e}")
    
    def get(self, key: str) -> Optional[Any]:
        with self._lock:
            try:
                cache_path = self._get_cache_path(key)
                meta_path = self._get_meta_path(key)
                
                if not cache_path.exists() or self._is_expired(meta_path):
                    self._stats["misses"] += 1
                    return None
                
                with open(cache_path, 'rb') as f:
                    data = pickle.load(f)
                
                self._stats["hits"] += 1
                return data
            except Exception as e:
                logger.error(f"文件缓存获取失败 {key}: {e}")
                self._stats["misses"] += 1
                return None
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        with self._lock:
            try:
                cache_path = self._get_cache_path(key)
                meta_path = self._get_meta_path(key)
                
                # 保存数据
                with open(cache_path, 'wb') as f:
                    pickle.dump(value, f)
                
                # 保存元数据
                meta = {
                    "created_time": time.time(),
                    "ttl": ttl,
                    "key": key
                }
                with open(meta_path, 'w') as f:
                    json.dump(meta, f)
                
                self._stats["sets"] += 1
                
                # 定期清理
                if self._stats["sets"] % 100 == 0:
                    self._cleanup_expired()
                    self._enforce_size_limit()
                
                return True
            except Exception as e:
                logger.error(f"文件缓存设置失败 {key}: {e}")
                return False
    
    def delete(self, key: str) -> bool:
        with self._lock:
            try:
                cache_path = self._get_cache_path(key)
                meta_path = self._get_meta_path(key)
                
                deleted = False
                if cache_path.exists():
                    cache_path.unlink()
                    deleted = True
                if meta_path.exists():
                    meta_path.unlink()
                    deleted = True
                
                if deleted:
                    self._stats["deletes"] += 1
                
                return deleted
            except Exception as e:
                logger.error(f"文件缓存删除失败 {key}: {e}")
                return False
    
    def exists(self, key: str) -> bool:
        with self._lock:
            cache_path = self._get_cache_path(key)
            meta_path = self._get_meta_path(key)
            return cache_path.exists() and not self._is_expired(meta_path)
    
    def clear(self) -> bool:
        with self._lock:
            try:
                for file_path in self.cache_dir.glob("*"):
                    if file_path.is_file():
                        file_path.unlink()
                logger.info("文件缓存已清空")
                return True
            except Exception as e:
                logger.error(f"文件缓存清空失败: {e}")
                return False
    
    def get_stats(self) -> Dict[str, Any]:
        with self._lock:
            try:
                cache_files = list(self.cache_dir.glob("*.cache"))
                total_size = sum(f.stat().st_size for f in cache_files)
                
                return {
                    "backend": "file",
                    "cache_dir": str(self.cache_dir),
                    "total_files": len(cache_files),
                    "total_size_bytes": total_size,
                    "total_size_mb": round(total_size / 1024 / 1024, 2),
                    "max_size": self.max_size,
                    "hits": self._stats["hits"],
                    "misses": self._stats["misses"],
                    "sets": self._stats["sets"],
                    "deletes": self._stats["deletes"],
                    "hit_rate": round(
                        self._stats["hits"] / max(self._stats["hits"] + self._stats["misses"], 1) * 100, 2
                    )
                }
            except Exception as e:
                logger.error(f"文件缓存获取统计信息失败: {e}")
                return {"backend": "file", "error": str(e)}


class CacheManager:
    """统一缓存管理器"""

    def __init__(
        self,
        backend: Optional[CacheBackend] = None,
        default_ttl: int = 3600,
        key_prefix: str = "subtitle_cache"
    ):
        self.backend = backend
        self.default_ttl = default_ttl
        self.key_prefix = key_prefix

        if self.backend is None:
            self.backend = self._create_default_backend()

        logger.info(f"缓存管理器初始化完成，后端: {type(self.backend).__name__}")

    def _create_default_backend(self) -> CacheBackend:
        """创建默认缓存后端"""
        # 尝试使用Redis
        if REDIS_AVAILABLE:
            try:
                return RedisBackend()
            except Exception as e:
                logger.warning(f"Redis后端创建失败，回退到文件缓存: {e}")

        # 回退到文件缓存
        try:
            from .config_manager import config
            cache_dir = config.paths.cache_dir / "subtitle_cache"
        except ImportError:
            cache_dir = Path.cwd() / "cache" / "subtitle_cache"

        return FileBackend(cache_dir)

    def _generate_key(self, *args, **kwargs) -> str:
        """生成缓存键"""
        # 创建包含所有参数的字符串
        key_parts = [self.key_prefix]

        # 添加位置参数
        for arg in args:
            if isinstance(arg, (str, int, float, bool)):
                key_parts.append(str(arg))
            elif isinstance(arg, (dict, list, tuple)):
                key_parts.append(json.dumps(arg, sort_keys=True))
            else:
                key_parts.append(str(hash(str(arg))))

        # 添加关键字参数
        if kwargs:
            sorted_kwargs = sorted(kwargs.items())
            key_parts.append(json.dumps(sorted_kwargs, sort_keys=True))

        # 生成最终键
        key_string = ":".join(key_parts)
        return hashlib.md5(key_string.encode()).hexdigest()

    def get(self, *args, **kwargs) -> Optional[Any]:
        """获取缓存"""
        key = self._generate_key(*args, **kwargs)
        return self.backend.get(key)

    def set(self, value: Any, *args, ttl: Optional[int] = None, **kwargs) -> bool:
        """设置缓存"""
        key = self._generate_key(*args, **kwargs)
        actual_ttl = ttl if ttl is not None else self.default_ttl
        return self.backend.set(key, value, actual_ttl)

    def delete(self, *args, **kwargs) -> bool:
        """删除缓存"""
        key = self._generate_key(*args, **kwargs)
        return self.backend.delete(key)

    def exists(self, *args, **kwargs) -> bool:
        """检查缓存是否存在"""
        key = self._generate_key(*args, **kwargs)
        return self.backend.exists(key)

    def clear_all(self) -> bool:
        """清空所有缓存"""
        return self.backend.clear()

    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        stats = self.backend.get_stats()
        stats.update({
            "default_ttl": self.default_ttl,
            "key_prefix": self.key_prefix
        })
        return stats


class TranscriptionCache(CacheManager):
    """转录专用缓存"""

    def __init__(self, backend: Optional[CacheBackend] = None):
        super().__init__(
            backend=backend,
            default_ttl=7200,  # 2小时
            key_prefix="transcription"
        )

    def get_transcription(
        self,
        audio_path: str,
        model: str,
        language: str,
        additional_config: Dict[str, Any] = None
    ) -> Optional[Any]:
        """获取转录缓存"""
        config_dict = additional_config or {}
        return self.get(audio_path, model, language, config_dict)

    def set_transcription(
        self,
        result: Any,
        audio_path: str,
        model: str,
        language: str,
        additional_config: Dict[str, Any] = None,
        ttl: Optional[int] = None
    ) -> bool:
        """设置转录缓存"""
        config_dict = additional_config or {}
        return self.set(result, audio_path, model, language, config_dict, ttl=ttl)


class TranslationCache(CacheManager):
    """翻译专用缓存"""

    def __init__(self, backend: Optional[CacheBackend] = None):
        super().__init__(
            backend=backend,
            default_ttl=86400,  # 24小时
            key_prefix="translation"
        )

    def get_translation(
        self,
        text: str,
        source_language: str,
        target_language: str,
        model: str = "default"
    ) -> Optional[Any]:
        """获取翻译缓存"""
        return self.get(text, source_language, target_language, model)

    def set_translation(
        self,
        result: Any,
        text: str,
        source_language: str,
        target_language: str,
        model: str = "default",
        ttl: Optional[int] = None
    ) -> bool:
        """设置翻译缓存"""
        return self.set(result, text, source_language, target_language, model, ttl=ttl)


class SubtitleCache(CacheManager):
    """字幕处理专用缓存"""

    def __init__(self, backend: Optional[CacheBackend] = None):
        super().__init__(
            backend=backend,
            default_ttl=3600,  # 1小时
            key_prefix="subtitle"
        )

    def get_processed_subtitle(
        self,
        content: str,
        processing_type: str,
        parameters: Dict[str, Any] = None
    ) -> Optional[Any]:
        """获取处理后的字幕缓存"""
        params = parameters or {}
        return self.get(content, processing_type, params)

    def set_processed_subtitle(
        self,
        result: Any,
        content: str,
        processing_type: str,
        parameters: Dict[str, Any] = None,
        ttl: Optional[int] = None
    ) -> bool:
        """设置处理后的字幕缓存"""
        params = parameters or {}
        return self.set(result, content, processing_type, params, ttl=ttl)


# 全局缓存实例
_global_cache_manager = None
_transcription_cache = None
_translation_cache = None
_subtitle_cache = None


def get_cache_manager() -> CacheManager:
    """获取全局缓存管理器"""
    global _global_cache_manager
    if _global_cache_manager is None:
        _global_cache_manager = CacheManager()
    return _global_cache_manager


def get_transcription_cache() -> TranscriptionCache:
    """获取转录缓存"""
    global _transcription_cache
    if _transcription_cache is None:
        _transcription_cache = TranscriptionCache()
    return _transcription_cache


def get_translation_cache() -> TranslationCache:
    """获取翻译缓存"""
    global _translation_cache
    if _translation_cache is None:
        _translation_cache = TranslationCache()
    return _translation_cache


def get_subtitle_cache() -> SubtitleCache:
    """获取字幕缓存"""
    global _subtitle_cache
    if _subtitle_cache is None:
        _subtitle_cache = SubtitleCache()
    return _subtitle_cache


def cache_decorator(
    cache_manager: Optional[CacheManager] = None,
    ttl: Optional[int] = None,
    key_args: Optional[List[str]] = None
):
    """缓存装饰器"""
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # 获取缓存管理器
            cm = cache_manager or get_cache_manager()

            # 构建缓存键参数
            cache_args = [func.__name__]
            if key_args:
                # 使用指定的参数名
                for arg_name in key_args:
                    if arg_name in kwargs:
                        cache_args.append(kwargs[arg_name])
            else:
                # 使用所有参数
                cache_args.extend(args)
                cache_args.append(kwargs)

            # 尝试从缓存获取
            cached_result = cm.get(*cache_args)
            if cached_result is not None:
                logger.debug(f"缓存命中: {func.__name__}")
                return cached_result

            # 执行函数
            result = func(*args, **kwargs)

            # 保存到缓存
            cm.set(result, *cache_args, ttl=ttl)
            logger.debug(f"缓存保存: {func.__name__}")

            return result
        return wrapper
    return decorator
