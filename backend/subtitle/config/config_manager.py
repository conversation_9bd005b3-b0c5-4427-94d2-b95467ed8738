# -*- coding: utf-8 -*-
"""
统一配置管理器

提供标准化的配置加载、验证和管理功能，支持：
- 环境变量配置
- 配置文件加载
- 配置验证和默认值
- 运行时配置更新
"""

import os
import json
from typing import Dict, Any, Optional, Type, Union
from pathlib import Path
from dataclasses import dataclass, field
from enum import Enum
import logging
# 导入新的统一日志系统
from utils.universal_logging import get_logger, ensure_trace_id, LogContext, set_trace_id

logger = get_logger(__name__)


class ConfigSource(Enum):
    """配置来源枚举"""
    ENVIRONMENT = "environment"
    FILE = "file"
    DEFAULT = "default"
    RUNTIME = "runtime"


@dataclass
class ConfigItem:
    """配置项定义"""
    key: str
    default_value: Any
    description: str
    required: bool = False
    validator: Optional[callable] = None
    source: ConfigSource = ConfigSource.DEFAULT
    env_var: Optional[str] = None


@dataclass
class TranslatorConfig:
    """翻译器配置"""
    # OpenAI 配置
    openai_api_key: Optional[str] = None
    openai_base_url: Optional[str] = None
    openai_model: str = "gpt-4o-mini"
    openai_temperature: float = 0.7
    openai_timeout: int = 60
    
    # DeepLX 配置
    deeplx_endpoint: Optional[str] = None
    deeplx_timeout: int = 20
    
    # 通用配置
    default_translator: str = "openai"
    thread_num: int = 1
    batch_num: int = 1
    use_cache: bool = True
    retry_times: int = 1


@dataclass
class SplitterConfig:
    """文本分割器配置"""
    model: str = "gpt-4o-mini"
    temperature: float = 0.4
    timeout: int = 60
    retry_times: int = 1
    split_type: str = "semantic"
    max_word_count_cjk: int = 25
    max_word_count_english: int = 18
    thread_num: int = 1
    use_cache: bool = True
    api_key: Optional[str] = None
    base_url: Optional[str] = None


@dataclass
class TranscriptionConfig:
    """转录配置"""
    model: str = "whisper-1"
    language: Optional[str] = None
    prompt: Optional[str] = None
    response_format: str = "verbose_json"
    temperature: float = 0.0
    timeout: int = 300
    use_cache: bool = True


@dataclass
class AudioProcessorConfig:
    """音频处理器配置"""
    output_format: str = "wav"
    sample_rate: int = 16000
    channels: int = 1
    timeout: int = 300


@dataclass
class CacheConfig:
    """缓存配置"""
    enabled: bool = True
    backend: str = "memory"  # memory, redis, file
    ttl: int = 86400  # 24 hours
    max_size: int = 1000
    cache_dir: Optional[str] = None


@dataclass
class LoggingConfig:
    """日志配置"""
    level: str = "INFO"
    format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    file_path: Optional[str] = None
    max_bytes: int = 10 * 1024 * 1024  # 10MB
    backup_count: int = 5


@dataclass
class AppConfig:
    """应用总配置"""
    translator: TranslatorConfig = field(default_factory=TranslatorConfig)
    splitter: SplitterConfig = field(default_factory=SplitterConfig)
    transcription: TranscriptionConfig = field(default_factory=TranscriptionConfig)
    audio_processor: AudioProcessorConfig = field(default_factory=AudioProcessorConfig)
    cache: CacheConfig = field(default_factory=CacheConfig)
    logging: LoggingConfig = field(default_factory=LoggingConfig)
    
    # 全局配置
    debug: bool = False
    max_file_size: int = 100 * 1024 * 1024  # 100MB
    temp_dir: Optional[str] = None


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_file: Optional[str] = None):
        self._config = AppConfig()
        self._config_items = self._define_config_items()
        self._config_file = config_file
        
        # 按优先级加载配置：默认值 → 配置文件 → 环境变量
        self._load_defaults()
        if config_file:
            self._load_from_file(config_file)
        self._load_from_environment()
        
    def _define_config_items(self) -> Dict[str, ConfigItem]:
        """定义所有配置项"""
        return {
            # 翻译器配置
            "translator.openai_api_key": ConfigItem(
                "translator.openai_api_key",
                None,
                "OpenAI API密钥",
                required=False,
                env_var="OPENAI_API_KEY"
            ),
            "translator.openai_base_url": ConfigItem(
                "translator.openai_base_url",
                None,
                "OpenAI API基础URL",
                env_var="OPENAI_BASE_URL"
            ),
            "translator.openai_model": ConfigItem(
                "translator.openai_model",
                "gpt-4o-mini",
                "OpenAI模型名称",
                env_var="OPENAI_MODEL"
            ),
            "translator.deeplx_endpoint": ConfigItem(
                "translator.deeplx_endpoint",
                None,
                "DeepLX API端点",
                env_var="DEEPLX_ENDPOINT"
            ),
            "translator.default_translator": ConfigItem(
                "translator.default_translator",
                "openai",
                "默认翻译器类型",
                validator=lambda x: x in ["openai", "deeplx"],
                env_var="DEFAULT_TRANSLATOR"
            ),
            
            # 分割器配置
            "splitter.model": ConfigItem(
                "splitter.model",
                "gpt-4o-mini",
                "文本分割器模型",
                env_var="SPLITTER_MODEL"
            ),
            "splitter.split_type": ConfigItem(
                "splitter.split_type",
                "semantic",
                "分割类型",
                validator=lambda x: x in ["semantic", "sentence"],
                env_var="SPLITTER_TYPE"
            ),
            
            # 缓存配置
            "cache.enabled": ConfigItem(
                "cache.enabled",
                True,
                "是否启用缓存",
                validator=lambda x: isinstance(x, bool),
                env_var="CACHE_ENABLED"
            ),
            "cache.backend": ConfigItem(
                "cache.backend",
                "memory",
                "缓存后端类型",
                validator=lambda x: x in ["memory", "redis", "file"],
                env_var="CACHE_BACKEND"
            ),
            
            # 日志配置
            "logging.level": ConfigItem(
                "logging.level",
                "INFO",
                "日志级别",
                validator=lambda x: x in ["DEBUG", "INFO", "WARNING", "ERROR"],
                env_var="LOG_LEVEL"
            ),
            
            # 全局配置
            "debug": ConfigItem(
                "debug",
                False,
                "调试模式",
                validator=lambda x: isinstance(x, bool),
                env_var="DEBUG"
            ),
            "temp_dir": ConfigItem(
                "temp_dir",
                None,
                "临时文件目录",
                env_var="TEMP_DIR"
            )
        }
    
    def _load_defaults(self):
        """加载默认配置"""
        # 默认值已在dataclass中定义
        logger.debug("已加载默认配置")
    
    def _load_from_file(self, config_file: str):
        """从配置文件加载"""
        try:
            config_path = Path(config_file)
            if not config_path.exists():
                logger.warning(f"配置文件不存在: {config_file}")
                return
                
            with open(config_path, 'r', encoding='utf-8') as f:
                if config_path.suffix.lower() == '.json':
                    file_config = json.load(f)
                else:
                    logger.warning(f"不支持的配置文件格式: {config_path.suffix}")
                    return
            
            self._apply_config_dict(file_config, ConfigSource.FILE)
            logger.info(f"已从文件加载配置: {config_file}")
            
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
    
    def _load_from_environment(self):
        """从环境变量加载"""
        for config_item in self._config_items.values():
            if config_item.env_var:
                env_value = os.getenv(config_item.env_var)
                if env_value is not None:
                    try:
                        # 类型转换
                        converted_value = self._convert_env_value(env_value, config_item.default_value)
                        self._set_config_value(config_item.key, converted_value, ConfigSource.ENVIRONMENT)
                    except Exception as e:
                        logger.warning(f"环境变量转换失败 {config_item.env_var}: {e}")
        
        logger.debug("已从环境变量加载配置")
    
    def _convert_env_value(self, env_value: str, default_value: Any) -> Any:
        """转换环境变量值到正确类型"""
        if isinstance(default_value, bool):
            return env_value.lower() in ('true', '1', 'yes', 'on')
        elif isinstance(default_value, int):
            return int(env_value)
        elif isinstance(default_value, float):
            return float(env_value)
        else:
            return env_value
    
    def _apply_config_dict(self, config_dict: Dict[str, Any], source: ConfigSource):
        """应用配置字典"""
        for key, value in self._flatten_dict(config_dict).items():
            if key in self._config_items:
                self._set_config_value(key, value, source)
    
    def _flatten_dict(self, d: Dict[str, Any], parent_key: str = '') -> Dict[str, Any]:
        """展平嵌套字典"""
        items = []
        for k, v in d.items():
            new_key = f"{parent_key}.{k}" if parent_key else k
            if isinstance(v, dict):
                items.extend(self._flatten_dict(v, new_key).items())
            else:
                items.append((new_key, v))
        return dict(items)
    
    def _set_config_value(self, key: str, value: Any, source: ConfigSource):
        """设置配置值"""
        config_item = self._config_items.get(key)
        if not config_item:
            logger.warning(f"未知的配置项: {key}")
            return
        
        # 验证值
        if config_item.validator:
            try:
                if not config_item.validator(value):
                    logger.warning(f"配置值验证失败: {key}={value}")
                    return
            except Exception as e:
                logger.warning(f"配置值验证出错: {key}={value}, {e}")
                return
        
        # 设置到配置对象
        self._set_nested_attr(self._config, key, value)
        config_item.source = source
        
        logger.debug(f"设置配置: {key}={value} (来源: {source.value})")
    
    def _set_nested_attr(self, obj: Any, key: str, value: Any):
        """设置嵌套属性"""
        parts = key.split('.')
        for part in parts[:-1]:
            obj = getattr(obj, part)
        setattr(obj, parts[-1], value)
    
    def get_config(self) -> AppConfig:
        """获取完整配置"""
        return self._config
    
    def get_translator_config(self) -> TranslatorConfig:
        """获取翻译器配置"""
        return self._config.translator
    
    def get_splitter_config(self) -> SplitterConfig:
        """获取分割器配置"""
        return self._config.splitter
    
    def get_transcription_config(self) -> TranscriptionConfig:
        """获取转录配置"""
        return self._config.transcription
    
    def get_audio_processor_config(self) -> AudioProcessorConfig:
        """获取音频处理器配置"""
        return self._config.audio_processor
    
    def get_cache_config(self) -> CacheConfig:
        """获取缓存配置"""
        return self._config.cache
    
    def get_logging_config(self) -> LoggingConfig:
        """获取日志配置"""
        return self._config.logging
    
    def get_value(self, key: str, default: Any = None) -> Any:
        """获取单个配置值"""
        try:
            parts = key.split('.')
            obj = self._config
            for part in parts:
                obj = getattr(obj, part)
            return obj
        except AttributeError:
            return default
    
    def set_value(self, key: str, value: Any):
        """运行时设置配置值"""
        self._set_config_value(key, value, ConfigSource.RUNTIME)
    
    def validate_config(self) -> Dict[str, str]:
        """验证配置并返回错误信息"""
        errors = {}
        
        # 检查必需的配置项
        for key, config_item in self._config_items.items():
            if config_item.required:
                current_value = self.get_value(key)
                if current_value is None:
                    errors[key] = f"必需的配置项未设置: {config_item.description}"
        
        # 特定验证逻辑
        translator_type = self.get_value("translator.default_translator")
        if translator_type == "openai" and not self.get_value("translator.openai_api_key"):
            errors["translator.openai_api_key"] = "使用OpenAI翻译器时必须设置API密钥"
        
        return errors
    
    def save_to_file(self, file_path: str):
        """保存配置到文件"""
        try:
            config_dict = self._config_to_dict()
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(config_dict, f, indent=2, ensure_ascii=False)
            logger.info(f"配置已保存到: {file_path}")
        except Exception as e:
            logger.error(f"保存配置文件失败: {e}")
            raise
    
    def _config_to_dict(self) -> Dict[str, Any]:
        """将配置对象转换为字典"""
        import dataclasses
        return dataclasses.asdict(self._config)
    
    def reload(self):
        """重新加载配置"""
        logger.info("重新加载配置")
        self._load_defaults()
        if self._config_file:
            self._load_from_file(self._config_file)
        self._load_from_environment()


# 全局配置管理器实例
_config_manager: Optional[ConfigManager] = None


def get_config_manager() -> ConfigManager:
    """获取全局配置管理器"""
    global _config_manager
    if _config_manager is None:
        # 查找配置文件
        config_file = None
        for candidate in ['config.json', 'subtitle_config.json', 'config/app.json']:
            if os.path.exists(candidate):
                config_file = candidate
                break
        
        _config_manager = ConfigManager(config_file)
    return _config_manager


def get_config() -> AppConfig:
    """获取应用配置"""
    return get_config_manager().get_config()


def get_translator_config() -> TranslatorConfig:
    """获取翻译器配置"""
    return get_config_manager().get_translator_config()


def get_splitter_config() -> SplitterConfig:
    """获取分割器配置"""
    return get_config_manager().get_splitter_config() 