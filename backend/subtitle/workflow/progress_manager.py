"""
进度管理模块

提供统一的进度报告、状态管理和错误处理功能。
"""

import time
import traceback
from typing import Dict, Any, Optional
from google.protobuf import any_pb2
import logging
# 导入新的统一日志系统
from utils.universal_logging import get_logger, ensure_trace_id, LogContext, set_trace_id

# 导入新的标准化常量和工厂
from subtitle.constants import OperationStatus, ErrorCode, StageNames
from subtitle.utils.progress_factory import (
    create_progress_dict,
    create_success_progress,
    create_error_progress,
    create_in_progress_update
)

# 导入trace_id管理器
from utils.trace_id_manager import ensure_trace_id

# 导入trace_id管理器
from utils.trace_id_manager import ensure_trace_id

logger = get_logger(__name__)


def _create_progress(
    stage_name: str,
    percentage: int,
    message: str,
    is_error: bool = False,
    error_message: str = "",
    final_result_payload: Any = None,
    result_field_name: str = None,
    trace_id: str = None,
    status: str = None,
    error_detail: Dict = None,
    data: Dict = None
) -> Dict:
    """
    创建统一的进度更新格式
    
    Args:
        stage_name: 阶段名称
        percentage: 进度百分比 (0-100)
        message: 进度消息
        is_error: 是否为错误状态
        error_message: 错误消息
        final_result_payload: 最终结果负载
        result_field_name: 结果字段名
        trace_id: 追踪ID
        status: 操作状态
        error_detail: 错误详情
        data: 数据负载
        
    Returns:
        格式化的进度字典
    """
    # 确保trace_id有效
    effective_trace_id = ensure_trace_id(trace_id, f"_create_progress-{stage_name}")
    
    progress_dict = {
        "stage_name": stage_name,
        "percentage": percentage,
        "message": message,
        "timestamp": time.time(),
        "is_error": is_error,
        "error_message": error_message if error_message else "",
        "trace_id": effective_trace_id,  # 使用确保有效的trace_id
        "status": status if status else ("ERROR" if is_error else "IN_PROGRESS" if percentage < 100 else "SUCCESS"),
        "error_detail": error_detail,
        "data": data
    }
    
    # 处理最终结果负载
    if final_result_payload is not None and result_field_name:
        # 转换为标准的 final_result 格式
        progress_dict["final_result"] = {result_field_name: final_result_payload}
    
    return progress_dict


class ProgressManager:
    """进度管理器类"""
    
    def __init__(self):
        self.progress_callbacks = []
        self.current_stage = None
        self.overall_progress = 0
        
    def add_callback(self, callback):
        """添加进度回调函数"""
        self.progress_callbacks.append(callback)
        
    def remove_callback(self, callback):
        """移除进度回调函数"""
        if callback in self.progress_callbacks:
            self.progress_callbacks.remove(callback)
            
    def update_progress(self, stage_name: str, percentage: int, message: str, **kwargs):
        """更新进度并通知所有回调"""
        self.current_stage = stage_name
        self.overall_progress = percentage
        
        # 确保kwargs中的trace_id有效
        if 'trace_id' in kwargs:
            kwargs['trace_id'] = ensure_trace_id(kwargs['trace_id'], f"update_progress-{stage_name}")
        
        progress_data = _create_progress(
            stage_name=stage_name,
            percentage=percentage,
            message=message,
            **kwargs
        )
        
        # 通知所有回调
        for callback in self.progress_callbacks:
            try:
                callback(progress_data)
            except Exception as e:
                logger.error(f"Progress callback error: {e}")
                
    def create_error_progress(self, stage_name: str, error: Exception, trace_id: str = None) -> Dict:
        """创建错误进度报告"""
        tb_str = traceback.format_exc()
        error_msg = str(error)
        
        # 确保trace_id有效
        effective_trace_id = ensure_trace_id(trace_id, f"create_error_progress-{stage_name}")
        
        return create_error_progress(
            stage_name=stage_name,
            error_message=f"{error_msg}\n{tb_str}",
            trace_id=effective_trace_id,
            error_code=ErrorCode.UNEXPECTED_ERROR,
            error_detail={
                "error_code": ErrorCode.UNEXPECTED_ERROR.value,
                "technical_message": error_msg,
                "user_message": f"{stage_name}过程中发生错误",
                "context": {"exception_type": type(error).__name__}
            }
        )

    def create_success_progress(self, stage_name: str, message: str, data: Dict = None, trace_id: str = None) -> Dict:
        """创建成功进度报告"""
        # 确保trace_id有效
        effective_trace_id = ensure_trace_id(trace_id, f"create_success_progress-{stage_name}")
        
        return create_success_progress(
            stage_name=stage_name,
            message=message,
            trace_id=effective_trace_id,
            data=data
        )


# 全局进度管理器实例
default_progress_manager = ProgressManager() 