# -*- coding: utf-8 -*-
"""
配置模块 - 向后兼容层

此模块保持向后兼容性，同时使用新的配置管理器。
逐步迁移到 config_manager.py 中的新配置系统。
"""
import logging
# 导入新的统一日志系统
from utils.universal_logging import get_logger, ensure_trace_id, LogContext, set_trace_id
import os
from pathlib import Path

# 导入新的配置管理器
try:
    from .config_manager import config as new_config
    _use_new_config = True
except ImportError:
    _use_new_config = False
    import warnings
    warnings.warn("无法导入新配置管理器，使用旧配置", DeprecationWarning)

# 应用信息 - 保持向后兼容
if _use_new_config:
    VERSION = new_config.version
    APP_NAME = new_config.app_name
else:
    VERSION = "v1.3.3"
    APP_NAME = "VideoCaptioner"

YEAR = 2025
AUTHOR = "Weifeng"

HELP_URL = "https://github.com/WEIFENG2333/VideoCaptioner"
GITHUB_REPO_URL = "https://github.com/WEIFENG2333/VideoCaptioner"
RELEASE_URL = "https://github.com/WEIFENG2333/VideoCaptioner/releases/latest"
FEEDBACK_URL = "https://github.com/WEIFENG2333/VideoCaptioner/issues"

# 路径配置 - 使用新配置管理器或回退到旧配置
if _use_new_config:
    ROOT_PATH = new_config.paths.root_path
    APPDATA_PATH = new_config.paths.appdata_path
    WORK_PATH = new_config.paths.work_path
    CACHE_PATH = new_config.paths.cache_dir
    LOG_PATH = new_config.paths.log_dir
    MODEL_PATH = new_config.paths.model_dir
else:
    ROOT_PATH = Path(__file__).parent
    APPDATA_PATH = ROOT_PATH.parent / "AppData"
    WORK_PATH = ROOT_PATH.parent / "work-dir"
    CACHE_PATH = APPDATA_PATH / "cache"
    LOG_PATH = APPDATA_PATH / "logs"
    MODEL_PATH = APPDATA_PATH / "models"

# 资源路径 (保持不变)
RESOURCE_PATH = ROOT_PATH.parent / "resource"
BIN_PATH = RESOURCE_PATH / "bin"
ASSETS_PATH = RESOURCE_PATH / "assets"
SUBTITLE_STYLE_PATH = RESOURCE_PATH / "subtitle_style"
SETTINGS_PATH = APPDATA_PATH / "settings.json"

# 日志配置
if _use_new_config:
    LOG_LEVEL = getattr(logging, new_config.server.log_level)
    LOG_FORMAT = new_config.server.log_format
else:
    LOG_LEVEL = logging.INFO
    LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

# 环境变量设置 (保持不变)
if BIN_PATH.exists():
    os.environ["PATH"] = str(BIN_PATH) + os.pathsep + os.environ["PATH"]
    os.environ["PYTHON_VLC_MODULE_PATH"] = str(BIN_PATH / "vlc")

# 字幕处理配置 - 使用新配置管理器或回退到旧配置
if _use_new_config:
    # 转录配置
    DEFAULT_TRANSCRIBE_MODEL_NAME = new_config.transcription.transcribe_model
    DEFAULT_TRANSCRIBE_LANGUAGE = new_config.transcription.transcribe_language
    DEFAULT_USE_ASR_CACHE = new_config.transcription.use_asr_cache
    DEFAULT_NEED_WORD_TIMESTAMP = new_config.transcription.need_word_timestamp

    # API配置



    # 分割器配置
    SPLITTER_MODEL_NAME = new_config.processing.splitter_model
    SPLITTER_TEMPERATURE = new_config.processing.splitter_temperature
    SPLITTER_TIMEOUT = new_config.processing.splitter_timeout
    SPLITTER_RETRY_TIMES = new_config.processing.splitter_retry_times
    SPLITTER_THREAD_NUM = new_config.processing.splitter_thread_num

    # 优化器配置
    OPTIMIZER_MODEL_NAME = new_config.processing.optimizer_model
    OPTIMIZER_BATCH_NUM = new_config.processing.optimizer_batch_num
    OPTIMIZER_THREAD_NUM = new_config.processing.optimizer_thread_num

    # 翻译器配置
    TRANSLATOR_MODEL_NAME = new_config.processing.translator_model
    TRANSLATOR_THREAD_NUM = new_config.processing.translator_thread_num
    TRANSLATOR_BATCH_NUM = new_config.processing.translator_batch_num
    DEFAULT_TRANSLATOR_SERVICE_NAME = new_config.processing.translator_service
    DEFAULT_TARGET_LANGUAGE = new_config.processing.target_language
    DEEPLX_API_ENDPOINT = new_config.processing.deeplx_api_endpoint

    # 文件前缀和后缀
    RAW_SUBTITLE_PREFIX = new_config.files.raw_subtitle_prefix
    SPLIT_SUBTITLE_PREFIX = new_config.files.split_subtitle_prefix
    OPTIMIZED_SUBTITLE_PREFIX = new_config.files.optimized_subtitle_prefix
    FINAL_TEXT_PREFIX = new_config.files.final_text_prefix
    TRANSLATED_FILE_PREFIX = new_config.files.translated_file_prefix
    SUBTITLE_SUFFIX = new_config.files.subtitle_suffix
    TEXT_SUFFIX = new_config.files.text_suffix
else:
    # 回退到旧配置
    DEFAULT_TRANSCRIBE_MODEL_NAME = "BIJIAN"  # 修改默认模型为必剪（速度快且稳定）
    DEFAULT_TRANSCRIBE_LANGUAGE = "en"
    DEFAULT_USE_ASR_CACHE = True
    DEFAULT_NEED_WORD_TIMESTAMP = True




    SPLITTER_MODEL_NAME = "gemini-2.5-flash-preview-04-17"
    SPLITTER_TEMPERATURE = 0.3
    SPLITTER_TIMEOUT = 60
    SPLITTER_RETRY_TIMES = 1
    SPLITTER_THREAD_NUM = 1  # 降低并发数，使进度更新更平滑

    OPTIMIZER_MODEL_NAME = "gemini-2.5-flash-preview-04-17"
    OPTIMIZER_BATCH_NUM = 1  # 降低批处理大小，使进度更新更平滑
    OPTIMIZER_THREAD_NUM = 1  # 降低并发数，使进度更新更平滑

    TRANSLATOR_MODEL_NAME = "gemini-2.5-flash-preview-04-17"
    TRANSLATOR_THREAD_NUM = 1
    TRANSLATOR_BATCH_NUM = 1  # 降低批处理大小，使进度更新更平滑
    DEFAULT_TRANSLATOR_SERVICE_NAME = "OPENAI"
    DEFAULT_TARGET_LANGUAGE = "Chinese"
    DEEPLX_API_ENDPOINT = os.getenv("DEEPLX_API_ENDPOINT")

    RAW_SUBTITLE_PREFIX = "【原始字幕】"
    SPLIT_SUBTITLE_PREFIX = "【断句字幕】"
    OPTIMIZED_SUBTITLE_PREFIX = "【优化字幕】"
    FINAL_TEXT_PREFIX = "【最终纯文字】"
    TRANSLATED_FILE_PREFIX = "【翻译】"
    SUBTITLE_SUFFIX = ".srt"
    TEXT_SUFFIX = ".txt"

# ASR配置 (保持向后兼容)
ASR_LANGUAGE = "zh"
ASR_ENABLE_CACHE = True

# 创建必要的路径
for p in [CACHE_PATH, LOG_PATH, WORK_PATH, MODEL_PATH]:
    if p and not p.exists():
        p.mkdir(parents=True, exist_ok=True)
