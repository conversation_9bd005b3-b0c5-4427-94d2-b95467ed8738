# -*- coding: utf-8 -*-
"""
错误处理工具模块

提供错误处理装饰器、错误转换函数和错误报告功能。
"""
import functools
import logging
# 导入新的统一日志系统
from utils.universal_logging import get_logger, ensure_trace_id, LogContext, set_trace_id
from typing import Callable, Any, Dict, Optional, Type
import grpc
from .exceptions import (
    SubtitleProcessingError, 
    APIError, 
    NetworkError, 
    TimeoutError,
    ConfigurationError
)

logger = get_logger(__name__)


def handle_subtitle_errors(
    default_error_message: str = "处理过程中发生未知错误",
    reraise: bool = False,
    log_errors: bool = True
):
    """
    字幕处理错误处理装饰器
    
    Args:
        default_error_message: 默认错误信息
        reraise: 是否重新抛出异常
        log_errors: 是否记录错误日志
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except SubtitleProcessingError as e:
                # 已知的字幕处理错误，直接处理
                if log_errors:
                    logger.error(
                        f"字幕处理错误 in {func.__name__}: {e.message}",
                        extra={
                            "error_code": e.error_code,
                            "details": e.details,
                            "function": func.__name__
                        }
                    )
                if reraise:
                    raise
                return _create_error_response(e)
            except Exception as e:
                # 未知错误，转换为字幕处理错误
                error = SubtitleProcessingError(
                    message=str(e),
                    error_code="UNKNOWN_ERROR",
                    details={
                        "original_exception": type(e).__name__,
                        "function": func.__name__
                    },
                    user_message=default_error_message
                )
                
                if log_errors:
                    logger.error(
                        f"未知错误 in {func.__name__}: {str(e)}",
                        exc_info=True,
                        extra={
                            "function": func.__name__,
                            "original_exception": type(e).__name__
                        }
                    )
                
                if reraise:
                    raise error
                return _create_error_response(error)
        
        return wrapper
    return decorator


def handle_grpc_errors(func: Callable) -> Callable:
    """gRPC错误处理装饰器"""
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except SubtitleProcessingError as e:
            # 转换为gRPC错误
            logger.error(f"gRPC服务错误: {e.message}", extra={"grpc_error": e.to_dict()})
            
            # 根据错误类型选择gRPC状态码
            if isinstance(e, ConfigurationError):
                raise grpc.RpcError(grpc.StatusCode.FAILED_PRECONDITION, e.user_message)
            elif isinstance(e, APIError):
                raise grpc.RpcError(grpc.StatusCode.UNAVAILABLE, e.user_message)
            elif isinstance(e, NetworkError):
                raise grpc.RpcError(grpc.StatusCode.UNAVAILABLE, e.user_message)
            elif isinstance(e, TimeoutError):
                raise grpc.RpcError(grpc.StatusCode.DEADLINE_EXCEEDED, e.user_message)
            else:
                raise grpc.RpcError(grpc.StatusCode.INTERNAL, e.user_message)
        except Exception as e:
            logger.error(f"gRPC服务未知错误: {str(e)}", exc_info=True)
            raise grpc.RpcError(grpc.StatusCode.INTERNAL, "服务内部错误")
    
    return wrapper


def convert_exception_to_subtitle_error(
    exception: Exception,
    context: str = None,
    default_message: str = "处理失败"
) -> SubtitleProcessingError:
    """
    将标准异常转换为字幕处理错误
    
    Args:
        exception: 原始异常
        context: 错误上下文
        default_message: 默认错误信息
    """
    exception_type = type(exception).__name__
    exception_message = str(exception)
    
    # 根据异常类型进行转换
    if "timeout" in exception_message.lower() or "TimeoutError" in exception_type:
        return TimeoutError(
            message=exception_message,
            details={"original_exception": exception_type, "context": context}
        )
    elif "network" in exception_message.lower() or "ConnectionError" in exception_type:
        return NetworkError(
            message=exception_message,
            details={"original_exception": exception_type, "context": context}
        )
    elif "api" in exception_message.lower() or "HTTPError" in exception_type:
        return APIError(
            message=exception_message,
            details={"original_exception": exception_type, "context": context}
        )
    else:
        return SubtitleProcessingError(
            message=exception_message,
            error_code=f"CONVERTED_{exception_type.upper()}",
            details={"original_exception": exception_type, "context": context},
            user_message=default_message
        )


def _create_error_response(error: SubtitleProcessingError) -> Dict[str, Any]:
    """创建错误响应"""
    return {
        "success": False,
        "error": error.to_dict(),
        "message": error.user_message
    }


def safe_execute(
    func: Callable,
    *args,
    default_return=None,
    error_message: str = "操作失败",
    log_errors: bool = True,
    **kwargs
) -> Any:
    """
    安全执行函数，捕获并处理异常
    
    Args:
        func: 要执行的函数
        *args: 函数参数
        default_return: 发生错误时的默认返回值
        error_message: 错误信息
        log_errors: 是否记录错误
        **kwargs: 函数关键字参数
    """
    try:
        return func(*args, **kwargs)
    except Exception as e:
        if log_errors:
            logger.error(f"安全执行失败 {func.__name__}: {str(e)}", exc_info=True)
        
        if isinstance(e, SubtitleProcessingError):
            raise
        else:
            raise convert_exception_to_subtitle_error(
                e, 
                context=f"safe_execute:{func.__name__}",
                default_message=error_message
            )


class ErrorReporter:
    """错误报告器"""
    
    def __init__(self, logger_name: str = __name__):
        self.logger = get_logger(logger_name)
    
    def report_error(
        self,
        error: SubtitleProcessingError,
        context: Dict[str, Any] = None,
        notify_user: bool = True
    ):
        """
        报告错误
        
        Args:
            error: 错误对象
            context: 错误上下文
            notify_user: 是否通知用户
        """
        error_data = error.to_dict()
        if context:
            error_data["context"] = context
        
        # 记录错误日志
        self.logger.error(
            f"错误报告: {error.message}",
            extra={"error_report": error_data}
        )
        
        # 如果需要通知用户，可以在这里添加通知逻辑
        if notify_user:
            self._notify_user(error)
    
    def _notify_user(self, error: SubtitleProcessingError):
        """通知用户错误 (可扩展)"""
        # 这里可以添加用户通知逻辑，比如发送到前端
        pass
    
    def report_performance_issue(
        self,
        operation: str,
        duration: float,
        threshold: float = 30.0
    ):
        """报告性能问题"""
        if duration > threshold:
            self.logger.warning(
                f"性能警告: {operation} 耗时 {duration:.2f}秒 (阈值: {threshold}秒)",
                extra={
                    "operation": operation,
                    "duration": duration,
                    "threshold": threshold,
                    "performance_issue": True
                }
            )


# 全局错误报告器实例
error_reporter = ErrorReporter()


def create_progress_error(
    stage_name: str,
    error: SubtitleProcessingError,
    percentage: int = 0
) -> Dict[str, Any]:
    """创建进度错误响应"""
    return {
        "stage_name": stage_name,
        "percentage": percentage,
        "message": error.user_message,
        "is_error": True,
        "error_message": error.message,
        "error_code": error.error_code,
        "error_details": error.details,
        "recoverable": error.recoverable
    }


def validate_required_config(config_dict: Dict[str, Any], required_keys: list) -> None:
    """
    验证必需的配置项
    
    Args:
        config_dict: 配置字典
        required_keys: 必需的配置键列表
    
    Raises:
        ConfigurationError: 当缺少必需配置时
    """
    missing_keys = []
    for key in required_keys:
        if key not in config_dict or config_dict[key] is None:
            missing_keys.append(key)
    
    if missing_keys:
        raise ConfigurationError(
            message=f"缺少必需的配置项: {', '.join(missing_keys)}",
            details={"missing_keys": missing_keys, "provided_keys": list(config_dict.keys())}
        )
