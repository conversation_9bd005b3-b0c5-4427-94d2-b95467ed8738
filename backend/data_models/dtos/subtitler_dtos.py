# backend/data_models/dtos/subtitler_dtos.py
from typing import List, Optional, Dict, Any, Union
from pydantic import BaseModel, Field, ConfigDict

from data_models.dtos.dto_common import OperationStatusEnum, ErrorDetailDto, BackendStageSpecificErrorDetail

# Forward declarations for Pydantic models used in ProgressUpdate oneof
class VideoToAudioResponse(BaseModel):
    model_config = ConfigDict(from_attributes=True)
    audio_path: str
    audio_data: bytes
    trace_id: Optional[str] = None

class TimestampedTextSegment(BaseModel):
    model_config = ConfigDict(from_attributes=True)
    text: str
    start_time_ms: Optional[int] = None # As per specific instruction for this DTO
    end_time_ms: Optional[int] = None   # As per specific instruction for this DTO
    status: Optional[OperationStatusEnum] = None # Message type
    error_detail: Optional[ErrorDetailDto] = None # Message type

class AudioToTextResponse(BaseModel):
    model_config = ConfigDict(from_attributes=True)
    segments: List[TimestampedTextSegment] = Field(default_factory=list)
    trace_id: Optional[str] = None
    total_segments_processed: int
    successful_segments: int
    failed_segments: int
    transcript: Optional[str] = None  # 添加完整的转录文本字段

class GenerateSubtitlesResponse(BaseModel):
    model_config = ConfigDict(from_attributes=True)
    srt_content: str
    ass_content: str
    trace_id: Optional[str] = None

class TranslatedSegmentDto(BaseModel): # Renamed from TranslatedSegmentResult and updated
    model_config = ConfigDict(from_attributes=True)
    segment_id: str
    original_text: str
    translated_text: str
    status: Optional[OperationStatusEnum] = None
    error_detail: Optional[ErrorDetailDto] = None
    start_time_ms: Optional[int] = None
    end_time_ms: Optional[int] = None

class TranslateSubtitlesResponse(BaseModel):
    model_config = ConfigDict(from_attributes=True)
    translated_subtitle_content: str
    trace_id: Optional[str] = None
    segment_results: List[TranslatedSegmentDto] = Field(default_factory=list) # Updated to TranslatedSegmentDto
    total_segments_processed: int
    successful_segments: int
    failed_segments: int

# ---- New DTOs for granular Translate RPC ----
class TextToTranslateDto(BaseModel):
    model_config = ConfigDict(from_attributes=True)
    segment_id: str
    text_to_translate: str

class TranslateRequestDto(BaseModel):
    model_config = ConfigDict(from_attributes=True)
    task_id: str
    text_segments: List[TextToTranslateDto] = Field(default_factory=list)
    source_language: Optional[str] = None
    target_language: str
    trace_id: Optional[str] = None

class TranslateResponseDto(BaseModel):
    model_config = ConfigDict(from_attributes=True)
    task_id: str
    translated_segments: List[TranslatedSegmentDto] = Field(default_factory=list)
    error_detail: Optional[ErrorDetailDto] = None
    trace_id: Optional[str] = None
# ---- End new DTOs ----

class ProcessVideoToTranslatedSubtitlesResponse(BaseModel):
    model_config = ConfigDict(from_attributes=True)
    translated_subtitle_content: str
    trace_id: Optional[str] = None

class ProgressUpdate(BaseModel):
    model_config = ConfigDict(from_attributes=True)
    trace_id: Optional[str] = None
    stage_name: str
    percentage: int
    message: str
    status: Optional[OperationStatusEnum] = None # Message type
    error_detail: Optional[ErrorDetailDto] = None # Message type, field name in proto is error_detail
    # 注意：移除了 data 字段，所有结果数据现在通过 oneof final_result 字段传递

    # For oneof final_result - all are message types, hence Optional
    video_to_audio_response: Optional[VideoToAudioResponse] = None
    audio_to_text_response: Optional[AudioToTextResponse] = None
    generate_subtitles_response: Optional[GenerateSubtitlesResponse] = None
    translate_subtitles_response: Optional[TranslateSubtitlesResponse] = None
    process_video_to_translated_subtitles_response: Optional[ProcessVideoToTranslatedSubtitlesResponse] = None
    
    # Deprecated fields
    is_error: Optional[bool] = Field(None, deprecated=True)
    error_message: Optional[str] = Field(None, deprecated=True)


class VideoToAudioRequest(BaseModel):
    model_config = ConfigDict(from_attributes=True)
    video_path: str
    trace_id: Optional[str] = None

class AudioToTextRequest(BaseModel):
    model_config = ConfigDict(from_attributes=True)
    audio_path: str
    audio_data: bytes
    request_word_timestamps: bool
    skip_cache: bool
    model: Optional[str] = None  # 转录模型 (BIJIAN, JIANYING)
    language: Optional[str] = None  # 语言代码 (zh, en, etc.)
    trace_id: Optional[str] = None

class GenerateSubtitlesRequest(BaseModel):
    model_config = ConfigDict(from_attributes=True)
    text: str
    audio_path: str
    skip_cache: bool
    trace_id: Optional[str] = None

class TranslateSubtitlesRequest(BaseModel):
    model_config = ConfigDict(from_attributes=True)
    subtitle_content: str
    target_language: str
    skip_cache: bool
    trace_id: Optional[str] = None

class ProcessVideoToTranslatedSubtitlesRequest(BaseModel):
    model_config = ConfigDict(from_attributes=True)
    video_path: str
    target_language: str
    trace_id: Optional[str] = None

class SubtitleSegment(BaseModel):
    model_config = ConfigDict(from_attributes=True)
    start_time: int # ms
    end_time: int # ms
    original_text: str
    translated_text: str
    status: Optional[OperationStatusEnum] = None # Message type
    error_detail: Optional[ErrorDetailDto] = None # Message type

class SaveSubtitleRequest(BaseModel):
    model_config = ConfigDict(from_attributes=True)
    subtitle_content: str
    format: str
    layout: str
    file_name: str
    original_content: str
    translated_content: str
    segments: List[SubtitleSegment] = Field(default_factory=list)
    auto_save_to_default: bool
    ass_style_options: Optional[Dict[str, Any]] = None # For google.protobuf.Struct (message type)
    trace_id: Optional[str] = None

class SaveSubtitleResponse(BaseModel):
    model_config = ConfigDict(from_attributes=True)
    file_path: str
    file_data: bytes
    file_name: str
    file_size: int # int64 in proto, pydantic uses int
    saved_to_default: bool
    format: str
    layout: str
    content_source: str
    original_filename_or_title: str
    trace_id: Optional[str] = None
    status: Optional[OperationStatusEnum] = None # Message type
    error_detail: Optional[ErrorDetailDto] = None # Message type

class BatchSaveSubtitleRequest(BaseModel):
    model_config = ConfigDict(from_attributes=True)
    formats: List[str] = Field(default_factory=list)
    layouts: List[str] = Field(default_factory=list)
    content_sources: List[str] = Field(default_factory=list)
    file_name_prefix: str
    original_content: str
    translated_content: str
    segments: List[SubtitleSegment] = Field(default_factory=list)
    auto_save_to_default: bool
    translation_requested: bool
    ass_style_options: Optional[Dict[str, Any]] = None # For google.protobuf.Struct (message type)
    trace_id: Optional[str] = None

class BatchSaveSubtitleResponse(BaseModel):
    model_config = ConfigDict(from_attributes=True)
    files: List[SaveSubtitleResponse] = Field(default_factory=list)
    trace_id: Optional[str] = None
    status: Optional[OperationStatusEnum] = None # Message type
    error_detail: Optional[ErrorDetailDto] = None # Message type

class ClearCacheRequest(BaseModel):
    model_config = ConfigDict(from_attributes=True)
    cache_type: str
    trace_id: Optional[str] = None

class ClearCacheResponse(BaseModel):
    model_config = ConfigDict(from_attributes=True)
    success: bool
    message: str
    trace_id: Optional[str] = None
    status: Optional[OperationStatusEnum] = None # Message type
    error_detail: Optional[ErrorDetailDto] = None # Message type

class BackendVideoMetadata(BaseModel):
    """后端视频元数据DTO，包含错误和回退数据支持"""
    model_config = ConfigDict(from_attributes=True)
    
    duration: Optional[float] = None
    resolution: Optional[str] = None
    codec: Optional[str] = None
    bitrate: Optional[int] = None
    framerate: Optional[float] = None
    
    # 错误和回退支持
    error: Optional[BackendStageSpecificErrorDetail] = None
    is_fallback_data: bool = False
    fallback_reason: Optional[str] = None

class BackendKeyframeExtractionResult(BaseModel):
    """后端关键帧提取结果DTO"""
    model_config = ConfigDict(from_attributes=True)
    
    keyframe_timestamps: Optional[List[float]] = None
    preview_image_paths: Optional[List[str]] = None
    extracted_count: int = 0
    target_count: int = 10
    
    # 错误和回退支持
    error: Optional[BackendStageSpecificErrorDetail] = None
    is_fallback_data: bool = False
    fallback_reason: Optional[str] = None

class BackendAudioExtractionResult(BaseModel):
    """后端音频提取结果DTO"""
    model_config = ConfigDict(from_attributes=True)
    
    audio_path: Optional[str] = None
    duration: Optional[float] = None
    sample_rate: Optional[int] = None
    channels: Optional[int] = None
    format: Optional[str] = None
    
    # 错误和回退支持
    error: Optional[BackendStageSpecificErrorDetail] = None
    is_fallback_data: bool = False
    fallback_reason: Optional[str] = None

class BackendProgressUpdate(BaseModel):
    """增强的后端进度更新DTO - 重构后移除data字段"""
    model_config = ConfigDict(from_attributes=True)

    trace_id: Optional[str] = None
    stage_name: str
    percentage: int
    message: str
    status: OperationStatusEnum

    # 顶层错误，用于致命或全局性错误
    error_detail: Optional[ErrorDetailDto] = None

    # 注意：移除了 data 字段以避免与 oneof final_result 冲突
    # 所有结构化数据现在通过类型安全的 final_result 字段传递

    # 数据状态标识（保留用于向后兼容）
    data_status: Optional[str] = None  # "PARTIAL_SUCCESS_WITH_FALLBACK", "STAGE_ERROR", "COMPLETE"

    # 最终结果 - 现在是唯一的数据传递方式
    final_result: Optional[Union[
        AudioToTextResponse,
        BackendVideoMetadata,
        BackendKeyframeExtractionResult,
        BackendAudioExtractionResult,
        # 其他响应DTO类型...
        Dict[str, Any]
    ]] = None
    final_result_type: Optional[str] = None

# Update forward references
# ErrorDetailDto is now imported, so no need to forward declare 'ErrorDetail'
# Pydantic v2 automatically handles forward references in most cases if type hints are strings
# However, explicit model_rebuild can be useful for complex scenarios or older Pydantic versions.
# For now, let's assume Pydantic v2 handles it. If not, these can be added back.
# TimestampedTextSegment.model_rebuild()
# TranslatedSegmentDto.model_rebuild() # Updated from TranslatedSegmentResult
# ProgressUpdate.model_rebuild()
# SubtitleSegment.model_rebuild()
# TranslateRequestDto.model_rebuild() # Added
# TranslateResponseDto.model_rebuild() # Added
# SaveSubtitleResponse.model_rebuild()
# BatchSaveSubtitleResponse.model_rebuild()
# ClearCacheResponse.model_rebuild()

# If using Pydantic v1 or encountering issues, uncomment and adjust the model_rebuild calls:
# Make sure ErrorDetailDto is correctly referenced if it were a string forward ref.
# Since it's directly imported, direct type hints like Optional[ErrorDetailDto] should work.