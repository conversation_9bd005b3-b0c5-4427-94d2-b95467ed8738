# backend/ai_config_service.py
import logging
# 导入新的统一日志系统
from utils.universal_logging import get_logger, ensure_trace_id, LogContext, set_trace_id
import grpc

from api_protos.v1.ai_config import ai_config_service_pb2
from api_protos.v1.ai_config import ai_config_service_pb2_grpc
import ai_config_manager
from data_models.dtos.ai_config_dtos import (
    UpdateAIConfigurationsRequestDto,  # Renamed for clarity
    UpdateAIConfigurationsResponseDto, # Renamed for clarity
    AIProviderConfigDto  # Assuming this is the Pydantic DTO for AIProviderConfigProto
)
from data_models.dtos.dto_common import ErrorDetailDto
from utils.conversion_utils import (
    convert_proto_to_dto,
    convert_dto_to_proto,
    ProtoToDtoConversionError,
    DtoToProtoConversionError
)

logger = get_logger(__name__)

class AIConfigurationServiceServicer(ai_config_service_pb2_grpc.AIConfigurationServiceServicer):
    """
    gRPC 服务实现，用于管理 AI 服务配置。
    """
    def UpdateAIConfigurations(self, request: ai_config_service_pb2.UpdateAIConfigurationsRequest, context: grpc.ServicerContext) -> ai_config_service_pb2.UpdateAIConfigurationsResponse:
        """
        接收前端推送的 AI 服务配置列表，并更新到 ai_config_manager。
        内部使用 Pydantic DTO 进行数据处理。
        """
        # 获取trace_id用于全链路追踪 - 修复：正确提取trace_id
        metadata = context.invocation_metadata()
        trace_id_from_metadata = None
        if metadata:
            for key, value in metadata:
                if key.lower() == 'trace-id' or key.lower() == 'x-trace-id':
                    trace_id_from_metadata = value
                    break
        trace_id = ensure_trace_id(trace_id_from_metadata, "UpdateAIConfigurations")
        
        logger.info(f"🔥 [UpdateAIConfigurations] 开始处理配置更新请求 | trace_id: {trace_id}")
        # 📊 请求数据流分析
        logger.info(f"📊 [UpdateAIConfigurations] 请求分析:")
        logger.info(f"   - 配置数量: {len(request.configs)}")
        logger.info(f"   - 客户端信息: {context.peer()}")
        
        # 详细记录每个配置的基本信息（不包含敏感数据）
        for i, config in enumerate(request.configs):
            logger.info(f"   - 配置#{i+1}: ID={config.provider_id}, Type={config.provider_type}, Enabled={config.is_enabled}")
        
        response_dto: UpdateAIConfigurationsResponseDto
        try:
            # B. 请求处理 (Proto -> DTO)
            try:
                request_dto = convert_proto_to_dto(request, UpdateAIConfigurationsRequestDto)
            except ProtoToDtoConversionError as e:
                logger.error(f"Proto to DTO conversion error in UpdateAIConfigurations: {e}", exc_info=True)
                context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                context.set_details(f"Invalid request format: {str(e)}")
                # Return protobuf response directly
                return ai_config_service_pb2.UpdateAIConfigurationsResponse(
                    success=False,
                    message=f"Invalid request format: {str(e)}"
                )

            logger.info(f"Received UpdateAIConfigurations request with {len(request_dto.configs)} configurations (DTO).")

            if not request_dto.configs:
                logger.warning("UpdateAIConfigurations called with an empty list of configs (DTO).")
                # ai_config_manager.update_configurations will handle empty list if applicable

            # C. Use original proto objects directly - no conversion needed
            # This fixes the synchronization issue where DTO->Proto conversion was losing data
            ai_config_manager.update_configurations(list(request.configs))

            # Log details of received configs for debugging (from DTO)
            for config_dto_log in request_dto.configs:
                logger.info(f"Processed config (DTO) - ID: {config_dto_log.provider_id}, Type: {config_dto_log.provider_type}, Enabled: {config_dto_log.is_enabled}, Name: {config_dto_log.display_name}")
                if hasattr(config_dto_log, 'credentials') and config_dto_log.credentials:
                    logger.info(f"  Credentials keys: {list(config_dto_log.credentials.keys())}")
                if hasattr(config_dto_log, 'attributes') and config_dto_log.attributes:
                    logger.info(f"  Attributes: {dict(config_dto_log.attributes)}")


            # Return protobuf response directly
            return ai_config_service_pb2.UpdateAIConfigurationsResponse(
                success=True,
                message="AI configurations updated successfully."
            )

        except DtoToProtoConversionError as e: # Catch conversion error for the final response
            logger.error(f"DTO to Proto conversion error for final response in UpdateAIConfigurations: {e}", exc_info=True)
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Error preparing response: {str(e)}")
            # Fallback to a basic proto response if DTO to Proto fails for the response itself
            return ai_config_service_pb2.UpdateAIConfigurationsResponse(
                success=False,
                message=f"Internal server error preparing response: {str(e)}"
            )
        except Exception as e:
            logger.error(f"Error processing UpdateAIConfigurations: {e}", exc_info=True)
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Internal server error: {e}")
            
            # ErrorDetailDto is for internal use or if the DTO had an error field.
            # The proto response doesn't have it, so it's mainly for logging or if the DTO was richer.
            return ai_config_service_pb2.UpdateAIConfigurationsResponse(
                success=False,
                message=f"Failed to update AI configurations: {e}"
            )

    def GetAIConfigurations(self, request: ai_config_service_pb2.GetAIConfigurationsRequest, context: grpc.ServicerContext) -> ai_config_service_pb2.GetAIConfigurationsResponse:
        """
        获取当前存储的AI配置列表
        """
        # 获取trace_id用于全链路追踪 - 修复：正确提取trace_id
        metadata = context.invocation_metadata()
        trace_id_from_metadata = None
        if metadata:
            for key, value in metadata:
                if key.lower() == 'trace-id' or key.lower() == 'x-trace-id':
                    trace_id_from_metadata = value
                    break
        trace_id = ensure_trace_id(trace_id_from_metadata, "GetAIConfigurations")
        
        logger.info(f"🔥 [GetAIConfigurations] 开始获取配置请求 | trace_id: {trace_id}")
        logger.info(f"📊 [GetAIConfigurations] 请求参数:")
        logger.info(f"   - provider_type过滤: '{request.provider_type}' (空表示获取所有)")
        logger.info(f"   - 仅启用配置: {request.enabled_only}")
        
        try:
            # 获取所有配置
            all_configs = ai_config_manager.get_all_configs()
            logger.info(f"📊 [GetAIConfigurations] 获取到 {len(all_configs)} 个配置")
            
            # 构建响应配置列表
            response_configs = []
            
            for config_id, config in all_configs.items():
                # 应用过滤条件
                should_include = True
                
                # 按类型过滤
                if request.provider_type:
                    config_type = getattr(config, 'provider_type', '')
                    if config_type != request.provider_type:
                        should_include = False
                        continue
                
                # 按启用状态过滤
                if request.enabled_only:
                    is_enabled = getattr(config, 'is_enabled', False)
                    if not is_enabled:
                        should_include = False
                        continue
                
                if should_include:
                    # 转换为Proto对象
                    try:
                        if hasattr(config, 'provider_id'):
                            # 如果config是一个具有属性的对象
                            proto_config = ai_config_service_pb2.AIProviderConfigProto()
                            proto_config.provider_id = config.provider_id
                            proto_config.provider_type = config.provider_type
                            proto_config.display_name = config.display_name
                            proto_config.is_enabled = config.is_enabled
                            
                            # 处理credentials
                            if hasattr(config, 'credentials') and config.credentials:
                                for key, value in config.credentials.items():
                                    proto_config.credentials[key] = str(value)
                            
                            # 处理attributes
                            if hasattr(config, 'attributes') and config.attributes:
                                for key, value in config.attributes.items():
                                    proto_config.attributes[key] = str(value)
                            
                            # 处理metadata
                            if hasattr(config, 'metadata') and config.metadata:
                                for key, value in config.metadata.items():
                                    proto_config.metadata[key] = str(value)
                            
                            response_configs.append(proto_config)
                            logger.info(f"✅ [GetAIConfigurations] 包含配置: {config_id} ({config.provider_type}, enabled={config.is_enabled})")
                        else:
                            logger.warning(f"⚠️ [GetAIConfigurations] 配置 {config_id} 格式不正确，跳过")
                    
                    except Exception as e:
                        logger.error(f"❌ [GetAIConfigurations] 转换配置 {config_id} 时出错: {e}")
                        continue
            
            # 构建响应
            response = ai_config_service_pb2.GetAIConfigurationsResponse()
            response.success = True
            response.message = f"Successfully retrieved {len(response_configs)} configurations"
            response.configs.extend(response_configs)
            
            logger.info(f"🎉 [GetAIConfigurations] 成功返回 {len(response_configs)} 个配置 | trace_id: {trace_id}")
            return response
            
        except Exception as e:
            logger.error(f"❌ [GetAIConfigurations] 获取配置时发生错误: {e} | trace_id: {trace_id}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Internal server error: {e}")
            
            return ai_config_service_pb2.GetAIConfigurationsResponse(
                success=False,
                message=f"Failed to get configurations: {e}",
                configs=[]
            )

    def Ping(self, request: ai_config_service_pb2.PingRequest, context: grpc.ServicerContext) -> ai_config_service_pb2.PingResponse:
        """
        处理来自客户端的 Ping 请求，返回服务状态和配置信息
        """
        # 获取trace_id用于全链路追踪
        metadata = context.invocation_metadata()
        trace_id_from_metadata = None
        if metadata:
            for key, value in metadata:
                if key.lower() == 'trace-id' or key.lower() == 'x-trace-id':
                    trace_id_from_metadata = value
                    break
        trace_id = ensure_trace_id(trace_id_from_metadata, "Ping")
        
        logger.info(f"🏓 [Ping] 收到健康检查请求 | trace_id: {trace_id}")
        logger.info(f"📊 [Ping] 客户端信息: {request.client_version}")
        
        try:
            # 获取服务状态信息
            server_start_time = ai_config_manager.get_server_start_time()
            has_configs = ai_config_manager.has_configs()
            
            logger.info(f"📊 [Ping] 服务状态:")
            logger.info(f"   - 启动时间: {server_start_time}")
            logger.info(f"   - 有配置: {has_configs}")
            
            # 构建响应
            response = ai_config_service_pb2.PingResponse()
            response.success = True
            response.message = "Pong - AI Config Service is healthy"
            response.server_start_time = server_start_time
            response.has_configs = has_configs
            
            logger.info(f"🎉 [Ping] 健康检查成功 | trace_id: {trace_id}")
            return response
            
        except Exception as e:
            logger.error(f"❌ [Ping] 健康检查时发生错误: {e} | trace_id: {trace_id}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Internal server error: {e}")
            
            return ai_config_service_pb2.PingResponse(
                success=False,
                message=f"Ping failed: {e}",
                server_start_time=0,
                has_configs=False
            )