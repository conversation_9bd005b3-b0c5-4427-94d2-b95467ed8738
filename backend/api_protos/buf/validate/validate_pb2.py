# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: api-protos/buf/validate/validate.proto
# Protobuf Python Version: 4.25.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import descriptor_pb2 as google_dot_protobuf_dot_descriptor__pb2
from google.protobuf import duration_pb2 as google_dot_protobuf_dot_duration__pb2
from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n&api-protos/buf/validate/validate.proto\x12\x0c\x62uf.validate\x1a google/protobuf/descriptor.proto\x1a\x1egoogle/protobuf/duration.proto\x1a\x1fgoogle/protobuf/timestamp.proto\"7\n\x04Rule\x12\n\n\x02id\x18\x01 \x01(\t\x12\x0f\n\x07message\x18\x02 \x01(\t\x12\x12\n\nexpression\x18\x03 \x01(\t\"A\n\x0cMessageRules\x12\x10\n\x08\x64isabled\x18\x01 \x01(\x08\x12\x1f\n\x03\x63\x65l\x18\x03 \x03(\x0b\x32\x12.buf.validate.Rule\"\x1e\n\nOneofRules\x12\x10\n\x08required\x18\x01 \x01(\x08\"\xbf\x08\n\nFieldRules\x12\x1f\n\x03\x63\x65l\x18\x17 \x03(\x0b\x32\x12.buf.validate.Rule\x12\x10\n\x08required\x18\x19 \x01(\x08\x12$\n\x06ignore\x18\x1b \x01(\x0e\x32\x14.buf.validate.Ignore\x12)\n\x05\x66loat\x18\x01 \x01(\x0b\x32\x18.buf.validate.FloatRulesH\x00\x12+\n\x06\x64ouble\x18\x02 \x01(\x0b\x32\x19.buf.validate.DoubleRulesH\x00\x12)\n\x05int32\x18\x03 \x01(\x0b\x32\x18.buf.validate.Int32RulesH\x00\x12)\n\x05int64\x18\x04 \x01(\x0b\x32\x18.buf.validate.Int64RulesH\x00\x12+\n\x06uint32\x18\x05 \x01(\x0b\x32\x19.buf.validate.UInt32RulesH\x00\x12+\n\x06uint64\x18\x06 \x01(\x0b\x32\x19.buf.validate.UInt64RulesH\x00\x12+\n\x06sint32\x18\x07 \x01(\x0b\x32\x19.buf.validate.SInt32RulesH\x00\x12+\n\x06sint64\x18\x08 \x01(\x0b\x32\x19.buf.validate.SInt64RulesH\x00\x12-\n\x07\x66ixed32\x18\t \x01(\x0b\x32\x1a.buf.validate.Fixed32RulesH\x00\x12-\n\x07\x66ixed64\x18\n \x01(\x0b\x32\x1a.buf.validate.Fixed64RulesH\x00\x12/\n\x08sfixed32\x18\x0b \x01(\x0b\x32\x1b.buf.validate.SFixed32RulesH\x00\x12/\n\x08sfixed64\x18\x0c \x01(\x0b\x32\x1b.buf.validate.SFixed64RulesH\x00\x12\'\n\x04\x62ool\x18\r \x01(\x0b\x32\x17.buf.validate.BoolRulesH\x00\x12+\n\x06string\x18\x0e \x01(\x0b\x32\x19.buf.validate.StringRulesH\x00\x12)\n\x05\x62ytes\x18\x0f \x01(\x0b\x32\x18.buf.validate.BytesRulesH\x00\x12\'\n\x04\x65num\x18\x10 \x01(\x0b\x32\x17.buf.validate.EnumRulesH\x00\x12/\n\x08repeated\x18\x12 \x01(\x0b\x32\x1b.buf.validate.RepeatedRulesH\x00\x12%\n\x03map\x18\x13 \x01(\x0b\x32\x16.buf.validate.MapRulesH\x00\x12%\n\x03\x61ny\x18\x14 \x01(\x0b\x32\x16.buf.validate.AnyRulesH\x00\x12/\n\x08\x64uration\x18\x15 \x01(\x0b\x32\x1b.buf.validate.DurationRulesH\x00\x12\x31\n\ttimestamp\x18\x16 \x01(\x0b\x32\x1c.buf.validate.TimestampRulesH\x00\x42\x06\n\x04typeJ\x04\x08\x18\x10\x19J\x04\x08\x1a\x10\x1bR\x07skippedR\x0cignore_empty\"S\n\x0fPredefinedRules\x12\x1f\n\x03\x63\x65l\x18\x01 \x03(\x0b\x32\x12.buf.validate.RuleJ\x04\x08\x18\x10\x19J\x04\x08\x1a\x10\x1bR\x13skippedignore_empty\"\xda\x17\n\nFloatRules\x12\x83\x01\n\x05\x63onst\x18\x01 \x01(\x02\x42t\xc2Hq\no\n\x0b\x66loat.const\x1a`this != getField(rules, \'const\') ? \'value must equal %s\'.format([getField(rules, \'const\')]) : \'\'\x12\x9f\x01\n\x02lt\x18\x02 \x01(\x02\x42\x90\x01\xc2H\x8c\x01\n\x89\x01\n\x08\x66loat.lt\x1a}!has(rules.gte) && !has(rules.gt) && (this.isNan() || this >= rules.lt)? \'value must be less than %s\'.format([rules.lt]) : \'\'H\x00\x12\xaf\x01\n\x03lte\x18\x03 \x01(\x02\x42\x9f\x01\xc2H\x9b\x01\n\x98\x01\n\tfloat.lte\x1a\x8a\x01!has(rules.gte) && !has(rules.gt) && (this.isNan() || this > rules.lte)? \'value must be less than or equal to %s\'.format([rules.lte]) : \'\'H\x00\x12\xef\x07\n\x02gt\x18\x04 \x01(\x02\x42\xe0\x07\xc2H\xdc\x07\n\x8d\x01\n\x08\x66loat.gt\x1a\x80\x01!has(rules.lt) && !has(rules.lte) && (this.isNan() || this <= rules.gt)? \'value must be greater than %s\'.format([rules.gt]) : \'\'\n\xc3\x01\n\x0b\x66loat.gt_lt\x1a\xb3\x01has(rules.lt) && rules.lt >= rules.gt && (this.isNan() || this >= rules.lt || this <= rules.gt)? \'value must be greater than %s and less than %s\'.format([rules.gt, rules.lt]) : \'\'\n\xcd\x01\n\x15\x66loat.gt_lt_exclusive\x1a\xb3\x01has(rules.lt) && rules.lt < rules.gt && (this.isNan() || (rules.lt <= this && this <= rules.gt))? \'value must be greater than %s or less than %s\'.format([rules.gt, rules.lt]) : \'\'\n\xd3\x01\n\x0c\x66loat.gt_lte\x1a\xc2\x01has(rules.lte) && rules.lte >= rules.gt && (this.isNan() || this > rules.lte || this <= rules.gt)? \'value must be greater than %s and less than or equal to %s\'.format([rules.gt, rules.lte]) : \'\'\n\xdd\x01\n\x16\x66loat.gt_lte_exclusive\x1a\xc2\x01has(rules.lte) && rules.lte < rules.gt && (this.isNan() || (rules.lte < this && this <= rules.gt))? \'value must be greater than %s or less than or equal to %s\'.format([rules.gt, rules.lte]) : \'\'H\x01\x12\xba\x08\n\x03gte\x18\x05 \x01(\x02\x42\xaa\x08\xc2H\xa6\x08\n\x9b\x01\n\tfloat.gte\x1a\x8d\x01!has(rules.lt) && !has(rules.lte) && (this.isNan() || this < rules.gte)? \'value must be greater than or equal to %s\'.format([rules.gte]) : \'\'\n\xd2\x01\n\x0c\x66loat.gte_lt\x1a\xc1\x01has(rules.lt) && rules.lt >= rules.gte && (this.isNan() || this >= rules.lt || this < rules.gte)? \'value must be greater than or equal to %s and less than %s\'.format([rules.gte, rules.lt]) : \'\'\n\xdc\x01\n\x16\x66loat.gte_lt_exclusive\x1a\xc1\x01has(rules.lt) && rules.lt < rules.gte && (this.isNan() || (rules.lt <= this && this < rules.gte))? \'value must be greater than or equal to %s or less than %s\'.format([rules.gte, rules.lt]) : \'\'\n\xe2\x01\n\rfloat.gte_lte\x1a\xd0\x01has(rules.lte) && rules.lte >= rules.gte && (this.isNan() || this > rules.lte || this < rules.gte)? \'value must be greater than or equal to %s and less than or equal to %s\'.format([rules.gte, rules.lte]) : \'\'\n\xec\x01\n\x17\x66loat.gte_lte_exclusive\x1a\xd0\x01has(rules.lte) && rules.lte < rules.gte && (this.isNan() || (rules.lte < this && this < rules.gte))? \'value must be greater than or equal to %s or less than or equal to %s\'.format([rules.gte, rules.lte]) : \'\'H\x01\x12\x7f\n\x02in\x18\x06 \x03(\x02\x42s\xc2Hp\nn\n\x08\x66loat.in\x1a\x62!(this in getField(rules, \'in\')) ? \'value must be in list %s\'.format([getField(rules, \'in\')]) : \'\'\x12v\n\x06not_in\x18\x07 \x03(\x02\x42\x66\xc2Hc\na\n\x0c\x66loat.not_in\x1aQthis in rules.not_in ? \'value must not be in list %s\'.format([rules.not_in]) : \'\'\x12u\n\x06\x66inite\x18\x08 \x01(\x08\x42\x65\xc2Hb\n`\n\x0c\x66loat.finite\x1aPrules.finite ? (this.isNan() || this.isInf() ? \'value must be finite\' : \'\') : \'\'\x12+\n\x07\x65xample\x18\t \x03(\x02\x42\x1a\xc2H\x17\n\x15\n\rfloat.example\x1a\x04true*\t\x08\xe8\x07\x10\x80\x80\x80\x80\x02\x42\x0b\n\tless_thanB\x0e\n\x0cgreater_than\"\xed\x17\n\x0b\x44oubleRules\x12\x84\x01\n\x05\x63onst\x18\x01 \x01(\x01\x42u\xc2Hr\np\n\x0c\x64ouble.const\x1a`this != getField(rules, \'const\') ? \'value must equal %s\'.format([getField(rules, \'const\')]) : \'\'\x12\xa0\x01\n\x02lt\x18\x02 \x01(\x01\x42\x91\x01\xc2H\x8d\x01\n\x8a\x01\n\tdouble.lt\x1a}!has(rules.gte) && !has(rules.gt) && (this.isNan() || this >= rules.lt)? \'value must be less than %s\'.format([rules.lt]) : \'\'H\x00\x12\xb0\x01\n\x03lte\x18\x03 \x01(\x01\x42\xa0\x01\xc2H\x9c\x01\n\x99\x01\n\ndouble.lte\x1a\x8a\x01!has(rules.gte) && !has(rules.gt) && (this.isNan() || this > rules.lte)? \'value must be less than or equal to %s\'.format([rules.lte]) : \'\'H\x00\x12\xf4\x07\n\x02gt\x18\x04 \x01(\x01\x42\xe5\x07\xc2H\xe1\x07\n\x8e\x01\n\tdouble.gt\x1a\x80\x01!has(rules.lt) && !has(rules.lte) && (this.isNan() || this <= rules.gt)? \'value must be greater than %s\'.format([rules.gt]) : \'\'\n\xc4\x01\n\x0c\x64ouble.gt_lt\x1a\xb3\x01has(rules.lt) && rules.lt >= rules.gt && (this.isNan() || this >= rules.lt || this <= rules.gt)? \'value must be greater than %s and less than %s\'.format([rules.gt, rules.lt]) : \'\'\n\xce\x01\n\x16\x64ouble.gt_lt_exclusive\x1a\xb3\x01has(rules.lt) && rules.lt < rules.gt && (this.isNan() || (rules.lt <= this && this <= rules.gt))? \'value must be greater than %s or less than %s\'.format([rules.gt, rules.lt]) : \'\'\n\xd4\x01\n\rdouble.gt_lte\x1a\xc2\x01has(rules.lte) && rules.lte >= rules.gt && (this.isNan() || this > rules.lte || this <= rules.gt)? \'value must be greater than %s and less than or equal to %s\'.format([rules.gt, rules.lte]) : \'\'\n\xde\x01\n\x17\x64ouble.gt_lte_exclusive\x1a\xc2\x01has(rules.lte) && rules.lte < rules.gt && (this.isNan() || (rules.lte < this && this <= rules.gt))? \'value must be greater than %s or less than or equal to %s\'.format([rules.gt, rules.lte]) : \'\'H\x01\x12\xbf\x08\n\x03gte\x18\x05 \x01(\x01\x42\xaf\x08\xc2H\xab\x08\n\x9c\x01\n\ndouble.gte\x1a\x8d\x01!has(rules.lt) && !has(rules.lte) && (this.isNan() || this < rules.gte)? \'value must be greater than or equal to %s\'.format([rules.gte]) : \'\'\n\xd3\x01\n\rdouble.gte_lt\x1a\xc1\x01has(rules.lt) && rules.lt >= rules.gte && (this.isNan() || this >= rules.lt || this < rules.gte)? \'value must be greater than or equal to %s and less than %s\'.format([rules.gte, rules.lt]) : \'\'\n\xdd\x01\n\x17\x64ouble.gte_lt_exclusive\x1a\xc1\x01has(rules.lt) && rules.lt < rules.gte && (this.isNan() || (rules.lt <= this && this < rules.gte))? \'value must be greater than or equal to %s or less than %s\'.format([rules.gte, rules.lt]) : \'\'\n\xe3\x01\n\x0e\x64ouble.gte_lte\x1a\xd0\x01has(rules.lte) && rules.lte >= rules.gte && (this.isNan() || this > rules.lte || this < rules.gte)? \'value must be greater than or equal to %s and less than or equal to %s\'.format([rules.gte, rules.lte]) : \'\'\n\xed\x01\n\x18\x64ouble.gte_lte_exclusive\x1a\xd0\x01has(rules.lte) && rules.lte < rules.gte && (this.isNan() || (rules.lte < this && this < rules.gte))? \'value must be greater than or equal to %s or less than or equal to %s\'.format([rules.gte, rules.lte]) : \'\'H\x01\x12\x80\x01\n\x02in\x18\x06 \x03(\x01\x42t\xc2Hq\no\n\tdouble.in\x1a\x62!(this in getField(rules, \'in\')) ? \'value must be in list %s\'.format([getField(rules, \'in\')]) : \'\'\x12w\n\x06not_in\x18\x07 \x03(\x01\x42g\xc2Hd\nb\n\rdouble.not_in\x1aQthis in rules.not_in ? \'value must not be in list %s\'.format([rules.not_in]) : \'\'\x12v\n\x06\x66inite\x18\x08 \x01(\x08\x42\x66\xc2Hc\na\n\rdouble.finite\x1aPrules.finite ? (this.isNan() || this.isInf() ? \'value must be finite\' : \'\') : \'\'\x12,\n\x07\x65xample\x18\t \x03(\x01\x42\x1b\xc2H\x18\n\x16\n\x0e\x64ouble.example\x1a\x04true*\t\x08\xe8\x07\x10\x80\x80\x80\x80\x02\x42\x0b\n\tless_thanB\x0e\n\x0cgreater_than\"\x8c\x15\n\nInt32Rules\x12\x83\x01\n\x05\x63onst\x18\x01 \x01(\x05\x42t\xc2Hq\no\n\x0bint32.const\x1a`this != getField(rules, \'const\') ? \'value must equal %s\'.format([getField(rules, \'const\')]) : \'\'\x12\x8a\x01\n\x02lt\x18\x02 \x01(\x05\x42|\xc2Hy\nw\n\x08int32.lt\x1ak!has(rules.gte) && !has(rules.gt) && this >= rules.lt? \'value must be less than %s\'.format([rules.lt]) : \'\'H\x00\x12\x9c\x01\n\x03lte\x18\x03 \x01(\x05\x42\x8c\x01\xc2H\x88\x01\n\x85\x01\n\tint32.lte\x1ax!has(rules.gte) && !has(rules.gt) && this > rules.lte? \'value must be less than or equal to %s\'.format([rules.lte]) : \'\'H\x00\x12\x97\x07\n\x02gt\x18\x04 \x01(\x05\x42\x88\x07\xc2H\x84\x07\nz\n\x08int32.gt\x1an!has(rules.lt) && !has(rules.lte) && this <= rules.gt? \'value must be greater than %s\'.format([rules.gt]) : \'\'\n\xb3\x01\n\x0bint32.gt_lt\x1a\xa3\x01has(rules.lt) && rules.lt >= rules.gt && (this >= rules.lt || this <= rules.gt)? \'value must be greater than %s and less than %s\'.format([rules.gt, rules.lt]) : \'\'\n\xbb\x01\n\x15int32.gt_lt_exclusive\x1a\xa1\x01has(rules.lt) && rules.lt < rules.gt && (rules.lt <= this && this <= rules.gt)? \'value must be greater than %s or less than %s\'.format([rules.gt, rules.lt]) : \'\'\n\xc3\x01\n\x0cint32.gt_lte\x1a\xb2\x01has(rules.lte) && rules.lte >= rules.gt && (this > rules.lte || this <= rules.gt)? \'value must be greater than %s and less than or equal to %s\'.format([rules.gt, rules.lte]) : \'\'\n\xcb\x01\n\x16int32.gt_lte_exclusive\x1a\xb0\x01has(rules.lte) && rules.lte < rules.gt && (rules.lte < this && this <= rules.gt)? \'value must be greater than %s or less than or equal to %s\'.format([rules.gt, rules.lte]) : \'\'H\x01\x12\xe3\x07\n\x03gte\x18\x05 \x01(\x05\x42\xd3\x07\xc2H\xcf\x07\n\x88\x01\n\tint32.gte\x1a{!has(rules.lt) && !has(rules.lte) && this < rules.gte? \'value must be greater than or equal to %s\'.format([rules.gte]) : \'\'\n\xc2\x01\n\x0cint32.gte_lt\x1a\xb1\x01has(rules.lt) && rules.lt >= rules.gte && (this >= rules.lt || this < rules.gte)? \'value must be greater than or equal to %s and less than %s\'.format([rules.gte, rules.lt]) : \'\'\n\xca\x01\n\x16int32.gte_lt_exclusive\x1a\xaf\x01has(rules.lt) && rules.lt < rules.gte && (rules.lt <= this && this < rules.gte)? \'value must be greater than or equal to %s or less than %s\'.format([rules.gte, rules.lt]) : \'\'\n\xd2\x01\n\rint32.gte_lte\x1a\xc0\x01has(rules.lte) && rules.lte >= rules.gte && (this > rules.lte || this < rules.gte)? \'value must be greater than or equal to %s and less than or equal to %s\'.format([rules.gte, rules.lte]) : \'\'\n\xda\x01\n\x17int32.gte_lte_exclusive\x1a\xbe\x01has(rules.lte) && rules.lte < rules.gte && (rules.lte < this && this < rules.gte)? \'value must be greater than or equal to %s or less than or equal to %s\'.format([rules.gte, rules.lte]) : \'\'H\x01\x12\x7f\n\x02in\x18\x06 \x03(\x05\x42s\xc2Hp\nn\n\x08int32.in\x1a\x62!(this in getField(rules, \'in\')) ? \'value must be in list %s\'.format([getField(rules, \'in\')]) : \'\'\x12v\n\x06not_in\x18\x07 \x03(\x05\x42\x66\xc2Hc\na\n\x0cint32.not_in\x1aQthis in rules.not_in ? \'value must not be in list %s\'.format([rules.not_in]) : \'\'\x12+\n\x07\x65xample\x18\x08 \x03(\x05\x42\x1a\xc2H\x17\n\x15\n\rint32.example\x1a\x04true*\t\x08\xe8\x07\x10\x80\x80\x80\x80\x02\x42\x0b\n\tless_thanB\x0e\n\x0cgreater_than\"\x8c\x15\n\nInt64Rules\x12\x83\x01\n\x05\x63onst\x18\x01 \x01(\x03\x42t\xc2Hq\no\n\x0bint64.const\x1a`this != getField(rules, \'const\') ? \'value must equal %s\'.format([getField(rules, \'const\')]) : \'\'\x12\x8a\x01\n\x02lt\x18\x02 \x01(\x03\x42|\xc2Hy\nw\n\x08int64.lt\x1ak!has(rules.gte) && !has(rules.gt) && this >= rules.lt? \'value must be less than %s\'.format([rules.lt]) : \'\'H\x00\x12\x9c\x01\n\x03lte\x18\x03 \x01(\x03\x42\x8c\x01\xc2H\x88\x01\n\x85\x01\n\tint64.lte\x1ax!has(rules.gte) && !has(rules.gt) && this > rules.lte? \'value must be less than or equal to %s\'.format([rules.lte]) : \'\'H\x00\x12\x97\x07\n\x02gt\x18\x04 \x01(\x03\x42\x88\x07\xc2H\x84\x07\nz\n\x08int64.gt\x1an!has(rules.lt) && !has(rules.lte) && this <= rules.gt? \'value must be greater than %s\'.format([rules.gt]) : \'\'\n\xb3\x01\n\x0bint64.gt_lt\x1a\xa3\x01has(rules.lt) && rules.lt >= rules.gt && (this >= rules.lt || this <= rules.gt)? \'value must be greater than %s and less than %s\'.format([rules.gt, rules.lt]) : \'\'\n\xbb\x01\n\x15int64.gt_lt_exclusive\x1a\xa1\x01has(rules.lt) && rules.lt < rules.gt && (rules.lt <= this && this <= rules.gt)? \'value must be greater than %s or less than %s\'.format([rules.gt, rules.lt]) : \'\'\n\xc3\x01\n\x0cint64.gt_lte\x1a\xb2\x01has(rules.lte) && rules.lte >= rules.gt && (this > rules.lte || this <= rules.gt)? \'value must be greater than %s and less than or equal to %s\'.format([rules.gt, rules.lte]) : \'\'\n\xcb\x01\n\x16int64.gt_lte_exclusive\x1a\xb0\x01has(rules.lte) && rules.lte < rules.gt && (rules.lte < this && this <= rules.gt)? \'value must be greater than %s or less than or equal to %s\'.format([rules.gt, rules.lte]) : \'\'H\x01\x12\xe3\x07\n\x03gte\x18\x05 \x01(\x03\x42\xd3\x07\xc2H\xcf\x07\n\x88\x01\n\tint64.gte\x1a{!has(rules.lt) && !has(rules.lte) && this < rules.gte? \'value must be greater than or equal to %s\'.format([rules.gte]) : \'\'\n\xc2\x01\n\x0cint64.gte_lt\x1a\xb1\x01has(rules.lt) && rules.lt >= rules.gte && (this >= rules.lt || this < rules.gte)? \'value must be greater than or equal to %s and less than %s\'.format([rules.gte, rules.lt]) : \'\'\n\xca\x01\n\x16int64.gte_lt_exclusive\x1a\xaf\x01has(rules.lt) && rules.lt < rules.gte && (rules.lt <= this && this < rules.gte)? \'value must be greater than or equal to %s or less than %s\'.format([rules.gte, rules.lt]) : \'\'\n\xd2\x01\n\rint64.gte_lte\x1a\xc0\x01has(rules.lte) && rules.lte >= rules.gte && (this > rules.lte || this < rules.gte)? \'value must be greater than or equal to %s and less than or equal to %s\'.format([rules.gte, rules.lte]) : \'\'\n\xda\x01\n\x17int64.gte_lte_exclusive\x1a\xbe\x01has(rules.lte) && rules.lte < rules.gte && (rules.lte < this && this < rules.gte)? \'value must be greater than or equal to %s or less than or equal to %s\'.format([rules.gte, rules.lte]) : \'\'H\x01\x12\x7f\n\x02in\x18\x06 \x03(\x03\x42s\xc2Hp\nn\n\x08int64.in\x1a\x62!(this in getField(rules, \'in\')) ? \'value must be in list %s\'.format([getField(rules, \'in\')]) : \'\'\x12v\n\x06not_in\x18\x07 \x03(\x03\x42\x66\xc2Hc\na\n\x0cint64.not_in\x1aQthis in rules.not_in ? \'value must not be in list %s\'.format([rules.not_in]) : \'\'\x12+\n\x07\x65xample\x18\t \x03(\x03\x42\x1a\xc2H\x17\n\x15\n\rint64.example\x1a\x04true*\t\x08\xe8\x07\x10\x80\x80\x80\x80\x02\x42\x0b\n\tless_thanB\x0e\n\x0cgreater_than\"\x9e\x15\n\x0bUInt32Rules\x12\x84\x01\n\x05\x63onst\x18\x01 \x01(\rBu\xc2Hr\np\n\x0cuint32.const\x1a`this != getField(rules, \'const\') ? \'value must equal %s\'.format([getField(rules, \'const\')]) : \'\'\x12\x8b\x01\n\x02lt\x18\x02 \x01(\rB}\xc2Hz\nx\n\tuint32.lt\x1ak!has(rules.gte) && !has(rules.gt) && this >= rules.lt? \'value must be less than %s\'.format([rules.lt]) : \'\'H\x00\x12\x9d\x01\n\x03lte\x18\x03 \x01(\rB\x8d\x01\xc2H\x89\x01\n\x86\x01\n\nuint32.lte\x1ax!has(rules.gte) && !has(rules.gt) && this > rules.lte? \'value must be less than or equal to %s\'.format([rules.lte]) : \'\'H\x00\x12\x9c\x07\n\x02gt\x18\x04 \x01(\rB\x8d\x07\xc2H\x89\x07\n{\n\tuint32.gt\x1an!has(rules.lt) && !has(rules.lte) && this <= rules.gt? \'value must be greater than %s\'.format([rules.gt]) : \'\'\n\xb4\x01\n\x0cuint32.gt_lt\x1a\xa3\x01has(rules.lt) && rules.lt >= rules.gt && (this >= rules.lt || this <= rules.gt)? \'value must be greater than %s and less than %s\'.format([rules.gt, rules.lt]) : \'\'\n\xbc\x01\n\x16uint32.gt_lt_exclusive\x1a\xa1\x01has(rules.lt) && rules.lt < rules.gt && (rules.lt <= this && this <= rules.gt)? \'value must be greater than %s or less than %s\'.format([rules.gt, rules.lt]) : \'\'\n\xc4\x01\n\ruint32.gt_lte\x1a\xb2\x01has(rules.lte) && rules.lte >= rules.gt && (this > rules.lte || this <= rules.gt)? \'value must be greater than %s and less than or equal to %s\'.format([rules.gt, rules.lte]) : \'\'\n\xcc\x01\n\x17uint32.gt_lte_exclusive\x1a\xb0\x01has(rules.lte) && rules.lte < rules.gt && (rules.lte < this && this <= rules.gt)? \'value must be greater than %s or less than or equal to %s\'.format([rules.gt, rules.lte]) : \'\'H\x01\x12\xe8\x07\n\x03gte\x18\x05 \x01(\rB\xd8\x07\xc2H\xd4\x07\n\x89\x01\n\nuint32.gte\x1a{!has(rules.lt) && !has(rules.lte) && this < rules.gte? \'value must be greater than or equal to %s\'.format([rules.gte]) : \'\'\n\xc3\x01\n\ruint32.gte_lt\x1a\xb1\x01has(rules.lt) && rules.lt >= rules.gte && (this >= rules.lt || this < rules.gte)? \'value must be greater than or equal to %s and less than %s\'.format([rules.gte, rules.lt]) : \'\'\n\xcb\x01\n\x17uint32.gte_lt_exclusive\x1a\xaf\x01has(rules.lt) && rules.lt < rules.gte && (rules.lt <= this && this < rules.gte)? \'value must be greater than or equal to %s or less than %s\'.format([rules.gte, rules.lt]) : \'\'\n\xd3\x01\n\x0euint32.gte_lte\x1a\xc0\x01has(rules.lte) && rules.lte >= rules.gte && (this > rules.lte || this < rules.gte)? \'value must be greater than or equal to %s and less than or equal to %s\'.format([rules.gte, rules.lte]) : \'\'\n\xdb\x01\n\x18uint32.gte_lte_exclusive\x1a\xbe\x01has(rules.lte) && rules.lte < rules.gte && (rules.lte < this && this < rules.gte)? \'value must be greater than or equal to %s or less than or equal to %s\'.format([rules.gte, rules.lte]) : \'\'H\x01\x12\x80\x01\n\x02in\x18\x06 \x03(\rBt\xc2Hq\no\n\tuint32.in\x1a\x62!(this in getField(rules, \'in\')) ? \'value must be in list %s\'.format([getField(rules, \'in\')]) : \'\'\x12w\n\x06not_in\x18\x07 \x03(\rBg\xc2Hd\nb\n\ruint32.not_in\x1aQthis in rules.not_in ? \'value must not be in list %s\'.format([rules.not_in]) : \'\'\x12,\n\x07\x65xample\x18\x08 \x03(\rB\x1b\xc2H\x18\n\x16\n\x0euint32.example\x1a\x04true*\t\x08\xe8\x07\x10\x80\x80\x80\x80\x02\x42\x0b\n\tless_thanB\x0e\n\x0cgreater_than\"\x9e\x15\n\x0bUInt64Rules\x12\x84\x01\n\x05\x63onst\x18\x01 \x01(\x04\x42u\xc2Hr\np\n\x0cuint64.const\x1a`this != getField(rules, \'const\') ? \'value must equal %s\'.format([getField(rules, \'const\')]) : \'\'\x12\x8b\x01\n\x02lt\x18\x02 \x01(\x04\x42}\xc2Hz\nx\n\tuint64.lt\x1ak!has(rules.gte) && !has(rules.gt) && this >= rules.lt? \'value must be less than %s\'.format([rules.lt]) : \'\'H\x00\x12\x9d\x01\n\x03lte\x18\x03 \x01(\x04\x42\x8d\x01\xc2H\x89\x01\n\x86\x01\n\nuint64.lte\x1ax!has(rules.gte) && !has(rules.gt) && this > rules.lte? \'value must be less than or equal to %s\'.format([rules.lte]) : \'\'H\x00\x12\x9c\x07\n\x02gt\x18\x04 \x01(\x04\x42\x8d\x07\xc2H\x89\x07\n{\n\tuint64.gt\x1an!has(rules.lt) && !has(rules.lte) && this <= rules.gt? \'value must be greater than %s\'.format([rules.gt]) : \'\'\n\xb4\x01\n\x0cuint64.gt_lt\x1a\xa3\x01has(rules.lt) && rules.lt >= rules.gt && (this >= rules.lt || this <= rules.gt)? \'value must be greater than %s and less than %s\'.format([rules.gt, rules.lt]) : \'\'\n\xbc\x01\n\x16uint64.gt_lt_exclusive\x1a\xa1\x01has(rules.lt) && rules.lt < rules.gt && (rules.lt <= this && this <= rules.gt)? \'value must be greater than %s or less than %s\'.format([rules.gt, rules.lt]) : \'\'\n\xc4\x01\n\ruint64.gt_lte\x1a\xb2\x01has(rules.lte) && rules.lte >= rules.gt && (this > rules.lte || this <= rules.gt)? \'value must be greater than %s and less than or equal to %s\'.format([rules.gt, rules.lte]) : \'\'\n\xcc\x01\n\x17uint64.gt_lte_exclusive\x1a\xb0\x01has(rules.lte) && rules.lte < rules.gt && (rules.lte < this && this <= rules.gt)? \'value must be greater than %s or less than or equal to %s\'.format([rules.gt, rules.lte]) : \'\'H\x01\x12\xe8\x07\n\x03gte\x18\x05 \x01(\x04\x42\xd8\x07\xc2H\xd4\x07\n\x89\x01\n\nuint64.gte\x1a{!has(rules.lt) && !has(rules.lte) && this < rules.gte? \'value must be greater than or equal to %s\'.format([rules.gte]) : \'\'\n\xc3\x01\n\ruint64.gte_lt\x1a\xb1\x01has(rules.lt) && rules.lt >= rules.gte && (this >= rules.lt || this < rules.gte)? \'value must be greater than or equal to %s and less than %s\'.format([rules.gte, rules.lt]) : \'\'\n\xcb\x01\n\x17uint64.gte_lt_exclusive\x1a\xaf\x01has(rules.lt) && rules.lt < rules.gte && (rules.lt <= this && this < rules.gte)? \'value must be greater than or equal to %s or less than %s\'.format([rules.gte, rules.lt]) : \'\'\n\xd3\x01\n\x0euint64.gte_lte\x1a\xc0\x01has(rules.lte) && rules.lte >= rules.gte && (this > rules.lte || this < rules.gte)? \'value must be greater than or equal to %s and less than or equal to %s\'.format([rules.gte, rules.lte]) : \'\'\n\xdb\x01\n\x18uint64.gte_lte_exclusive\x1a\xbe\x01has(rules.lte) && rules.lte < rules.gte && (rules.lte < this && this < rules.gte)? \'value must be greater than or equal to %s or less than or equal to %s\'.format([rules.gte, rules.lte]) : \'\'H\x01\x12\x80\x01\n\x02in\x18\x06 \x03(\x04\x42t\xc2Hq\no\n\tuint64.in\x1a\x62!(this in getField(rules, \'in\')) ? \'value must be in list %s\'.format([getField(rules, \'in\')]) : \'\'\x12w\n\x06not_in\x18\x07 \x03(\x04\x42g\xc2Hd\nb\n\ruint64.not_in\x1aQthis in rules.not_in ? \'value must not be in list %s\'.format([rules.not_in]) : \'\'\x12,\n\x07\x65xample\x18\x08 \x03(\x04\x42\x1b\xc2H\x18\n\x16\n\x0euint64.example\x1a\x04true*\t\x08\xe8\x07\x10\x80\x80\x80\x80\x02\x42\x0b\n\tless_thanB\x0e\n\x0cgreater_than\"\x9e\x15\n\x0bSInt32Rules\x12\x84\x01\n\x05\x63onst\x18\x01 \x01(\x11\x42u\xc2Hr\np\n\x0csint32.const\x1a`this != getField(rules, \'const\') ? \'value must equal %s\'.format([getField(rules, \'const\')]) : \'\'\x12\x8b\x01\n\x02lt\x18\x02 \x01(\x11\x42}\xc2Hz\nx\n\tsint32.lt\x1ak!has(rules.gte) && !has(rules.gt) && this >= rules.lt? \'value must be less than %s\'.format([rules.lt]) : \'\'H\x00\x12\x9d\x01\n\x03lte\x18\x03 \x01(\x11\x42\x8d\x01\xc2H\x89\x01\n\x86\x01\n\nsint32.lte\x1ax!has(rules.gte) && !has(rules.gt) && this > rules.lte? \'value must be less than or equal to %s\'.format([rules.lte]) : \'\'H\x00\x12\x9c\x07\n\x02gt\x18\x04 \x01(\x11\x42\x8d\x07\xc2H\x89\x07\n{\n\tsint32.gt\x1an!has(rules.lt) && !has(rules.lte) && this <= rules.gt? \'value must be greater than %s\'.format([rules.gt]) : \'\'\n\xb4\x01\n\x0csint32.gt_lt\x1a\xa3\x01has(rules.lt) && rules.lt >= rules.gt && (this >= rules.lt || this <= rules.gt)? \'value must be greater than %s and less than %s\'.format([rules.gt, rules.lt]) : \'\'\n\xbc\x01\n\x16sint32.gt_lt_exclusive\x1a\xa1\x01has(rules.lt) && rules.lt < rules.gt && (rules.lt <= this && this <= rules.gt)? \'value must be greater than %s or less than %s\'.format([rules.gt, rules.lt]) : \'\'\n\xc4\x01\n\rsint32.gt_lte\x1a\xb2\x01has(rules.lte) && rules.lte >= rules.gt && (this > rules.lte || this <= rules.gt)? \'value must be greater than %s and less than or equal to %s\'.format([rules.gt, rules.lte]) : \'\'\n\xcc\x01\n\x17sint32.gt_lte_exclusive\x1a\xb0\x01has(rules.lte) && rules.lte < rules.gt && (rules.lte < this && this <= rules.gt)? \'value must be greater than %s or less than or equal to %s\'.format([rules.gt, rules.lte]) : \'\'H\x01\x12\xe8\x07\n\x03gte\x18\x05 \x01(\x11\x42\xd8\x07\xc2H\xd4\x07\n\x89\x01\n\nsint32.gte\x1a{!has(rules.lt) && !has(rules.lte) && this < rules.gte? \'value must be greater than or equal to %s\'.format([rules.gte]) : \'\'\n\xc3\x01\n\rsint32.gte_lt\x1a\xb1\x01has(rules.lt) && rules.lt >= rules.gte && (this >= rules.lt || this < rules.gte)? \'value must be greater than or equal to %s and less than %s\'.format([rules.gte, rules.lt]) : \'\'\n\xcb\x01\n\x17sint32.gte_lt_exclusive\x1a\xaf\x01has(rules.lt) && rules.lt < rules.gte && (rules.lt <= this && this < rules.gte)? \'value must be greater than or equal to %s or less than %s\'.format([rules.gte, rules.lt]) : \'\'\n\xd3\x01\n\x0esint32.gte_lte\x1a\xc0\x01has(rules.lte) && rules.lte >= rules.gte && (this > rules.lte || this < rules.gte)? \'value must be greater than or equal to %s and less than or equal to %s\'.format([rules.gte, rules.lte]) : \'\'\n\xdb\x01\n\x18sint32.gte_lte_exclusive\x1a\xbe\x01has(rules.lte) && rules.lte < rules.gte && (rules.lte < this && this < rules.gte)? \'value must be greater than or equal to %s or less than or equal to %s\'.format([rules.gte, rules.lte]) : \'\'H\x01\x12\x80\x01\n\x02in\x18\x06 \x03(\x11\x42t\xc2Hq\no\n\tsint32.in\x1a\x62!(this in getField(rules, \'in\')) ? \'value must be in list %s\'.format([getField(rules, \'in\')]) : \'\'\x12w\n\x06not_in\x18\x07 \x03(\x11\x42g\xc2Hd\nb\n\rsint32.not_in\x1aQthis in rules.not_in ? \'value must not be in list %s\'.format([rules.not_in]) : \'\'\x12,\n\x07\x65xample\x18\x08 \x03(\x11\x42\x1b\xc2H\x18\n\x16\n\x0esint32.example\x1a\x04true*\t\x08\xe8\x07\x10\x80\x80\x80\x80\x02\x42\x0b\n\tless_thanB\x0e\n\x0cgreater_than\"\x9e\x15\n\x0bSInt64Rules\x12\x84\x01\n\x05\x63onst\x18\x01 \x01(\x12\x42u\xc2Hr\np\n\x0csint64.const\x1a`this != getField(rules, \'const\') ? \'value must equal %s\'.format([getField(rules, \'const\')]) : \'\'\x12\x8b\x01\n\x02lt\x18\x02 \x01(\x12\x42}\xc2Hz\nx\n\tsint64.lt\x1ak!has(rules.gte) && !has(rules.gt) && this >= rules.lt? \'value must be less than %s\'.format([rules.lt]) : \'\'H\x00\x12\x9d\x01\n\x03lte\x18\x03 \x01(\x12\x42\x8d\x01\xc2H\x89\x01\n\x86\x01\n\nsint64.lte\x1ax!has(rules.gte) && !has(rules.gt) && this > rules.lte? \'value must be less than or equal to %s\'.format([rules.lte]) : \'\'H\x00\x12\x9c\x07\n\x02gt\x18\x04 \x01(\x12\x42\x8d\x07\xc2H\x89\x07\n{\n\tsint64.gt\x1an!has(rules.lt) && !has(rules.lte) && this <= rules.gt? \'value must be greater than %s\'.format([rules.gt]) : \'\'\n\xb4\x01\n\x0csint64.gt_lt\x1a\xa3\x01has(rules.lt) && rules.lt >= rules.gt && (this >= rules.lt || this <= rules.gt)? \'value must be greater than %s and less than %s\'.format([rules.gt, rules.lt]) : \'\'\n\xbc\x01\n\x16sint64.gt_lt_exclusive\x1a\xa1\x01has(rules.lt) && rules.lt < rules.gt && (rules.lt <= this && this <= rules.gt)? \'value must be greater than %s or less than %s\'.format([rules.gt, rules.lt]) : \'\'\n\xc4\x01\n\rsint64.gt_lte\x1a\xb2\x01has(rules.lte) && rules.lte >= rules.gt && (this > rules.lte || this <= rules.gt)? \'value must be greater than %s and less than or equal to %s\'.format([rules.gt, rules.lte]) : \'\'\n\xcc\x01\n\x17sint64.gt_lte_exclusive\x1a\xb0\x01has(rules.lte) && rules.lte < rules.gt && (rules.lte < this && this <= rules.gt)? \'value must be greater than %s or less than or equal to %s\'.format([rules.gt, rules.lte]) : \'\'H\x01\x12\xe8\x07\n\x03gte\x18\x05 \x01(\x12\x42\xd8\x07\xc2H\xd4\x07\n\x89\x01\n\nsint64.gte\x1a{!has(rules.lt) && !has(rules.lte) && this < rules.gte? \'value must be greater than or equal to %s\'.format([rules.gte]) : \'\'\n\xc3\x01\n\rsint64.gte_lt\x1a\xb1\x01has(rules.lt) && rules.lt >= rules.gte && (this >= rules.lt || this < rules.gte)? \'value must be greater than or equal to %s and less than %s\'.format([rules.gte, rules.lt]) : \'\'\n\xcb\x01\n\x17sint64.gte_lt_exclusive\x1a\xaf\x01has(rules.lt) && rules.lt < rules.gte && (rules.lt <= this && this < rules.gte)? \'value must be greater than or equal to %s or less than %s\'.format([rules.gte, rules.lt]) : \'\'\n\xd3\x01\n\x0esint64.gte_lte\x1a\xc0\x01has(rules.lte) && rules.lte >= rules.gte && (this > rules.lte || this < rules.gte)? \'value must be greater than or equal to %s and less than or equal to %s\'.format([rules.gte, rules.lte]) : \'\'\n\xdb\x01\n\x18sint64.gte_lte_exclusive\x1a\xbe\x01has(rules.lte) && rules.lte < rules.gte && (rules.lte < this && this < rules.gte)? \'value must be greater than or equal to %s or less than or equal to %s\'.format([rules.gte, rules.lte]) : \'\'H\x01\x12\x80\x01\n\x02in\x18\x06 \x03(\x12\x42t\xc2Hq\no\n\tsint64.in\x1a\x62!(this in getField(rules, \'in\')) ? \'value must be in list %s\'.format([getField(rules, \'in\')]) : \'\'\x12w\n\x06not_in\x18\x07 \x03(\x12\x42g\xc2Hd\nb\n\rsint64.not_in\x1aQthis in rules.not_in ? \'value must not be in list %s\'.format([rules.not_in]) : \'\'\x12,\n\x07\x65xample\x18\x08 \x03(\x12\x42\x1b\xc2H\x18\n\x16\n\x0esint64.example\x1a\x04true*\t\x08\xe8\x07\x10\x80\x80\x80\x80\x02\x42\x0b\n\tless_thanB\x0e\n\x0cgreater_than\"\xaf\x15\n\x0c\x46ixed32Rules\x12\x85\x01\n\x05\x63onst\x18\x01 \x01(\x07\x42v\xc2Hs\nq\n\rfixed32.const\x1a`this != getField(rules, \'const\') ? \'value must equal %s\'.format([getField(rules, \'const\')]) : \'\'\x12\x8c\x01\n\x02lt\x18\x02 \x01(\x07\x42~\xc2H{\ny\n\nfixed32.lt\x1ak!has(rules.gte) && !has(rules.gt) && this >= rules.lt? \'value must be less than %s\'.format([rules.lt]) : \'\'H\x00\x12\x9e\x01\n\x03lte\x18\x03 \x01(\x07\x42\x8e\x01\xc2H\x8a\x01\n\x87\x01\n\x0b\x66ixed32.lte\x1ax!has(rules.gte) && !has(rules.gt) && this > rules.lte? \'value must be less than or equal to %s\'.format([rules.lte]) : \'\'H\x00\x12\xa1\x07\n\x02gt\x18\x04 \x01(\x07\x42\x92\x07\xc2H\x8e\x07\n|\n\nfixed32.gt\x1an!has(rules.lt) && !has(rules.lte) && this <= rules.gt? \'value must be greater than %s\'.format([rules.gt]) : \'\'\n\xb5\x01\n\rfixed32.gt_lt\x1a\xa3\x01has(rules.lt) && rules.lt >= rules.gt && (this >= rules.lt || this <= rules.gt)? \'value must be greater than %s and less than %s\'.format([rules.gt, rules.lt]) : \'\'\n\xbd\x01\n\x17\x66ixed32.gt_lt_exclusive\x1a\xa1\x01has(rules.lt) && rules.lt < rules.gt && (rules.lt <= this && this <= rules.gt)? \'value must be greater than %s or less than %s\'.format([rules.gt, rules.lt]) : \'\'\n\xc5\x01\n\x0e\x66ixed32.gt_lte\x1a\xb2\x01has(rules.lte) && rules.lte >= rules.gt && (this > rules.lte || this <= rules.gt)? \'value must be greater than %s and less than or equal to %s\'.format([rules.gt, rules.lte]) : \'\'\n\xcd\x01\n\x18\x66ixed32.gt_lte_exclusive\x1a\xb0\x01has(rules.lte) && rules.lte < rules.gt && (rules.lte < this && this <= rules.gt)? \'value must be greater than %s or less than or equal to %s\'.format([rules.gt, rules.lte]) : \'\'H\x01\x12\xed\x07\n\x03gte\x18\x05 \x01(\x07\x42\xdd\x07\xc2H\xd9\x07\n\x8a\x01\n\x0b\x66ixed32.gte\x1a{!has(rules.lt) && !has(rules.lte) && this < rules.gte? \'value must be greater than or equal to %s\'.format([rules.gte]) : \'\'\n\xc4\x01\n\x0e\x66ixed32.gte_lt\x1a\xb1\x01has(rules.lt) && rules.lt >= rules.gte && (this >= rules.lt || this < rules.gte)? \'value must be greater than or equal to %s and less than %s\'.format([rules.gte, rules.lt]) : \'\'\n\xcc\x01\n\x18\x66ixed32.gte_lt_exclusive\x1a\xaf\x01has(rules.lt) && rules.lt < rules.gte && (rules.lt <= this && this < rules.gte)? \'value must be greater than or equal to %s or less than %s\'.format([rules.gte, rules.lt]) : \'\'\n\xd4\x01\n\x0f\x66ixed32.gte_lte\x1a\xc0\x01has(rules.lte) && rules.lte >= rules.gte && (this > rules.lte || this < rules.gte)? \'value must be greater than or equal to %s and less than or equal to %s\'.format([rules.gte, rules.lte]) : \'\'\n\xdc\x01\n\x19\x66ixed32.gte_lte_exclusive\x1a\xbe\x01has(rules.lte) && rules.lte < rules.gte && (rules.lte < this && this < rules.gte)? \'value must be greater than or equal to %s or less than or equal to %s\'.format([rules.gte, rules.lte]) : \'\'H\x01\x12\x81\x01\n\x02in\x18\x06 \x03(\x07\x42u\xc2Hr\np\n\nfixed32.in\x1a\x62!(this in getField(rules, \'in\')) ? \'value must be in list %s\'.format([getField(rules, \'in\')]) : \'\'\x12x\n\x06not_in\x18\x07 \x03(\x07\x42h\xc2He\nc\n\x0e\x66ixed32.not_in\x1aQthis in rules.not_in ? \'value must not be in list %s\'.format([rules.not_in]) : \'\'\x12-\n\x07\x65xample\x18\x08 \x03(\x07\x42\x1c\xc2H\x19\n\x17\n\x0f\x66ixed32.example\x1a\x04true*\t\x08\xe8\x07\x10\x80\x80\x80\x80\x02\x42\x0b\n\tless_thanB\x0e\n\x0cgreater_than\"\xaf\x15\n\x0c\x46ixed64Rules\x12\x85\x01\n\x05\x63onst\x18\x01 \x01(\x06\x42v\xc2Hs\nq\n\rfixed64.const\x1a`this != getField(rules, \'const\') ? \'value must equal %s\'.format([getField(rules, \'const\')]) : \'\'\x12\x8c\x01\n\x02lt\x18\x02 \x01(\x06\x42~\xc2H{\ny\n\nfixed64.lt\x1ak!has(rules.gte) && !has(rules.gt) && this >= rules.lt? \'value must be less than %s\'.format([rules.lt]) : \'\'H\x00\x12\x9e\x01\n\x03lte\x18\x03 \x01(\x06\x42\x8e\x01\xc2H\x8a\x01\n\x87\x01\n\x0b\x66ixed64.lte\x1ax!has(rules.gte) && !has(rules.gt) && this > rules.lte? \'value must be less than or equal to %s\'.format([rules.lte]) : \'\'H\x00\x12\xa1\x07\n\x02gt\x18\x04 \x01(\x06\x42\x92\x07\xc2H\x8e\x07\n|\n\nfixed64.gt\x1an!has(rules.lt) && !has(rules.lte) && this <= rules.gt? \'value must be greater than %s\'.format([rules.gt]) : \'\'\n\xb5\x01\n\rfixed64.gt_lt\x1a\xa3\x01has(rules.lt) && rules.lt >= rules.gt && (this >= rules.lt || this <= rules.gt)? \'value must be greater than %s and less than %s\'.format([rules.gt, rules.lt]) : \'\'\n\xbd\x01\n\x17\x66ixed64.gt_lt_exclusive\x1a\xa1\x01has(rules.lt) && rules.lt < rules.gt && (rules.lt <= this && this <= rules.gt)? \'value must be greater than %s or less than %s\'.format([rules.gt, rules.lt]) : \'\'\n\xc5\x01\n\x0e\x66ixed64.gt_lte\x1a\xb2\x01has(rules.lte) && rules.lte >= rules.gt && (this > rules.lte || this <= rules.gt)? \'value must be greater than %s and less than or equal to %s\'.format([rules.gt, rules.lte]) : \'\'\n\xcd\x01\n\x18\x66ixed64.gt_lte_exclusive\x1a\xb0\x01has(rules.lte) && rules.lte < rules.gt && (rules.lte < this && this <= rules.gt)? \'value must be greater than %s or less than or equal to %s\'.format([rules.gt, rules.lte]) : \'\'H\x01\x12\xed\x07\n\x03gte\x18\x05 \x01(\x06\x42\xdd\x07\xc2H\xd9\x07\n\x8a\x01\n\x0b\x66ixed64.gte\x1a{!has(rules.lt) && !has(rules.lte) && this < rules.gte? \'value must be greater than or equal to %s\'.format([rules.gte]) : \'\'\n\xc4\x01\n\x0e\x66ixed64.gte_lt\x1a\xb1\x01has(rules.lt) && rules.lt >= rules.gte && (this >= rules.lt || this < rules.gte)? \'value must be greater than or equal to %s and less than %s\'.format([rules.gte, rules.lt]) : \'\'\n\xcc\x01\n\x18\x66ixed64.gte_lt_exclusive\x1a\xaf\x01has(rules.lt) && rules.lt < rules.gte && (rules.lt <= this && this < rules.gte)? \'value must be greater than or equal to %s or less than %s\'.format([rules.gte, rules.lt]) : \'\'\n\xd4\x01\n\x0f\x66ixed64.gte_lte\x1a\xc0\x01has(rules.lte) && rules.lte >= rules.gte && (this > rules.lte || this < rules.gte)? \'value must be greater than or equal to %s and less than or equal to %s\'.format([rules.gte, rules.lte]) : \'\'\n\xdc\x01\n\x19\x66ixed64.gte_lte_exclusive\x1a\xbe\x01has(rules.lte) && rules.lte < rules.gte && (rules.lte < this && this < rules.gte)? \'value must be greater than or equal to %s or less than or equal to %s\'.format([rules.gte, rules.lte]) : \'\'H\x01\x12\x81\x01\n\x02in\x18\x06 \x03(\x06\x42u\xc2Hr\np\n\nfixed64.in\x1a\x62!(this in getField(rules, \'in\')) ? \'value must be in list %s\'.format([getField(rules, \'in\')]) : \'\'\x12x\n\x06not_in\x18\x07 \x03(\x06\x42h\xc2He\nc\n\x0e\x66ixed64.not_in\x1aQthis in rules.not_in ? \'value must not be in list %s\'.format([rules.not_in]) : \'\'\x12-\n\x07\x65xample\x18\x08 \x03(\x06\x42\x1c\xc2H\x19\n\x17\n\x0f\x66ixed64.example\x1a\x04true*\t\x08\xe8\x07\x10\x80\x80\x80\x80\x02\x42\x0b\n\tless_thanB\x0e\n\x0cgreater_than\"\xc0\x15\n\rSFixed32Rules\x12\x86\x01\n\x05\x63onst\x18\x01 \x01(\x0f\x42w\xc2Ht\nr\n\x0esfixed32.const\x1a`this != getField(rules, \'const\') ? \'value must equal %s\'.format([getField(rules, \'const\')]) : \'\'\x12\x8d\x01\n\x02lt\x18\x02 \x01(\x0f\x42\x7f\xc2H|\nz\n\x0bsfixed32.lt\x1ak!has(rules.gte) && !has(rules.gt) && this >= rules.lt? \'value must be less than %s\'.format([rules.lt]) : \'\'H\x00\x12\x9f\x01\n\x03lte\x18\x03 \x01(\x0f\x42\x8f\x01\xc2H\x8b\x01\n\x88\x01\n\x0csfixed32.lte\x1ax!has(rules.gte) && !has(rules.gt) && this > rules.lte? \'value must be less than or equal to %s\'.format([rules.lte]) : \'\'H\x00\x12\xa6\x07\n\x02gt\x18\x04 \x01(\x0f\x42\x97\x07\xc2H\x93\x07\n}\n\x0bsfixed32.gt\x1an!has(rules.lt) && !has(rules.lte) && this <= rules.gt? \'value must be greater than %s\'.format([rules.gt]) : \'\'\n\xb6\x01\n\x0esfixed32.gt_lt\x1a\xa3\x01has(rules.lt) && rules.lt >= rules.gt && (this >= rules.lt || this <= rules.gt)? \'value must be greater than %s and less than %s\'.format([rules.gt, rules.lt]) : \'\'\n\xbe\x01\n\x18sfixed32.gt_lt_exclusive\x1a\xa1\x01has(rules.lt) && rules.lt < rules.gt && (rules.lt <= this && this <= rules.gt)? \'value must be greater than %s or less than %s\'.format([rules.gt, rules.lt]) : \'\'\n\xc6\x01\n\x0fsfixed32.gt_lte\x1a\xb2\x01has(rules.lte) && rules.lte >= rules.gt && (this > rules.lte || this <= rules.gt)? \'value must be greater than %s and less than or equal to %s\'.format([rules.gt, rules.lte]) : \'\'\n\xce\x01\n\x19sfixed32.gt_lte_exclusive\x1a\xb0\x01has(rules.lte) && rules.lte < rules.gt && (rules.lte < this && this <= rules.gt)? \'value must be greater than %s or less than or equal to %s\'.format([rules.gt, rules.lte]) : \'\'H\x01\x12\xf2\x07\n\x03gte\x18\x05 \x01(\x0f\x42\xe2\x07\xc2H\xde\x07\n\x8b\x01\n\x0csfixed32.gte\x1a{!has(rules.lt) && !has(rules.lte) && this < rules.gte? \'value must be greater than or equal to %s\'.format([rules.gte]) : \'\'\n\xc5\x01\n\x0fsfixed32.gte_lt\x1a\xb1\x01has(rules.lt) && rules.lt >= rules.gte && (this >= rules.lt || this < rules.gte)? \'value must be greater than or equal to %s and less than %s\'.format([rules.gte, rules.lt]) : \'\'\n\xcd\x01\n\x19sfixed32.gte_lt_exclusive\x1a\xaf\x01has(rules.lt) && rules.lt < rules.gte && (rules.lt <= this && this < rules.gte)? \'value must be greater than or equal to %s or less than %s\'.format([rules.gte, rules.lt]) : \'\'\n\xd5\x01\n\x10sfixed32.gte_lte\x1a\xc0\x01has(rules.lte) && rules.lte >= rules.gte && (this > rules.lte || this < rules.gte)? \'value must be greater than or equal to %s and less than or equal to %s\'.format([rules.gte, rules.lte]) : \'\'\n\xdd\x01\n\x1asfixed32.gte_lte_exclusive\x1a\xbe\x01has(rules.lte) && rules.lte < rules.gte && (rules.lte < this && this < rules.gte)? \'value must be greater than or equal to %s or less than or equal to %s\'.format([rules.gte, rules.lte]) : \'\'H\x01\x12\x82\x01\n\x02in\x18\x06 \x03(\x0f\x42v\xc2Hs\nq\n\x0bsfixed32.in\x1a\x62!(this in getField(rules, \'in\')) ? \'value must be in list %s\'.format([getField(rules, \'in\')]) : \'\'\x12y\n\x06not_in\x18\x07 \x03(\x0f\x42i\xc2Hf\nd\n\x0fsfixed32.not_in\x1aQthis in rules.not_in ? \'value must not be in list %s\'.format([rules.not_in]) : \'\'\x12.\n\x07\x65xample\x18\x08 \x03(\x0f\x42\x1d\xc2H\x1a\n\x18\n\x10sfixed32.example\x1a\x04true*\t\x08\xe8\x07\x10\x80\x80\x80\x80\x02\x42\x0b\n\tless_thanB\x0e\n\x0cgreater_than\"\xc0\x15\n\rSFixed64Rules\x12\x86\x01\n\x05\x63onst\x18\x01 \x01(\x10\x42w\xc2Ht\nr\n\x0esfixed64.const\x1a`this != getField(rules, \'const\') ? \'value must equal %s\'.format([getField(rules, \'const\')]) : \'\'\x12\x8d\x01\n\x02lt\x18\x02 \x01(\x10\x42\x7f\xc2H|\nz\n\x0bsfixed64.lt\x1ak!has(rules.gte) && !has(rules.gt) && this >= rules.lt? \'value must be less than %s\'.format([rules.lt]) : \'\'H\x00\x12\x9f\x01\n\x03lte\x18\x03 \x01(\x10\x42\x8f\x01\xc2H\x8b\x01\n\x88\x01\n\x0csfixed64.lte\x1ax!has(rules.gte) && !has(rules.gt) && this > rules.lte? \'value must be less than or equal to %s\'.format([rules.lte]) : \'\'H\x00\x12\xa6\x07\n\x02gt\x18\x04 \x01(\x10\x42\x97\x07\xc2H\x93\x07\n}\n\x0bsfixed64.gt\x1an!has(rules.lt) && !has(rules.lte) && this <= rules.gt? \'value must be greater than %s\'.format([rules.gt]) : \'\'\n\xb6\x01\n\x0esfixed64.gt_lt\x1a\xa3\x01has(rules.lt) && rules.lt >= rules.gt && (this >= rules.lt || this <= rules.gt)? \'value must be greater than %s and less than %s\'.format([rules.gt, rules.lt]) : \'\'\n\xbe\x01\n\x18sfixed64.gt_lt_exclusive\x1a\xa1\x01has(rules.lt) && rules.lt < rules.gt && (rules.lt <= this && this <= rules.gt)? \'value must be greater than %s or less than %s\'.format([rules.gt, rules.lt]) : \'\'\n\xc6\x01\n\x0fsfixed64.gt_lte\x1a\xb2\x01has(rules.lte) && rules.lte >= rules.gt && (this > rules.lte || this <= rules.gt)? \'value must be greater than %s and less than or equal to %s\'.format([rules.gt, rules.lte]) : \'\'\n\xce\x01\n\x19sfixed64.gt_lte_exclusive\x1a\xb0\x01has(rules.lte) && rules.lte < rules.gt && (rules.lte < this && this <= rules.gt)? \'value must be greater than %s or less than or equal to %s\'.format([rules.gt, rules.lte]) : \'\'H\x01\x12\xf2\x07\n\x03gte\x18\x05 \x01(\x10\x42\xe2\x07\xc2H\xde\x07\n\x8b\x01\n\x0csfixed64.gte\x1a{!has(rules.lt) && !has(rules.lte) && this < rules.gte? \'value must be greater than or equal to %s\'.format([rules.gte]) : \'\'\n\xc5\x01\n\x0fsfixed64.gte_lt\x1a\xb1\x01has(rules.lt) && rules.lt >= rules.gte && (this >= rules.lt || this < rules.gte)? \'value must be greater than or equal to %s and less than %s\'.format([rules.gte, rules.lt]) : \'\'\n\xcd\x01\n\x19sfixed64.gte_lt_exclusive\x1a\xaf\x01has(rules.lt) && rules.lt < rules.gte && (rules.lt <= this && this < rules.gte)? \'value must be greater than or equal to %s or less than %s\'.format([rules.gte, rules.lt]) : \'\'\n\xd5\x01\n\x10sfixed64.gte_lte\x1a\xc0\x01has(rules.lte) && rules.lte >= rules.gte && (this > rules.lte || this < rules.gte)? \'value must be greater than or equal to %s and less than or equal to %s\'.format([rules.gte, rules.lte]) : \'\'\n\xdd\x01\n\x1asfixed64.gte_lte_exclusive\x1a\xbe\x01has(rules.lte) && rules.lte < rules.gte && (rules.lte < this && this < rules.gte)? \'value must be greater than or equal to %s or less than or equal to %s\'.format([rules.gte, rules.lte]) : \'\'H\x01\x12\x82\x01\n\x02in\x18\x06 \x03(\x10\x42v\xc2Hs\nq\n\x0bsfixed64.in\x1a\x62!(this in getField(rules, \'in\')) ? \'value must be in list %s\'.format([getField(rules, \'in\')]) : \'\'\x12y\n\x06not_in\x18\x07 \x03(\x10\x42i\xc2Hf\nd\n\x0fsfixed64.not_in\x1aQthis in rules.not_in ? \'value must not be in list %s\'.format([rules.not_in]) : \'\'\x12.\n\x07\x65xample\x18\x08 \x03(\x10\x42\x1d\xc2H\x1a\n\x18\n\x10sfixed64.example\x1a\x04true*\t\x08\xe8\x07\x10\x80\x80\x80\x80\x02\x42\x0b\n\tless_thanB\x0e\n\x0cgreater_than\"\xc7\x01\n\tBoolRules\x12\x82\x01\n\x05\x63onst\x18\x01 \x01(\x08\x42s\xc2Hp\nn\n\nbool.const\x1a`this != getField(rules, \'const\') ? \'value must equal %s\'.format([getField(rules, \'const\')]) : \'\'\x12*\n\x07\x65xample\x18\x02 \x03(\x08\x42\x19\xc2H\x16\n\x14\n\x0c\x62ool.example\x1a\x04true*\t\x08\xe8\x07\x10\x80\x80\x80\x80\x02\"\x90\x37\n\x0bStringRules\x12\x86\x01\n\x05\x63onst\x18\x01 \x01(\tBw\xc2Ht\nr\n\x0cstring.const\x1a\x62this != getField(rules, \'const\') ? \'value must equal `%s`\'.format([getField(rules, \'const\')]) : \'\'\x12~\n\x03len\x18\x13 \x01(\x04\x42q\xc2Hn\nl\n\nstring.len\x1a^uint(this.size()) != rules.len ? \'value length must be %s characters\'.format([rules.len]) : \'\'\x12\x99\x01\n\x07min_len\x18\x02 \x01(\x04\x42\x87\x01\xc2H\x83\x01\n\x80\x01\n\x0estring.min_len\x1anuint(this.size()) < rules.min_len ? \'value length must be at least %s characters\'.format([rules.min_len]) : \'\'\x12\x97\x01\n\x07max_len\x18\x03 \x01(\x04\x42\x85\x01\xc2H\x81\x01\n\x7f\n\x0estring.max_len\x1amuint(this.size()) > rules.max_len ? \'value length must be at most %s characters\'.format([rules.max_len]) : \'\'\x12\x9b\x01\n\tlen_bytes\x18\x14 \x01(\x04\x42\x87\x01\xc2H\x83\x01\n\x80\x01\n\x10string.len_bytes\x1aluint(bytes(this).size()) != rules.len_bytes ? \'value length must be %s bytes\'.format([rules.len_bytes]) : \'\'\x12\xa3\x01\n\tmin_bytes\x18\x04 \x01(\x04\x42\x8f\x01\xc2H\x8b\x01\n\x88\x01\n\x10string.min_bytes\x1atuint(bytes(this).size()) < rules.min_bytes ? \'value length must be at least %s bytes\'.format([rules.min_bytes]) : \'\'\x12\xa2\x01\n\tmax_bytes\x18\x05 \x01(\x04\x42\x8e\x01\xc2H\x8a\x01\n\x87\x01\n\x10string.max_bytes\x1asuint(bytes(this).size()) > rules.max_bytes ? \'value length must be at most %s bytes\'.format([rules.max_bytes]) : \'\'\x12\x8d\x01\n\x07pattern\x18\x06 \x01(\tB|\xc2Hy\nw\n\x0estring.pattern\x1a\x65!this.matches(rules.pattern) ? \'value does not match regex pattern `%s`\'.format([rules.pattern]) : \'\'\x12\x84\x01\n\x06prefix\x18\x07 \x01(\tBt\xc2Hq\no\n\rstring.prefix\x1a^!this.startsWith(rules.prefix) ? \'value does not have prefix `%s`\'.format([rules.prefix]) : \'\'\x12\x82\x01\n\x06suffix\x18\x08 \x01(\tBr\xc2Ho\nm\n\rstring.suffix\x1a\\!this.endsWith(rules.suffix) ? \'value does not have suffix `%s`\'.format([rules.suffix]) : \'\'\x12\x90\x01\n\x08\x63ontains\x18\t \x01(\tB~\xc2H{\ny\n\x0fstring.contains\x1a\x66!this.contains(rules.contains) ? \'value does not contain substring `%s`\'.format([rules.contains]) : \'\'\x12\x98\x01\n\x0cnot_contains\x18\x17 \x01(\tB\x81\x01\xc2H~\n|\n\x13string.not_contains\x1a\x65this.contains(rules.not_contains) ? \'value contains substring `%s`\'.format([rules.not_contains]) : \'\'\x12\x80\x01\n\x02in\x18\n \x03(\tBt\xc2Hq\no\n\tstring.in\x1a\x62!(this in getField(rules, \'in\')) ? \'value must be in list %s\'.format([getField(rules, \'in\')]) : \'\'\x12w\n\x06not_in\x18\x0b \x03(\tBg\xc2Hd\nb\n\rstring.not_in\x1aQthis in rules.not_in ? \'value must not be in list %s\'.format([rules.not_in]) : \'\'\x12\xdf\x01\n\x05\x65mail\x18\x0c \x01(\x08\x42\xcd\x01\xc2H\xc9\x01\na\n\x0cstring.email\x12#value must be a valid email address\x1a,!rules.email || this == \'\' || this.isEmail()\nd\n\x12string.email_empty\x12\x32value is empty, which is not a valid email address\x1a\x1a!rules.email || this != \'\'H\x00\x12\xe7\x01\n\x08hostname\x18\r \x01(\x08\x42\xd2\x01\xc2H\xce\x01\ne\n\x0fstring.hostname\x12\x1evalue must be a valid hostname\x1a\x32!rules.hostname || this == \'\' || this.isHostname()\ne\n\x15string.hostname_empty\x12-value is empty, which is not a valid hostname\x1a\x1d!rules.hostname || this != \'\'H\x00\x12\xc7\x01\n\x02ip\x18\x0e \x01(\x08\x42\xb8\x01\xc2H\xb4\x01\nU\n\tstring.ip\x12 value must be a valid IP address\x1a&!rules.ip || this == \'\' || this.isIp()\n[\n\x0fstring.ip_empty\x12/value is empty, which is not a valid IP address\x1a\x17!rules.ip || this != \'\'H\x00\x12\xd6\x01\n\x04ipv4\x18\x0f \x01(\x08\x42\xc5\x01\xc2H\xc1\x01\n\\\n\x0bstring.ipv4\x12\"value must be a valid IPv4 address\x1a)!rules.ipv4 || this == \'\' || this.isIp(4)\na\n\x11string.ipv4_empty\x12\x31value is empty, which is not a valid IPv4 address\x1a\x19!rules.ipv4 || this != \'\'H\x00\x12\xd6\x01\n\x04ipv6\x18\x10 \x01(\x08\x42\xc5\x01\xc2H\xc1\x01\n\\\n\x0bstring.ipv6\x12\"value must be a valid IPv6 address\x1a)!rules.ipv6 || this == \'\' || this.isIp(6)\na\n\x11string.ipv6_empty\x12\x31value is empty, which is not a valid IPv6 address\x1a\x19!rules.ipv6 || this != \'\'H\x00\x12\xbf\x01\n\x03uri\x18\x11 \x01(\x08\x42\xaf\x01\xc2H\xab\x01\nQ\n\nstring.uri\x12\x19value must be a valid URI\x1a(!rules.uri || this == \'\' || this.isUri()\nV\n\x10string.uri_empty\x12(value is empty, which is not a valid URI\x1a\x18!rules.uri || this != \'\'H\x00\x12p\n\x07uri_ref\x18\x12 \x01(\x08\x42]\xc2HZ\nX\n\x0estring.uri_ref\x12#value must be a valid URI Reference\x1a!!rules.uri_ref || this.isUriRef()H\x00\x12\x90\x02\n\x07\x61\x64\x64ress\x18\x15 \x01(\x08\x42\xfc\x01\xc2H\xf8\x01\n\x81\x01\n\x0estring.address\x12-value must be a valid hostname, or ip address\x1a@!rules.address || this == \'\' || this.isHostname() || this.isIp()\nr\n\x14string.address_empty\x12<value is empty, which is not a valid hostname, or ip address\x1a\x1c!rules.address || this != \'\'H\x00\x12\x98\x02\n\x04uuid\x18\x16 \x01(\x08\x42\x87\x02\xc2H\x83\x02\n\xa5\x01\n\x0bstring.uuid\x12\x1avalue must be a valid UUID\x1az!rules.uuid || this == \'\' || this.matches(\'^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$\')\nY\n\x11string.uuid_empty\x12)value is empty, which is not a valid UUID\x1a\x19!rules.uuid || this != \'\'H\x00\x12\xf0\x01\n\x05tuuid\x18! \x01(\x08\x42\xde\x01\xc2H\xda\x01\ns\n\x0cstring.tuuid\x12\"value must be a valid trimmed UUID\x1a?!rules.tuuid || this == \'\' || this.matches(\'^[0-9a-fA-F]{32}$\')\nc\n\x12string.tuuid_empty\x12\x31value is empty, which is not a valid trimmed UUID\x1a\x1a!rules.tuuid || this != \'\'H\x00\x12\x96\x02\n\x11ip_with_prefixlen\x18\x1a \x01(\x08\x42\xf8\x01\xc2H\xf4\x01\nx\n\x18string.ip_with_prefixlen\x12\x1fvalue must be a valid IP prefix\x1a;!rules.ip_with_prefixlen || this == \'\' || this.isIpPrefix()\nx\n\x1estring.ip_with_prefixlen_empty\x12.value is empty, which is not a valid IP prefix\x1a&!rules.ip_with_prefixlen || this != \'\'H\x00\x12\xcf\x02\n\x13ipv4_with_prefixlen\x18\x1b \x01(\x08\x42\xaf\x02\xc2H\xab\x02\n\x93\x01\n\x1astring.ipv4_with_prefixlen\x12\x35value must be a valid IPv4 address with prefix length\x1a>!rules.ipv4_with_prefixlen || this == \'\' || this.isIpPrefix(4)\n\x92\x01\n string.ipv4_with_prefixlen_empty\x12\x44value is empty, which is not a valid IPv4 address with prefix length\x1a(!rules.ipv4_with_prefixlen || this != \'\'H\x00\x12\xcf\x02\n\x13ipv6_with_prefixlen\x18\x1c \x01(\x08\x42\xaf\x02\xc2H\xab\x02\n\x93\x01\n\x1astring.ipv6_with_prefixlen\x12\x35value must be a valid IPv6 address with prefix length\x1a>!rules.ipv6_with_prefixlen || this == \'\' || this.isIpPrefix(6)\n\x92\x01\n string.ipv6_with_prefixlen_empty\x12\x44value is empty, which is not a valid IPv6 address with prefix length\x1a(!rules.ipv6_with_prefixlen || this != \'\'H\x00\x12\xf2\x01\n\tip_prefix\x18\x1d \x01(\x08\x42\xdc\x01\xc2H\xd8\x01\nl\n\x10string.ip_prefix\x12\x1fvalue must be a valid IP prefix\x1a\x37!rules.ip_prefix || this == \'\' || this.isIpPrefix(true)\nh\n\x16string.ip_prefix_empty\x12.value is empty, which is not a valid IP prefix\x1a\x1e!rules.ip_prefix || this != \'\'H\x00\x12\x83\x02\n\x0bipv4_prefix\x18\x1e \x01(\x08\x42\xeb\x01\xc2H\xe7\x01\nu\n\x12string.ipv4_prefix\x12!value must be a valid IPv4 prefix\x1a<!rules.ipv4_prefix || this == \'\' || this.isIpPrefix(4, true)\nn\n\x18string.ipv4_prefix_empty\x12\x30value is empty, which is not a valid IPv4 prefix\x1a !rules.ipv4_prefix || this != \'\'H\x00\x12\x83\x02\n\x0bipv6_prefix\x18\x1f \x01(\x08\x42\xeb\x01\xc2H\xe7\x01\nu\n\x12string.ipv6_prefix\x12!value must be a valid IPv6 prefix\x1a<!rules.ipv6_prefix || this == \'\' || this.isIpPrefix(6, true)\nn\n\x18string.ipv6_prefix_empty\x12\x30value is empty, which is not a valid IPv6 prefix\x1a !rules.ipv6_prefix || this != \'\'H\x00\x12\xb5\x02\n\rhost_and_port\x18  \x01(\x08\x42\x9b\x02\xc2H\x97\x02\n\x99\x01\n\x14string.host_and_port\x12\x41value must be a valid host (hostname or IP address) and port pair\x1a>!rules.host_and_port || this == \'\' || this.isHostAndPort(true)\ny\n\x1astring.host_and_port_empty\x12\x37value is empty, which is not a valid host and port pair\x1a\"!rules.host_and_port || this != \'\'H\x00\x12\xa8\x05\n\x10well_known_regex\x18\x18 \x01(\x0e\x32\x18.buf.validate.KnownRegexB\xf1\x04\xc2H\xed\x04\n\xf0\x01\n#string.well_known_regex.header_name\x12&value must be a valid HTTP header name\x1a\xa0\x01rules.well_known_regex != 1 || this == \'\' || this.matches(!has(rules.strict) || rules.strict ?\'^:?[0-9a-zA-Z!#$%&\\\'*+-.^_|~\\x60]+$\' :\'^[^\\u0000\\u000A\\u000D]+$\')\n\x8d\x01\n)string.well_known_regex.header_name_empty\x12\x35value is empty, which is not a valid HTTP header name\x1a)rules.well_known_regex != 1 || this != \'\'\n\xe7\x01\n$string.well_known_regex.header_value\x12\'value must be a valid HTTP header value\x1a\x95\x01rules.well_known_regex != 2 || this.matches(!has(rules.strict) || rules.strict ?\'^[^\\u0000-\\u0008\\u000A-\\u001F\\u007F]*$\' :\'^[^\\u0000\\u000A\\u000D]*$\')H\x00\x12\x0e\n\x06strict\x18\x19 \x01(\x08\x12,\n\x07\x65xample\x18\" \x03(\tB\x1b\xc2H\x18\n\x16\n\x0estring.example\x1a\x04true*\t\x08\xe8\x07\x10\x80\x80\x80\x80\x02\x42\x0c\n\nwell_known\"\xea\x10\n\nBytesRules\x12\x80\x01\n\x05\x63onst\x18\x01 \x01(\x0c\x42q\xc2Hn\nl\n\x0b\x62ytes.const\x1a]this != getField(rules, \'const\') ? \'value must be %x\'.format([getField(rules, \'const\')]) : \'\'\x12x\n\x03len\x18\r \x01(\x04\x42k\xc2Hh\nf\n\tbytes.len\x1aYuint(this.size()) != rules.len ? \'value length must be %s bytes\'.format([rules.len]) : \'\'\x12\x90\x01\n\x07min_len\x18\x02 \x01(\x04\x42\x7f\xc2H|\nz\n\rbytes.min_len\x1aiuint(this.size()) < rules.min_len ? \'value length must be at least %s bytes\'.format([rules.min_len]) : \'\'\x12\x88\x01\n\x07max_len\x18\x03 \x01(\x04\x42w\xc2Ht\nr\n\rbytes.max_len\x1a\x61uint(this.size()) > rules.max_len ? \'value must be at most %s bytes\'.format([rules.max_len]) : \'\'\x12\x90\x01\n\x07pattern\x18\x04 \x01(\tB\x7f\xc2H|\nz\n\rbytes.pattern\x1ai!string(this).matches(rules.pattern) ? \'value must match regex pattern `%s`\'.format([rules.pattern]) : \'\'\x12\x81\x01\n\x06prefix\x18\x05 \x01(\x0c\x42q\xc2Hn\nl\n\x0c\x62ytes.prefix\x1a\\!this.startsWith(rules.prefix) ? \'value does not have prefix %x\'.format([rules.prefix]) : \'\'\x12\x7f\n\x06suffix\x18\x06 \x01(\x0c\x42o\xc2Hl\nj\n\x0c\x62ytes.suffix\x1aZ!this.endsWith(rules.suffix) ? \'value does not have suffix %x\'.format([rules.suffix]) : \'\'\x12\x83\x01\n\x08\x63ontains\x18\x07 \x01(\x0c\x42q\xc2Hn\nl\n\x0e\x62ytes.contains\x1aZ!this.contains(rules.contains) ? \'value does not contain %x\'.format([rules.contains]) : \'\'\x12\xa7\x01\n\x02in\x18\x08 \x03(\x0c\x42\x9a\x01\xc2H\x96\x01\n\x93\x01\n\x08\x62ytes.in\x1a\x86\x01getField(rules, \'in\').size() > 0 && !(this in getField(rules, \'in\')) ? \'value must be in list %s\'.format([getField(rules, \'in\')]) : \'\'\x12v\n\x06not_in\x18\t \x03(\x0c\x42\x66\xc2Hc\na\n\x0c\x62ytes.not_in\x1aQthis in rules.not_in ? \'value must not be in list %s\'.format([rules.not_in]) : \'\'\x12\xeb\x01\n\x02ip\x18\n \x01(\x08\x42\xdc\x01\xc2H\xd8\x01\nt\n\x08\x62ytes.ip\x12 value must be a valid IP address\x1a\x46!rules.ip || this.size() == 0 || this.size() == 4 || this.size() == 16\n`\n\x0e\x62ytes.ip_empty\x12/value is empty, which is not a valid IP address\x1a\x1d!rules.ip || this.size() != 0H\x00\x12\xe4\x01\n\x04ipv4\x18\x0b \x01(\x08\x42\xd3\x01\xc2H\xcf\x01\ne\n\nbytes.ipv4\x12\"value must be a valid IPv4 address\x1a\x33!rules.ipv4 || this.size() == 0 || this.size() == 4\nf\n\x10\x62ytes.ipv4_empty\x12\x31value is empty, which is not a valid IPv4 address\x1a\x1f!rules.ipv4 || this.size() != 0H\x00\x12\xe5\x01\n\x04ipv6\x18\x0c \x01(\x08\x42\xd4\x01\xc2H\xd0\x01\nf\n\nbytes.ipv6\x12\"value must be a valid IPv6 address\x1a\x34!rules.ipv6 || this.size() == 0 || this.size() == 16\nf\n\x10\x62ytes.ipv6_empty\x12\x31value is empty, which is not a valid IPv6 address\x1a\x1f!rules.ipv6 || this.size() != 0H\x00\x12+\n\x07\x65xample\x18\x0e \x03(\x0c\x42\x1a\xc2H\x17\n\x15\n\rbytes.example\x1a\x04true*\t\x08\xe8\x07\x10\x80\x80\x80\x80\x02\x42\x0c\n\nwell_known\"\xd4\x03\n\tEnumRules\x12\x82\x01\n\x05\x63onst\x18\x01 \x01(\x05\x42s\xc2Hp\nn\n\nenum.const\x1a`this != getField(rules, \'const\') ? \'value must equal %s\'.format([getField(rules, \'const\')]) : \'\'\x12\x14\n\x0c\x64\x65\x66ined_only\x18\x02 \x01(\x08\x12~\n\x02in\x18\x03 \x03(\x05\x42r\xc2Ho\nm\n\x07\x65num.in\x1a\x62!(this in getField(rules, \'in\')) ? \'value must be in list %s\'.format([getField(rules, \'in\')]) : \'\'\x12u\n\x06not_in\x18\x04 \x03(\x05\x42\x65\xc2Hb\n`\n\x0b\x65num.not_in\x1aQthis in rules.not_in ? \'value must not be in list %s\'.format([rules.not_in]) : \'\'\x12*\n\x07\x65xample\x18\x05 \x03(\x05\x42\x19\xc2H\x16\n\x14\n\x0c\x65num.example\x1a\x04true*\t\x08\xe8\x07\x10\x80\x80\x80\x80\x02\"\xfb\x03\n\rRepeatedRules\x12\x9e\x01\n\tmin_items\x18\x01 \x01(\x04\x42\x8a\x01\xc2H\x86\x01\n\x83\x01\n\x12repeated.min_items\x1amuint(this.size()) < rules.min_items ? \'value must contain at least %d item(s)\'.format([rules.min_items]) : \'\'\x12\xa2\x01\n\tmax_items\x18\x02 \x01(\x04\x42\x8e\x01\xc2H\x8a\x01\n\x87\x01\n\x12repeated.max_items\x1aquint(this.size()) > rules.max_items ? \'value must contain no more than %s item(s)\'.format([rules.max_items]) : \'\'\x12p\n\x06unique\x18\x03 \x01(\x08\x42`\xc2H]\n[\n\x0frepeated.unique\x12(repeated value must contain unique items\x1a\x1e!rules.unique || this.unique()\x12\'\n\x05items\x18\x04 \x01(\x0b\x32\x18.buf.validate.FieldRules*\t\x08\xe8\x07\x10\x80\x80\x80\x80\x02\"\x8a\x03\n\x08MapRules\x12\x8f\x01\n\tmin_pairs\x18\x01 \x01(\x04\x42|\xc2Hy\nw\n\rmap.min_pairs\x1a\x66uint(this.size()) < rules.min_pairs ? \'map must be at least %d entries\'.format([rules.min_pairs]) : \'\'\x12\x8e\x01\n\tmax_pairs\x18\x02 \x01(\x04\x42{\xc2Hx\nv\n\rmap.max_pairs\x1a\x65uint(this.size()) > rules.max_pairs ? \'map must be at most %d entries\'.format([rules.max_pairs]) : \'\'\x12&\n\x04keys\x18\x04 \x01(\x0b\x32\x18.buf.validate.FieldRules\x12(\n\x06values\x18\x05 \x01(\x0b\x32\x18.buf.validate.FieldRules*\t\x08\xe8\x07\x10\x80\x80\x80\x80\x02\"&\n\x08\x41nyRules\x12\n\n\x02in\x18\x02 \x03(\t\x12\x0e\n\x06not_in\x18\x03 \x03(\t\"\x99\x17\n\rDurationRules\x12\xa1\x01\n\x05\x63onst\x18\x02 \x01(\x0b\x32\x19.google.protobuf.DurationBw\xc2Ht\nr\n\x0e\x64uration.const\x1a`this != getField(rules, \'const\') ? \'value must equal %s\'.format([getField(rules, \'const\')]) : \'\'\x12\xa8\x01\n\x02lt\x18\x03 \x01(\x0b\x32\x19.google.protobuf.DurationB\x7f\xc2H|\nz\n\x0b\x64uration.lt\x1ak!has(rules.gte) && !has(rules.gt) && this >= rules.lt? \'value must be less than %s\'.format([rules.lt]) : \'\'H\x00\x12\xba\x01\n\x03lte\x18\x04 \x01(\x0b\x32\x19.google.protobuf.DurationB\x8f\x01\xc2H\x8b\x01\n\x88\x01\n\x0c\x64uration.lte\x1ax!has(rules.gte) && !has(rules.gt) && this > rules.lte? \'value must be less than or equal to %s\'.format([rules.lte]) : \'\'H\x00\x12\xc1\x07\n\x02gt\x18\x05 \x01(\x0b\x32\x19.google.protobuf.DurationB\x97\x07\xc2H\x93\x07\n}\n\x0b\x64uration.gt\x1an!has(rules.lt) && !has(rules.lte) && this <= rules.gt? \'value must be greater than %s\'.format([rules.gt]) : \'\'\n\xb6\x01\n\x0e\x64uration.gt_lt\x1a\xa3\x01has(rules.lt) && rules.lt >= rules.gt && (this >= rules.lt || this <= rules.gt)? \'value must be greater than %s and less than %s\'.format([rules.gt, rules.lt]) : \'\'\n\xbe\x01\n\x18\x64uration.gt_lt_exclusive\x1a\xa1\x01has(rules.lt) && rules.lt < rules.gt && (rules.lt <= this && this <= rules.gt)? \'value must be greater than %s or less than %s\'.format([rules.gt, rules.lt]) : \'\'\n\xc6\x01\n\x0f\x64uration.gt_lte\x1a\xb2\x01has(rules.lte) && rules.lte >= rules.gt && (this > rules.lte || this <= rules.gt)? \'value must be greater than %s and less than or equal to %s\'.format([rules.gt, rules.lte]) : \'\'\n\xce\x01\n\x19\x64uration.gt_lte_exclusive\x1a\xb0\x01has(rules.lte) && rules.lte < rules.gt && (rules.lte < this && this <= rules.gt)? \'value must be greater than %s or less than or equal to %s\'.format([rules.gt, rules.lte]) : \'\'H\x01\x12\x8d\x08\n\x03gte\x18\x06 \x01(\x0b\x32\x19.google.protobuf.DurationB\xe2\x07\xc2H\xde\x07\n\x8b\x01\n\x0c\x64uration.gte\x1a{!has(rules.lt) && !has(rules.lte) && this < rules.gte? \'value must be greater than or equal to %s\'.format([rules.gte]) : \'\'\n\xc5\x01\n\x0f\x64uration.gte_lt\x1a\xb1\x01has(rules.lt) && rules.lt >= rules.gte && (this >= rules.lt || this < rules.gte)? \'value must be greater than or equal to %s and less than %s\'.format([rules.gte, rules.lt]) : \'\'\n\xcd\x01\n\x19\x64uration.gte_lt_exclusive\x1a\xaf\x01has(rules.lt) && rules.lt < rules.gte && (rules.lt <= this && this < rules.gte)? \'value must be greater than or equal to %s or less than %s\'.format([rules.gte, rules.lt]) : \'\'\n\xd5\x01\n\x10\x64uration.gte_lte\x1a\xc0\x01has(rules.lte) && rules.lte >= rules.gte && (this > rules.lte || this < rules.gte)? \'value must be greater than or equal to %s and less than or equal to %s\'.format([rules.gte, rules.lte]) : \'\'\n\xdd\x01\n\x1a\x64uration.gte_lte_exclusive\x1a\xbe\x01has(rules.lte) && rules.lte < rules.gte && (rules.lte < this && this < rules.gte)? \'value must be greater than or equal to %s or less than or equal to %s\'.format([rules.gte, rules.lte]) : \'\'H\x01\x12\x9d\x01\n\x02in\x18\x07 \x03(\x0b\x32\x19.google.protobuf.DurationBv\xc2Hs\nq\n\x0b\x64uration.in\x1a\x62!(this in getField(rules, \'in\')) ? \'value must be in list %s\'.format([getField(rules, \'in\')]) : \'\'\x12\x94\x01\n\x06not_in\x18\x08 \x03(\x0b\x32\x19.google.protobuf.DurationBi\xc2Hf\nd\n\x0f\x64uration.not_in\x1aQthis in rules.not_in ? \'value must not be in list %s\'.format([rules.not_in]) : \'\'\x12I\n\x07\x65xample\x18\t \x03(\x0b\x32\x19.google.protobuf.DurationB\x1d\xc2H\x1a\n\x18\n\x10\x64uration.example\x1a\x04true*\t\x08\xe8\x07\x10\x80\x80\x80\x80\x02\x42\x0b\n\tless_thanB\x0e\n\x0cgreater_than\"\x92\x18\n\x0eTimestampRules\x12\xa3\x01\n\x05\x63onst\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.TimestampBx\xc2Hu\ns\n\x0ftimestamp.const\x1a`this != getField(rules, \'const\') ? \'value must equal %s\'.format([getField(rules, \'const\')]) : \'\'\x12\xab\x01\n\x02lt\x18\x03 \x01(\x0b\x32\x1a.google.protobuf.TimestampB\x80\x01\xc2H}\n{\n\x0ctimestamp.lt\x1ak!has(rules.gte) && !has(rules.gt) && this >= rules.lt? \'value must be less than %s\'.format([rules.lt]) : \'\'H\x00\x12\xbc\x01\n\x03lte\x18\x04 \x01(\x0b\x32\x1a.google.protobuf.TimestampB\x90\x01\xc2H\x8c\x01\n\x89\x01\n\rtimestamp.lte\x1ax!has(rules.gte) && !has(rules.gt) && this > rules.lte? \'value must be less than or equal to %s\'.format([rules.lte]) : \'\'H\x00\x12l\n\x06lt_now\x18\x07 \x01(\x08\x42Z\xc2HW\nU\n\x10timestamp.lt_now\x1a\x41(rules.lt_now && this > now) ? \'value must be less than now\' : \'\'H\x00\x12\xc7\x07\n\x02gt\x18\x05 \x01(\x0b\x32\x1a.google.protobuf.TimestampB\x9c\x07\xc2H\x98\x07\n~\n\x0ctimestamp.gt\x1an!has(rules.lt) && !has(rules.lte) && this <= rules.gt? \'value must be greater than %s\'.format([rules.gt]) : \'\'\n\xb7\x01\n\x0ftimestamp.gt_lt\x1a\xa3\x01has(rules.lt) && rules.lt >= rules.gt && (this >= rules.lt || this <= rules.gt)? \'value must be greater than %s and less than %s\'.format([rules.gt, rules.lt]) : \'\'\n\xbf\x01\n\x19timestamp.gt_lt_exclusive\x1a\xa1\x01has(rules.lt) && rules.lt < rules.gt && (rules.lt <= this && this <= rules.gt)? \'value must be greater than %s or less than %s\'.format([rules.gt, rules.lt]) : \'\'\n\xc7\x01\n\x10timestamp.gt_lte\x1a\xb2\x01has(rules.lte) && rules.lte >= rules.gt && (this > rules.lte || this <= rules.gt)? \'value must be greater than %s and less than or equal to %s\'.format([rules.gt, rules.lte]) : \'\'\n\xcf\x01\n\x1atimestamp.gt_lte_exclusive\x1a\xb0\x01has(rules.lte) && rules.lte < rules.gt && (rules.lte < this && this <= rules.gt)? \'value must be greater than %s or less than or equal to %s\'.format([rules.gt, rules.lte]) : \'\'H\x01\x12\x93\x08\n\x03gte\x18\x06 \x01(\x0b\x32\x1a.google.protobuf.TimestampB\xe7\x07\xc2H\xe3\x07\n\x8c\x01\n\rtimestamp.gte\x1a{!has(rules.lt) && !has(rules.lte) && this < rules.gte? \'value must be greater than or equal to %s\'.format([rules.gte]) : \'\'\n\xc6\x01\n\x10timestamp.gte_lt\x1a\xb1\x01has(rules.lt) && rules.lt >= rules.gte && (this >= rules.lt || this < rules.gte)? \'value must be greater than or equal to %s and less than %s\'.format([rules.gte, rules.lt]) : \'\'\n\xce\x01\n\x1atimestamp.gte_lt_exclusive\x1a\xaf\x01has(rules.lt) && rules.lt < rules.gte && (rules.lt <= this && this < rules.gte)? \'value must be greater than or equal to %s or less than %s\'.format([rules.gte, rules.lt]) : \'\'\n\xd6\x01\n\x11timestamp.gte_lte\x1a\xc0\x01has(rules.lte) && rules.lte >= rules.gte && (this > rules.lte || this < rules.gte)? \'value must be greater than or equal to %s and less than or equal to %s\'.format([rules.gte, rules.lte]) : \'\'\n\xde\x01\n\x1btimestamp.gte_lte_exclusive\x1a\xbe\x01has(rules.lte) && rules.lte < rules.gte && (rules.lte < this && this < rules.gte)? \'value must be greater than or equal to %s or less than or equal to %s\'.format([rules.gte, rules.lte]) : \'\'H\x01\x12o\n\x06gt_now\x18\x08 \x01(\x08\x42]\xc2HZ\nX\n\x10timestamp.gt_now\x1a\x44(rules.gt_now && this < now) ? \'value must be greater than now\' : \'\'H\x01\x12\xb8\x01\n\x06within\x18\t \x01(\x0b\x32\x19.google.protobuf.DurationB\x8c\x01\xc2H\x88\x01\n\x85\x01\n\x10timestamp.within\x1aqthis < now-rules.within || this > now+rules.within ? \'value must be within %s of now\'.format([rules.within]) : \'\'\x12K\n\x07\x65xample\x18\n \x03(\x0b\x32\x1a.google.protobuf.TimestampB\x1e\xc2H\x1b\n\x19\n\x11timestamp.example\x1a\x04true*\t\x08\xe8\x07\x10\x80\x80\x80\x80\x02\x42\x0b\n\tless_thanB\x0e\n\x0cgreater_than\"9\n\nViolations\x12+\n\nviolations\x18\x01 \x03(\x0b\x32\x17.buf.validate.Violation\"\x9f\x01\n\tViolation\x12&\n\x05\x66ield\x18\x05 \x01(\x0b\x32\x17.buf.validate.FieldPath\x12%\n\x04rule\x18\x06 \x01(\x0b\x32\x17.buf.validate.FieldPath\x12\x0f\n\x07rule_id\x18\x02 \x01(\t\x12\x0f\n\x07message\x18\x03 \x01(\t\x12\x0f\n\x07\x66or_key\x18\x04 \x01(\x08J\x04\x08\x01\x10\x02R\nfield_path\"=\n\tFieldPath\x12\x30\n\x08\x65lements\x18\x01 \x03(\x0b\x32\x1e.buf.validate.FieldPathElement\"\xe9\x02\n\x10\x46ieldPathElement\x12\x14\n\x0c\x66ield_number\x18\x01 \x01(\x05\x12\x12\n\nfield_name\x18\x02 \x01(\t\x12>\n\nfield_type\x18\x03 \x01(\x0e\x32*.google.protobuf.FieldDescriptorProto.Type\x12<\n\x08key_type\x18\x04 \x01(\x0e\x32*.google.protobuf.FieldDescriptorProto.Type\x12>\n\nvalue_type\x18\x05 \x01(\x0e\x32*.google.protobuf.FieldDescriptorProto.Type\x12\x0f\n\x05index\x18\x06 \x01(\x04H\x00\x12\x12\n\x08\x62ool_key\x18\x07 \x01(\x08H\x00\x12\x11\n\x07int_key\x18\x08 \x01(\x03H\x00\x12\x12\n\x08uint_key\x18\t \x01(\x04H\x00\x12\x14\n\nstring_key\x18\n \x01(\tH\x00\x42\x0b\n\tsubscript*\x87\x01\n\x06Ignore\x12\x16\n\x12IGNORE_UNSPECIFIED\x10\x00\x12\x19\n\x15IGNORE_IF_UNPOPULATED\x10\x01\x12\x1b\n\x17IGNORE_IF_DEFAULT_VALUE\x10\x02\x12\x11\n\rIGNORE_ALWAYS\x10\x03*\x1aIGNORE_EMPTYIGNORE_DEFAULT*n\n\nKnownRegex\x12\x1b\n\x17KNOWN_REGEX_UNSPECIFIED\x10\x00\x12 \n\x1cKNOWN_REGEX_HTTP_HEADER_NAME\x10\x01\x12!\n\x1dKNOWN_REGEX_HTTP_HEADER_VALUE\x10\x02:M\n\x07message\x12\x1f.google.protobuf.MessageOptions\x18\x87\t \x01(\x0b\x32\x1a.buf.validate.MessageRules:G\n\x05oneof\x12\x1d.google.protobuf.OneofOptions\x18\x87\t \x01(\x0b\x32\x18.buf.validate.OneofRules:G\n\x05\x66ield\x12\x1d.google.protobuf.FieldOptions\x18\x87\t \x01(\x0b\x32\x18.buf.validate.FieldRules:Q\n\npredefined\x12\x1d.google.protobuf.FieldOptions\x18\x88\t \x01(\x0b\x32\x1d.buf.validate.PredefinedRulesBn\n\x12\x62uild.buf.validateB\rValidateProtoP\x01ZGbuf.build/gen/go/bufbuild/protovalidate/protocolbuffers/go/buf/validate')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'api_protos.buf.validate.validate_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  _globals['DESCRIPTOR']._options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n\022build.buf.validateB\rValidateProtoP\001ZGbuf.build/gen/go/bufbuild/protovalidate/protocolbuffers/go/buf/validate'
  _globals['_FLOATRULES'].fields_by_name['const']._options = None
  _globals['_FLOATRULES'].fields_by_name['const']._serialized_options = b'\302Hq\no\n\013float.const\032`this != getField(rules, \'const\') ? \'value must equal %s\'.format([getField(rules, \'const\')]) : \'\''
  _globals['_FLOATRULES'].fields_by_name['lt']._options = None
  _globals['_FLOATRULES'].fields_by_name['lt']._serialized_options = b'\302H\214\001\n\211\001\n\010float.lt\032}!has(rules.gte) && !has(rules.gt) && (this.isNan() || this >= rules.lt)? \'value must be less than %s\'.format([rules.lt]) : \'\''
  _globals['_FLOATRULES'].fields_by_name['lte']._options = None
  _globals['_FLOATRULES'].fields_by_name['lte']._serialized_options = b'\302H\233\001\n\230\001\n\tfloat.lte\032\212\001!has(rules.gte) && !has(rules.gt) && (this.isNan() || this > rules.lte)? \'value must be less than or equal to %s\'.format([rules.lte]) : \'\''
  _globals['_FLOATRULES'].fields_by_name['gt']._options = None
  _globals['_FLOATRULES'].fields_by_name['gt']._serialized_options = b'\302H\334\007\n\215\001\n\010float.gt\032\200\001!has(rules.lt) && !has(rules.lte) && (this.isNan() || this <= rules.gt)? \'value must be greater than %s\'.format([rules.gt]) : \'\'\n\303\001\n\013float.gt_lt\032\263\001has(rules.lt) && rules.lt >= rules.gt && (this.isNan() || this >= rules.lt || this <= rules.gt)? \'value must be greater than %s and less than %s\'.format([rules.gt, rules.lt]) : \'\'\n\315\001\n\025float.gt_lt_exclusive\032\263\001has(rules.lt) && rules.lt < rules.gt && (this.isNan() || (rules.lt <= this && this <= rules.gt))? \'value must be greater than %s or less than %s\'.format([rules.gt, rules.lt]) : \'\'\n\323\001\n\014float.gt_lte\032\302\001has(rules.lte) && rules.lte >= rules.gt && (this.isNan() || this > rules.lte || this <= rules.gt)? \'value must be greater than %s and less than or equal to %s\'.format([rules.gt, rules.lte]) : \'\'\n\335\001\n\026float.gt_lte_exclusive\032\302\001has(rules.lte) && rules.lte < rules.gt && (this.isNan() || (rules.lte < this && this <= rules.gt))? \'value must be greater than %s or less than or equal to %s\'.format([rules.gt, rules.lte]) : \'\''
  _globals['_FLOATRULES'].fields_by_name['gte']._options = None
  _globals['_FLOATRULES'].fields_by_name['gte']._serialized_options = b'\302H\246\010\n\233\001\n\tfloat.gte\032\215\001!has(rules.lt) && !has(rules.lte) && (this.isNan() || this < rules.gte)? \'value must be greater than or equal to %s\'.format([rules.gte]) : \'\'\n\322\001\n\014float.gte_lt\032\301\001has(rules.lt) && rules.lt >= rules.gte && (this.isNan() || this >= rules.lt || this < rules.gte)? \'value must be greater than or equal to %s and less than %s\'.format([rules.gte, rules.lt]) : \'\'\n\334\001\n\026float.gte_lt_exclusive\032\301\001has(rules.lt) && rules.lt < rules.gte && (this.isNan() || (rules.lt <= this && this < rules.gte))? \'value must be greater than or equal to %s or less than %s\'.format([rules.gte, rules.lt]) : \'\'\n\342\001\n\rfloat.gte_lte\032\320\001has(rules.lte) && rules.lte >= rules.gte && (this.isNan() || this > rules.lte || this < rules.gte)? \'value must be greater than or equal to %s and less than or equal to %s\'.format([rules.gte, rules.lte]) : \'\'\n\354\001\n\027float.gte_lte_exclusive\032\320\001has(rules.lte) && rules.lte < rules.gte && (this.isNan() || (rules.lte < this && this < rules.gte))? \'value must be greater than or equal to %s or less than or equal to %s\'.format([rules.gte, rules.lte]) : \'\''
  _globals['_FLOATRULES'].fields_by_name['in']._options = None
  _globals['_FLOATRULES'].fields_by_name['in']._serialized_options = b'\302Hp\nn\n\010float.in\032b!(this in getField(rules, \'in\')) ? \'value must be in list %s\'.format([getField(rules, \'in\')]) : \'\''
  _globals['_FLOATRULES'].fields_by_name['not_in']._options = None
  _globals['_FLOATRULES'].fields_by_name['not_in']._serialized_options = b'\302Hc\na\n\014float.not_in\032Qthis in rules.not_in ? \'value must not be in list %s\'.format([rules.not_in]) : \'\''
  _globals['_FLOATRULES'].fields_by_name['finite']._options = None
  _globals['_FLOATRULES'].fields_by_name['finite']._serialized_options = b'\302Hb\n`\n\014float.finite\032Prules.finite ? (this.isNan() || this.isInf() ? \'value must be finite\' : \'\') : \'\''
  _globals['_FLOATRULES'].fields_by_name['example']._options = None
  _globals['_FLOATRULES'].fields_by_name['example']._serialized_options = b'\302H\027\n\025\n\rfloat.example\032\004true'
  _globals['_DOUBLERULES'].fields_by_name['const']._options = None
  _globals['_DOUBLERULES'].fields_by_name['const']._serialized_options = b'\302Hr\np\n\014double.const\032`this != getField(rules, \'const\') ? \'value must equal %s\'.format([getField(rules, \'const\')]) : \'\''
  _globals['_DOUBLERULES'].fields_by_name['lt']._options = None
  _globals['_DOUBLERULES'].fields_by_name['lt']._serialized_options = b'\302H\215\001\n\212\001\n\tdouble.lt\032}!has(rules.gte) && !has(rules.gt) && (this.isNan() || this >= rules.lt)? \'value must be less than %s\'.format([rules.lt]) : \'\''
  _globals['_DOUBLERULES'].fields_by_name['lte']._options = None
  _globals['_DOUBLERULES'].fields_by_name['lte']._serialized_options = b'\302H\234\001\n\231\001\n\ndouble.lte\032\212\001!has(rules.gte) && !has(rules.gt) && (this.isNan() || this > rules.lte)? \'value must be less than or equal to %s\'.format([rules.lte]) : \'\''
  _globals['_DOUBLERULES'].fields_by_name['gt']._options = None
  _globals['_DOUBLERULES'].fields_by_name['gt']._serialized_options = b'\302H\341\007\n\216\001\n\tdouble.gt\032\200\001!has(rules.lt) && !has(rules.lte) && (this.isNan() || this <= rules.gt)? \'value must be greater than %s\'.format([rules.gt]) : \'\'\n\304\001\n\014double.gt_lt\032\263\001has(rules.lt) && rules.lt >= rules.gt && (this.isNan() || this >= rules.lt || this <= rules.gt)? \'value must be greater than %s and less than %s\'.format([rules.gt, rules.lt]) : \'\'\n\316\001\n\026double.gt_lt_exclusive\032\263\001has(rules.lt) && rules.lt < rules.gt && (this.isNan() || (rules.lt <= this && this <= rules.gt))? \'value must be greater than %s or less than %s\'.format([rules.gt, rules.lt]) : \'\'\n\324\001\n\rdouble.gt_lte\032\302\001has(rules.lte) && rules.lte >= rules.gt && (this.isNan() || this > rules.lte || this <= rules.gt)? \'value must be greater than %s and less than or equal to %s\'.format([rules.gt, rules.lte]) : \'\'\n\336\001\n\027double.gt_lte_exclusive\032\302\001has(rules.lte) && rules.lte < rules.gt && (this.isNan() || (rules.lte < this && this <= rules.gt))? \'value must be greater than %s or less than or equal to %s\'.format([rules.gt, rules.lte]) : \'\''
  _globals['_DOUBLERULES'].fields_by_name['gte']._options = None
  _globals['_DOUBLERULES'].fields_by_name['gte']._serialized_options = b'\302H\253\010\n\234\001\n\ndouble.gte\032\215\001!has(rules.lt) && !has(rules.lte) && (this.isNan() || this < rules.gte)? \'value must be greater than or equal to %s\'.format([rules.gte]) : \'\'\n\323\001\n\rdouble.gte_lt\032\301\001has(rules.lt) && rules.lt >= rules.gte && (this.isNan() || this >= rules.lt || this < rules.gte)? \'value must be greater than or equal to %s and less than %s\'.format([rules.gte, rules.lt]) : \'\'\n\335\001\n\027double.gte_lt_exclusive\032\301\001has(rules.lt) && rules.lt < rules.gte && (this.isNan() || (rules.lt <= this && this < rules.gte))? \'value must be greater than or equal to %s or less than %s\'.format([rules.gte, rules.lt]) : \'\'\n\343\001\n\016double.gte_lte\032\320\001has(rules.lte) && rules.lte >= rules.gte && (this.isNan() || this > rules.lte || this < rules.gte)? \'value must be greater than or equal to %s and less than or equal to %s\'.format([rules.gte, rules.lte]) : \'\'\n\355\001\n\030double.gte_lte_exclusive\032\320\001has(rules.lte) && rules.lte < rules.gte && (this.isNan() || (rules.lte < this && this < rules.gte))? \'value must be greater than or equal to %s or less than or equal to %s\'.format([rules.gte, rules.lte]) : \'\''
  _globals['_DOUBLERULES'].fields_by_name['in']._options = None
  _globals['_DOUBLERULES'].fields_by_name['in']._serialized_options = b'\302Hq\no\n\tdouble.in\032b!(this in getField(rules, \'in\')) ? \'value must be in list %s\'.format([getField(rules, \'in\')]) : \'\''
  _globals['_DOUBLERULES'].fields_by_name['not_in']._options = None
  _globals['_DOUBLERULES'].fields_by_name['not_in']._serialized_options = b'\302Hd\nb\n\rdouble.not_in\032Qthis in rules.not_in ? \'value must not be in list %s\'.format([rules.not_in]) : \'\''
  _globals['_DOUBLERULES'].fields_by_name['finite']._options = None
  _globals['_DOUBLERULES'].fields_by_name['finite']._serialized_options = b'\302Hc\na\n\rdouble.finite\032Prules.finite ? (this.isNan() || this.isInf() ? \'value must be finite\' : \'\') : \'\''
  _globals['_DOUBLERULES'].fields_by_name['example']._options = None
  _globals['_DOUBLERULES'].fields_by_name['example']._serialized_options = b'\302H\030\n\026\n\016double.example\032\004true'
  _globals['_INT32RULES'].fields_by_name['const']._options = None
  _globals['_INT32RULES'].fields_by_name['const']._serialized_options = b'\302Hq\no\n\013int32.const\032`this != getField(rules, \'const\') ? \'value must equal %s\'.format([getField(rules, \'const\')]) : \'\''
  _globals['_INT32RULES'].fields_by_name['lt']._options = None
  _globals['_INT32RULES'].fields_by_name['lt']._serialized_options = b'\302Hy\nw\n\010int32.lt\032k!has(rules.gte) && !has(rules.gt) && this >= rules.lt? \'value must be less than %s\'.format([rules.lt]) : \'\''
  _globals['_INT32RULES'].fields_by_name['lte']._options = None
  _globals['_INT32RULES'].fields_by_name['lte']._serialized_options = b'\302H\210\001\n\205\001\n\tint32.lte\032x!has(rules.gte) && !has(rules.gt) && this > rules.lte? \'value must be less than or equal to %s\'.format([rules.lte]) : \'\''
  _globals['_INT32RULES'].fields_by_name['gt']._options = None
  _globals['_INT32RULES'].fields_by_name['gt']._serialized_options = b'\302H\204\007\nz\n\010int32.gt\032n!has(rules.lt) && !has(rules.lte) && this <= rules.gt? \'value must be greater than %s\'.format([rules.gt]) : \'\'\n\263\001\n\013int32.gt_lt\032\243\001has(rules.lt) && rules.lt >= rules.gt && (this >= rules.lt || this <= rules.gt)? \'value must be greater than %s and less than %s\'.format([rules.gt, rules.lt]) : \'\'\n\273\001\n\025int32.gt_lt_exclusive\032\241\001has(rules.lt) && rules.lt < rules.gt && (rules.lt <= this && this <= rules.gt)? \'value must be greater than %s or less than %s\'.format([rules.gt, rules.lt]) : \'\'\n\303\001\n\014int32.gt_lte\032\262\001has(rules.lte) && rules.lte >= rules.gt && (this > rules.lte || this <= rules.gt)? \'value must be greater than %s and less than or equal to %s\'.format([rules.gt, rules.lte]) : \'\'\n\313\001\n\026int32.gt_lte_exclusive\032\260\001has(rules.lte) && rules.lte < rules.gt && (rules.lte < this && this <= rules.gt)? \'value must be greater than %s or less than or equal to %s\'.format([rules.gt, rules.lte]) : \'\''
  _globals['_INT32RULES'].fields_by_name['gte']._options = None
  _globals['_INT32RULES'].fields_by_name['gte']._serialized_options = b'\302H\317\007\n\210\001\n\tint32.gte\032{!has(rules.lt) && !has(rules.lte) && this < rules.gte? \'value must be greater than or equal to %s\'.format([rules.gte]) : \'\'\n\302\001\n\014int32.gte_lt\032\261\001has(rules.lt) && rules.lt >= rules.gte && (this >= rules.lt || this < rules.gte)? \'value must be greater than or equal to %s and less than %s\'.format([rules.gte, rules.lt]) : \'\'\n\312\001\n\026int32.gte_lt_exclusive\032\257\001has(rules.lt) && rules.lt < rules.gte && (rules.lt <= this && this < rules.gte)? \'value must be greater than or equal to %s or less than %s\'.format([rules.gte, rules.lt]) : \'\'\n\322\001\n\rint32.gte_lte\032\300\001has(rules.lte) && rules.lte >= rules.gte && (this > rules.lte || this < rules.gte)? \'value must be greater than or equal to %s and less than or equal to %s\'.format([rules.gte, rules.lte]) : \'\'\n\332\001\n\027int32.gte_lte_exclusive\032\276\001has(rules.lte) && rules.lte < rules.gte && (rules.lte < this && this < rules.gte)? \'value must be greater than or equal to %s or less than or equal to %s\'.format([rules.gte, rules.lte]) : \'\''
  _globals['_INT32RULES'].fields_by_name['in']._options = None
  _globals['_INT32RULES'].fields_by_name['in']._serialized_options = b'\302Hp\nn\n\010int32.in\032b!(this in getField(rules, \'in\')) ? \'value must be in list %s\'.format([getField(rules, \'in\')]) : \'\''
  _globals['_INT32RULES'].fields_by_name['not_in']._options = None
  _globals['_INT32RULES'].fields_by_name['not_in']._serialized_options = b'\302Hc\na\n\014int32.not_in\032Qthis in rules.not_in ? \'value must not be in list %s\'.format([rules.not_in]) : \'\''
  _globals['_INT32RULES'].fields_by_name['example']._options = None
  _globals['_INT32RULES'].fields_by_name['example']._serialized_options = b'\302H\027\n\025\n\rint32.example\032\004true'
  _globals['_INT64RULES'].fields_by_name['const']._options = None
  _globals['_INT64RULES'].fields_by_name['const']._serialized_options = b'\302Hq\no\n\013int64.const\032`this != getField(rules, \'const\') ? \'value must equal %s\'.format([getField(rules, \'const\')]) : \'\''
  _globals['_INT64RULES'].fields_by_name['lt']._options = None
  _globals['_INT64RULES'].fields_by_name['lt']._serialized_options = b'\302Hy\nw\n\010int64.lt\032k!has(rules.gte) && !has(rules.gt) && this >= rules.lt? \'value must be less than %s\'.format([rules.lt]) : \'\''
  _globals['_INT64RULES'].fields_by_name['lte']._options = None
  _globals['_INT64RULES'].fields_by_name['lte']._serialized_options = b'\302H\210\001\n\205\001\n\tint64.lte\032x!has(rules.gte) && !has(rules.gt) && this > rules.lte? \'value must be less than or equal to %s\'.format([rules.lte]) : \'\''
  _globals['_INT64RULES'].fields_by_name['gt']._options = None
  _globals['_INT64RULES'].fields_by_name['gt']._serialized_options = b'\302H\204\007\nz\n\010int64.gt\032n!has(rules.lt) && !has(rules.lte) && this <= rules.gt? \'value must be greater than %s\'.format([rules.gt]) : \'\'\n\263\001\n\013int64.gt_lt\032\243\001has(rules.lt) && rules.lt >= rules.gt && (this >= rules.lt || this <= rules.gt)? \'value must be greater than %s and less than %s\'.format([rules.gt, rules.lt]) : \'\'\n\273\001\n\025int64.gt_lt_exclusive\032\241\001has(rules.lt) && rules.lt < rules.gt && (rules.lt <= this && this <= rules.gt)? \'value must be greater than %s or less than %s\'.format([rules.gt, rules.lt]) : \'\'\n\303\001\n\014int64.gt_lte\032\262\001has(rules.lte) && rules.lte >= rules.gt && (this > rules.lte || this <= rules.gt)? \'value must be greater than %s and less than or equal to %s\'.format([rules.gt, rules.lte]) : \'\'\n\313\001\n\026int64.gt_lte_exclusive\032\260\001has(rules.lte) && rules.lte < rules.gt && (rules.lte < this && this <= rules.gt)? \'value must be greater than %s or less than or equal to %s\'.format([rules.gt, rules.lte]) : \'\''
  _globals['_INT64RULES'].fields_by_name['gte']._options = None
  _globals['_INT64RULES'].fields_by_name['gte']._serialized_options = b'\302H\317\007\n\210\001\n\tint64.gte\032{!has(rules.lt) && !has(rules.lte) && this < rules.gte? \'value must be greater than or equal to %s\'.format([rules.gte]) : \'\'\n\302\001\n\014int64.gte_lt\032\261\001has(rules.lt) && rules.lt >= rules.gte && (this >= rules.lt || this < rules.gte)? \'value must be greater than or equal to %s and less than %s\'.format([rules.gte, rules.lt]) : \'\'\n\312\001\n\026int64.gte_lt_exclusive\032\257\001has(rules.lt) && rules.lt < rules.gte && (rules.lt <= this && this < rules.gte)? \'value must be greater than or equal to %s or less than %s\'.format([rules.gte, rules.lt]) : \'\'\n\322\001\n\rint64.gte_lte\032\300\001has(rules.lte) && rules.lte >= rules.gte && (this > rules.lte || this < rules.gte)? \'value must be greater than or equal to %s and less than or equal to %s\'.format([rules.gte, rules.lte]) : \'\'\n\332\001\n\027int64.gte_lte_exclusive\032\276\001has(rules.lte) && rules.lte < rules.gte && (rules.lte < this && this < rules.gte)? \'value must be greater than or equal to %s or less than or equal to %s\'.format([rules.gte, rules.lte]) : \'\''
  _globals['_INT64RULES'].fields_by_name['in']._options = None
  _globals['_INT64RULES'].fields_by_name['in']._serialized_options = b'\302Hp\nn\n\010int64.in\032b!(this in getField(rules, \'in\')) ? \'value must be in list %s\'.format([getField(rules, \'in\')]) : \'\''
  _globals['_INT64RULES'].fields_by_name['not_in']._options = None
  _globals['_INT64RULES'].fields_by_name['not_in']._serialized_options = b'\302Hc\na\n\014int64.not_in\032Qthis in rules.not_in ? \'value must not be in list %s\'.format([rules.not_in]) : \'\''
  _globals['_INT64RULES'].fields_by_name['example']._options = None
  _globals['_INT64RULES'].fields_by_name['example']._serialized_options = b'\302H\027\n\025\n\rint64.example\032\004true'
  _globals['_UINT32RULES'].fields_by_name['const']._options = None
  _globals['_UINT32RULES'].fields_by_name['const']._serialized_options = b'\302Hr\np\n\014uint32.const\032`this != getField(rules, \'const\') ? \'value must equal %s\'.format([getField(rules, \'const\')]) : \'\''
  _globals['_UINT32RULES'].fields_by_name['lt']._options = None
  _globals['_UINT32RULES'].fields_by_name['lt']._serialized_options = b'\302Hz\nx\n\tuint32.lt\032k!has(rules.gte) && !has(rules.gt) && this >= rules.lt? \'value must be less than %s\'.format([rules.lt]) : \'\''
  _globals['_UINT32RULES'].fields_by_name['lte']._options = None
  _globals['_UINT32RULES'].fields_by_name['lte']._serialized_options = b'\302H\211\001\n\206\001\n\nuint32.lte\032x!has(rules.gte) && !has(rules.gt) && this > rules.lte? \'value must be less than or equal to %s\'.format([rules.lte]) : \'\''
  _globals['_UINT32RULES'].fields_by_name['gt']._options = None
  _globals['_UINT32RULES'].fields_by_name['gt']._serialized_options = b'\302H\211\007\n{\n\tuint32.gt\032n!has(rules.lt) && !has(rules.lte) && this <= rules.gt? \'value must be greater than %s\'.format([rules.gt]) : \'\'\n\264\001\n\014uint32.gt_lt\032\243\001has(rules.lt) && rules.lt >= rules.gt && (this >= rules.lt || this <= rules.gt)? \'value must be greater than %s and less than %s\'.format([rules.gt, rules.lt]) : \'\'\n\274\001\n\026uint32.gt_lt_exclusive\032\241\001has(rules.lt) && rules.lt < rules.gt && (rules.lt <= this && this <= rules.gt)? \'value must be greater than %s or less than %s\'.format([rules.gt, rules.lt]) : \'\'\n\304\001\n\ruint32.gt_lte\032\262\001has(rules.lte) && rules.lte >= rules.gt && (this > rules.lte || this <= rules.gt)? \'value must be greater than %s and less than or equal to %s\'.format([rules.gt, rules.lte]) : \'\'\n\314\001\n\027uint32.gt_lte_exclusive\032\260\001has(rules.lte) && rules.lte < rules.gt && (rules.lte < this && this <= rules.gt)? \'value must be greater than %s or less than or equal to %s\'.format([rules.gt, rules.lte]) : \'\''
  _globals['_UINT32RULES'].fields_by_name['gte']._options = None
  _globals['_UINT32RULES'].fields_by_name['gte']._serialized_options = b'\302H\324\007\n\211\001\n\nuint32.gte\032{!has(rules.lt) && !has(rules.lte) && this < rules.gte? \'value must be greater than or equal to %s\'.format([rules.gte]) : \'\'\n\303\001\n\ruint32.gte_lt\032\261\001has(rules.lt) && rules.lt >= rules.gte && (this >= rules.lt || this < rules.gte)? \'value must be greater than or equal to %s and less than %s\'.format([rules.gte, rules.lt]) : \'\'\n\313\001\n\027uint32.gte_lt_exclusive\032\257\001has(rules.lt) && rules.lt < rules.gte && (rules.lt <= this && this < rules.gte)? \'value must be greater than or equal to %s or less than %s\'.format([rules.gte, rules.lt]) : \'\'\n\323\001\n\016uint32.gte_lte\032\300\001has(rules.lte) && rules.lte >= rules.gte && (this > rules.lte || this < rules.gte)? \'value must be greater than or equal to %s and less than or equal to %s\'.format([rules.gte, rules.lte]) : \'\'\n\333\001\n\030uint32.gte_lte_exclusive\032\276\001has(rules.lte) && rules.lte < rules.gte && (rules.lte < this && this < rules.gte)? \'value must be greater than or equal to %s or less than or equal to %s\'.format([rules.gte, rules.lte]) : \'\''
  _globals['_UINT32RULES'].fields_by_name['in']._options = None
  _globals['_UINT32RULES'].fields_by_name['in']._serialized_options = b'\302Hq\no\n\tuint32.in\032b!(this in getField(rules, \'in\')) ? \'value must be in list %s\'.format([getField(rules, \'in\')]) : \'\''
  _globals['_UINT32RULES'].fields_by_name['not_in']._options = None
  _globals['_UINT32RULES'].fields_by_name['not_in']._serialized_options = b'\302Hd\nb\n\ruint32.not_in\032Qthis in rules.not_in ? \'value must not be in list %s\'.format([rules.not_in]) : \'\''
  _globals['_UINT32RULES'].fields_by_name['example']._options = None
  _globals['_UINT32RULES'].fields_by_name['example']._serialized_options = b'\302H\030\n\026\n\016uint32.example\032\004true'
  _globals['_UINT64RULES'].fields_by_name['const']._options = None
  _globals['_UINT64RULES'].fields_by_name['const']._serialized_options = b'\302Hr\np\n\014uint64.const\032`this != getField(rules, \'const\') ? \'value must equal %s\'.format([getField(rules, \'const\')]) : \'\''
  _globals['_UINT64RULES'].fields_by_name['lt']._options = None
  _globals['_UINT64RULES'].fields_by_name['lt']._serialized_options = b'\302Hz\nx\n\tuint64.lt\032k!has(rules.gte) && !has(rules.gt) && this >= rules.lt? \'value must be less than %s\'.format([rules.lt]) : \'\''
  _globals['_UINT64RULES'].fields_by_name['lte']._options = None
  _globals['_UINT64RULES'].fields_by_name['lte']._serialized_options = b'\302H\211\001\n\206\001\n\nuint64.lte\032x!has(rules.gte) && !has(rules.gt) && this > rules.lte? \'value must be less than or equal to %s\'.format([rules.lte]) : \'\''
  _globals['_UINT64RULES'].fields_by_name['gt']._options = None
  _globals['_UINT64RULES'].fields_by_name['gt']._serialized_options = b'\302H\211\007\n{\n\tuint64.gt\032n!has(rules.lt) && !has(rules.lte) && this <= rules.gt? \'value must be greater than %s\'.format([rules.gt]) : \'\'\n\264\001\n\014uint64.gt_lt\032\243\001has(rules.lt) && rules.lt >= rules.gt && (this >= rules.lt || this <= rules.gt)? \'value must be greater than %s and less than %s\'.format([rules.gt, rules.lt]) : \'\'\n\274\001\n\026uint64.gt_lt_exclusive\032\241\001has(rules.lt) && rules.lt < rules.gt && (rules.lt <= this && this <= rules.gt)? \'value must be greater than %s or less than %s\'.format([rules.gt, rules.lt]) : \'\'\n\304\001\n\ruint64.gt_lte\032\262\001has(rules.lte) && rules.lte >= rules.gt && (this > rules.lte || this <= rules.gt)? \'value must be greater than %s and less than or equal to %s\'.format([rules.gt, rules.lte]) : \'\'\n\314\001\n\027uint64.gt_lte_exclusive\032\260\001has(rules.lte) && rules.lte < rules.gt && (rules.lte < this && this <= rules.gt)? \'value must be greater than %s or less than or equal to %s\'.format([rules.gt, rules.lte]) : \'\''
  _globals['_UINT64RULES'].fields_by_name['gte']._options = None
  _globals['_UINT64RULES'].fields_by_name['gte']._serialized_options = b'\302H\324\007\n\211\001\n\nuint64.gte\032{!has(rules.lt) && !has(rules.lte) && this < rules.gte? \'value must be greater than or equal to %s\'.format([rules.gte]) : \'\'\n\303\001\n\ruint64.gte_lt\032\261\001has(rules.lt) && rules.lt >= rules.gte && (this >= rules.lt || this < rules.gte)? \'value must be greater than or equal to %s and less than %s\'.format([rules.gte, rules.lt]) : \'\'\n\313\001\n\027uint64.gte_lt_exclusive\032\257\001has(rules.lt) && rules.lt < rules.gte && (rules.lt <= this && this < rules.gte)? \'value must be greater than or equal to %s or less than %s\'.format([rules.gte, rules.lt]) : \'\'\n\323\001\n\016uint64.gte_lte\032\300\001has(rules.lte) && rules.lte >= rules.gte && (this > rules.lte || this < rules.gte)? \'value must be greater than or equal to %s and less than or equal to %s\'.format([rules.gte, rules.lte]) : \'\'\n\333\001\n\030uint64.gte_lte_exclusive\032\276\001has(rules.lte) && rules.lte < rules.gte && (rules.lte < this && this < rules.gte)? \'value must be greater than or equal to %s or less than or equal to %s\'.format([rules.gte, rules.lte]) : \'\''
  _globals['_UINT64RULES'].fields_by_name['in']._options = None
  _globals['_UINT64RULES'].fields_by_name['in']._serialized_options = b'\302Hq\no\n\tuint64.in\032b!(this in getField(rules, \'in\')) ? \'value must be in list %s\'.format([getField(rules, \'in\')]) : \'\''
  _globals['_UINT64RULES'].fields_by_name['not_in']._options = None
  _globals['_UINT64RULES'].fields_by_name['not_in']._serialized_options = b'\302Hd\nb\n\ruint64.not_in\032Qthis in rules.not_in ? \'value must not be in list %s\'.format([rules.not_in]) : \'\''
  _globals['_UINT64RULES'].fields_by_name['example']._options = None
  _globals['_UINT64RULES'].fields_by_name['example']._serialized_options = b'\302H\030\n\026\n\016uint64.example\032\004true'
  _globals['_SINT32RULES'].fields_by_name['const']._options = None
  _globals['_SINT32RULES'].fields_by_name['const']._serialized_options = b'\302Hr\np\n\014sint32.const\032`this != getField(rules, \'const\') ? \'value must equal %s\'.format([getField(rules, \'const\')]) : \'\''
  _globals['_SINT32RULES'].fields_by_name['lt']._options = None
  _globals['_SINT32RULES'].fields_by_name['lt']._serialized_options = b'\302Hz\nx\n\tsint32.lt\032k!has(rules.gte) && !has(rules.gt) && this >= rules.lt? \'value must be less than %s\'.format([rules.lt]) : \'\''
  _globals['_SINT32RULES'].fields_by_name['lte']._options = None
  _globals['_SINT32RULES'].fields_by_name['lte']._serialized_options = b'\302H\211\001\n\206\001\n\nsint32.lte\032x!has(rules.gte) && !has(rules.gt) && this > rules.lte? \'value must be less than or equal to %s\'.format([rules.lte]) : \'\''
  _globals['_SINT32RULES'].fields_by_name['gt']._options = None
  _globals['_SINT32RULES'].fields_by_name['gt']._serialized_options = b'\302H\211\007\n{\n\tsint32.gt\032n!has(rules.lt) && !has(rules.lte) && this <= rules.gt? \'value must be greater than %s\'.format([rules.gt]) : \'\'\n\264\001\n\014sint32.gt_lt\032\243\001has(rules.lt) && rules.lt >= rules.gt && (this >= rules.lt || this <= rules.gt)? \'value must be greater than %s and less than %s\'.format([rules.gt, rules.lt]) : \'\'\n\274\001\n\026sint32.gt_lt_exclusive\032\241\001has(rules.lt) && rules.lt < rules.gt && (rules.lt <= this && this <= rules.gt)? \'value must be greater than %s or less than %s\'.format([rules.gt, rules.lt]) : \'\'\n\304\001\n\rsint32.gt_lte\032\262\001has(rules.lte) && rules.lte >= rules.gt && (this > rules.lte || this <= rules.gt)? \'value must be greater than %s and less than or equal to %s\'.format([rules.gt, rules.lte]) : \'\'\n\314\001\n\027sint32.gt_lte_exclusive\032\260\001has(rules.lte) && rules.lte < rules.gt && (rules.lte < this && this <= rules.gt)? \'value must be greater than %s or less than or equal to %s\'.format([rules.gt, rules.lte]) : \'\''
  _globals['_SINT32RULES'].fields_by_name['gte']._options = None
  _globals['_SINT32RULES'].fields_by_name['gte']._serialized_options = b'\302H\324\007\n\211\001\n\nsint32.gte\032{!has(rules.lt) && !has(rules.lte) && this < rules.gte? \'value must be greater than or equal to %s\'.format([rules.gte]) : \'\'\n\303\001\n\rsint32.gte_lt\032\261\001has(rules.lt) && rules.lt >= rules.gte && (this >= rules.lt || this < rules.gte)? \'value must be greater than or equal to %s and less than %s\'.format([rules.gte, rules.lt]) : \'\'\n\313\001\n\027sint32.gte_lt_exclusive\032\257\001has(rules.lt) && rules.lt < rules.gte && (rules.lt <= this && this < rules.gte)? \'value must be greater than or equal to %s or less than %s\'.format([rules.gte, rules.lt]) : \'\'\n\323\001\n\016sint32.gte_lte\032\300\001has(rules.lte) && rules.lte >= rules.gte && (this > rules.lte || this < rules.gte)? \'value must be greater than or equal to %s and less than or equal to %s\'.format([rules.gte, rules.lte]) : \'\'\n\333\001\n\030sint32.gte_lte_exclusive\032\276\001has(rules.lte) && rules.lte < rules.gte && (rules.lte < this && this < rules.gte)? \'value must be greater than or equal to %s or less than or equal to %s\'.format([rules.gte, rules.lte]) : \'\''
  _globals['_SINT32RULES'].fields_by_name['in']._options = None
  _globals['_SINT32RULES'].fields_by_name['in']._serialized_options = b'\302Hq\no\n\tsint32.in\032b!(this in getField(rules, \'in\')) ? \'value must be in list %s\'.format([getField(rules, \'in\')]) : \'\''
  _globals['_SINT32RULES'].fields_by_name['not_in']._options = None
  _globals['_SINT32RULES'].fields_by_name['not_in']._serialized_options = b'\302Hd\nb\n\rsint32.not_in\032Qthis in rules.not_in ? \'value must not be in list %s\'.format([rules.not_in]) : \'\''
  _globals['_SINT32RULES'].fields_by_name['example']._options = None
  _globals['_SINT32RULES'].fields_by_name['example']._serialized_options = b'\302H\030\n\026\n\016sint32.example\032\004true'
  _globals['_SINT64RULES'].fields_by_name['const']._options = None
  _globals['_SINT64RULES'].fields_by_name['const']._serialized_options = b'\302Hr\np\n\014sint64.const\032`this != getField(rules, \'const\') ? \'value must equal %s\'.format([getField(rules, \'const\')]) : \'\''
  _globals['_SINT64RULES'].fields_by_name['lt']._options = None
  _globals['_SINT64RULES'].fields_by_name['lt']._serialized_options = b'\302Hz\nx\n\tsint64.lt\032k!has(rules.gte) && !has(rules.gt) && this >= rules.lt? \'value must be less than %s\'.format([rules.lt]) : \'\''
  _globals['_SINT64RULES'].fields_by_name['lte']._options = None
  _globals['_SINT64RULES'].fields_by_name['lte']._serialized_options = b'\302H\211\001\n\206\001\n\nsint64.lte\032x!has(rules.gte) && !has(rules.gt) && this > rules.lte? \'value must be less than or equal to %s\'.format([rules.lte]) : \'\''
  _globals['_SINT64RULES'].fields_by_name['gt']._options = None
  _globals['_SINT64RULES'].fields_by_name['gt']._serialized_options = b'\302H\211\007\n{\n\tsint64.gt\032n!has(rules.lt) && !has(rules.lte) && this <= rules.gt? \'value must be greater than %s\'.format([rules.gt]) : \'\'\n\264\001\n\014sint64.gt_lt\032\243\001has(rules.lt) && rules.lt >= rules.gt && (this >= rules.lt || this <= rules.gt)? \'value must be greater than %s and less than %s\'.format([rules.gt, rules.lt]) : \'\'\n\274\001\n\026sint64.gt_lt_exclusive\032\241\001has(rules.lt) && rules.lt < rules.gt && (rules.lt <= this && this <= rules.gt)? \'value must be greater than %s or less than %s\'.format([rules.gt, rules.lt]) : \'\'\n\304\001\n\rsint64.gt_lte\032\262\001has(rules.lte) && rules.lte >= rules.gt && (this > rules.lte || this <= rules.gt)? \'value must be greater than %s and less than or equal to %s\'.format([rules.gt, rules.lte]) : \'\'\n\314\001\n\027sint64.gt_lte_exclusive\032\260\001has(rules.lte) && rules.lte < rules.gt && (rules.lte < this && this <= rules.gt)? \'value must be greater than %s or less than or equal to %s\'.format([rules.gt, rules.lte]) : \'\''
  _globals['_SINT64RULES'].fields_by_name['gte']._options = None
  _globals['_SINT64RULES'].fields_by_name['gte']._serialized_options = b'\302H\324\007\n\211\001\n\nsint64.gte\032{!has(rules.lt) && !has(rules.lte) && this < rules.gte? \'value must be greater than or equal to %s\'.format([rules.gte]) : \'\'\n\303\001\n\rsint64.gte_lt\032\261\001has(rules.lt) && rules.lt >= rules.gte && (this >= rules.lt || this < rules.gte)? \'value must be greater than or equal to %s and less than %s\'.format([rules.gte, rules.lt]) : \'\'\n\313\001\n\027sint64.gte_lt_exclusive\032\257\001has(rules.lt) && rules.lt < rules.gte && (rules.lt <= this && this < rules.gte)? \'value must be greater than or equal to %s or less than %s\'.format([rules.gte, rules.lt]) : \'\'\n\323\001\n\016sint64.gte_lte\032\300\001has(rules.lte) && rules.lte >= rules.gte && (this > rules.lte || this < rules.gte)? \'value must be greater than or equal to %s and less than or equal to %s\'.format([rules.gte, rules.lte]) : \'\'\n\333\001\n\030sint64.gte_lte_exclusive\032\276\001has(rules.lte) && rules.lte < rules.gte && (rules.lte < this && this < rules.gte)? \'value must be greater than or equal to %s or less than or equal to %s\'.format([rules.gte, rules.lte]) : \'\''
  _globals['_SINT64RULES'].fields_by_name['in']._options = None
  _globals['_SINT64RULES'].fields_by_name['in']._serialized_options = b'\302Hq\no\n\tsint64.in\032b!(this in getField(rules, \'in\')) ? \'value must be in list %s\'.format([getField(rules, \'in\')]) : \'\''
  _globals['_SINT64RULES'].fields_by_name['not_in']._options = None
  _globals['_SINT64RULES'].fields_by_name['not_in']._serialized_options = b'\302Hd\nb\n\rsint64.not_in\032Qthis in rules.not_in ? \'value must not be in list %s\'.format([rules.not_in]) : \'\''
  _globals['_SINT64RULES'].fields_by_name['example']._options = None
  _globals['_SINT64RULES'].fields_by_name['example']._serialized_options = b'\302H\030\n\026\n\016sint64.example\032\004true'
  _globals['_FIXED32RULES'].fields_by_name['const']._options = None
  _globals['_FIXED32RULES'].fields_by_name['const']._serialized_options = b'\302Hs\nq\n\rfixed32.const\032`this != getField(rules, \'const\') ? \'value must equal %s\'.format([getField(rules, \'const\')]) : \'\''
  _globals['_FIXED32RULES'].fields_by_name['lt']._options = None
  _globals['_FIXED32RULES'].fields_by_name['lt']._serialized_options = b'\302H{\ny\n\nfixed32.lt\032k!has(rules.gte) && !has(rules.gt) && this >= rules.lt? \'value must be less than %s\'.format([rules.lt]) : \'\''
  _globals['_FIXED32RULES'].fields_by_name['lte']._options = None
  _globals['_FIXED32RULES'].fields_by_name['lte']._serialized_options = b'\302H\212\001\n\207\001\n\013fixed32.lte\032x!has(rules.gte) && !has(rules.gt) && this > rules.lte? \'value must be less than or equal to %s\'.format([rules.lte]) : \'\''
  _globals['_FIXED32RULES'].fields_by_name['gt']._options = None
  _globals['_FIXED32RULES'].fields_by_name['gt']._serialized_options = b'\302H\216\007\n|\n\nfixed32.gt\032n!has(rules.lt) && !has(rules.lte) && this <= rules.gt? \'value must be greater than %s\'.format([rules.gt]) : \'\'\n\265\001\n\rfixed32.gt_lt\032\243\001has(rules.lt) && rules.lt >= rules.gt && (this >= rules.lt || this <= rules.gt)? \'value must be greater than %s and less than %s\'.format([rules.gt, rules.lt]) : \'\'\n\275\001\n\027fixed32.gt_lt_exclusive\032\241\001has(rules.lt) && rules.lt < rules.gt && (rules.lt <= this && this <= rules.gt)? \'value must be greater than %s or less than %s\'.format([rules.gt, rules.lt]) : \'\'\n\305\001\n\016fixed32.gt_lte\032\262\001has(rules.lte) && rules.lte >= rules.gt && (this > rules.lte || this <= rules.gt)? \'value must be greater than %s and less than or equal to %s\'.format([rules.gt, rules.lte]) : \'\'\n\315\001\n\030fixed32.gt_lte_exclusive\032\260\001has(rules.lte) && rules.lte < rules.gt && (rules.lte < this && this <= rules.gt)? \'value must be greater than %s or less than or equal to %s\'.format([rules.gt, rules.lte]) : \'\''
  _globals['_FIXED32RULES'].fields_by_name['gte']._options = None
  _globals['_FIXED32RULES'].fields_by_name['gte']._serialized_options = b'\302H\331\007\n\212\001\n\013fixed32.gte\032{!has(rules.lt) && !has(rules.lte) && this < rules.gte? \'value must be greater than or equal to %s\'.format([rules.gte]) : \'\'\n\304\001\n\016fixed32.gte_lt\032\261\001has(rules.lt) && rules.lt >= rules.gte && (this >= rules.lt || this < rules.gte)? \'value must be greater than or equal to %s and less than %s\'.format([rules.gte, rules.lt]) : \'\'\n\314\001\n\030fixed32.gte_lt_exclusive\032\257\001has(rules.lt) && rules.lt < rules.gte && (rules.lt <= this && this < rules.gte)? \'value must be greater than or equal to %s or less than %s\'.format([rules.gte, rules.lt]) : \'\'\n\324\001\n\017fixed32.gte_lte\032\300\001has(rules.lte) && rules.lte >= rules.gte && (this > rules.lte || this < rules.gte)? \'value must be greater than or equal to %s and less than or equal to %s\'.format([rules.gte, rules.lte]) : \'\'\n\334\001\n\031fixed32.gte_lte_exclusive\032\276\001has(rules.lte) && rules.lte < rules.gte && (rules.lte < this && this < rules.gte)? \'value must be greater than or equal to %s or less than or equal to %s\'.format([rules.gte, rules.lte]) : \'\''
  _globals['_FIXED32RULES'].fields_by_name['in']._options = None
  _globals['_FIXED32RULES'].fields_by_name['in']._serialized_options = b'\302Hr\np\n\nfixed32.in\032b!(this in getField(rules, \'in\')) ? \'value must be in list %s\'.format([getField(rules, \'in\')]) : \'\''
  _globals['_FIXED32RULES'].fields_by_name['not_in']._options = None
  _globals['_FIXED32RULES'].fields_by_name['not_in']._serialized_options = b'\302He\nc\n\016fixed32.not_in\032Qthis in rules.not_in ? \'value must not be in list %s\'.format([rules.not_in]) : \'\''
  _globals['_FIXED32RULES'].fields_by_name['example']._options = None
  _globals['_FIXED32RULES'].fields_by_name['example']._serialized_options = b'\302H\031\n\027\n\017fixed32.example\032\004true'
  _globals['_FIXED64RULES'].fields_by_name['const']._options = None
  _globals['_FIXED64RULES'].fields_by_name['const']._serialized_options = b'\302Hs\nq\n\rfixed64.const\032`this != getField(rules, \'const\') ? \'value must equal %s\'.format([getField(rules, \'const\')]) : \'\''
  _globals['_FIXED64RULES'].fields_by_name['lt']._options = None
  _globals['_FIXED64RULES'].fields_by_name['lt']._serialized_options = b'\302H{\ny\n\nfixed64.lt\032k!has(rules.gte) && !has(rules.gt) && this >= rules.lt? \'value must be less than %s\'.format([rules.lt]) : \'\''
  _globals['_FIXED64RULES'].fields_by_name['lte']._options = None
  _globals['_FIXED64RULES'].fields_by_name['lte']._serialized_options = b'\302H\212\001\n\207\001\n\013fixed64.lte\032x!has(rules.gte) && !has(rules.gt) && this > rules.lte? \'value must be less than or equal to %s\'.format([rules.lte]) : \'\''
  _globals['_FIXED64RULES'].fields_by_name['gt']._options = None
  _globals['_FIXED64RULES'].fields_by_name['gt']._serialized_options = b'\302H\216\007\n|\n\nfixed64.gt\032n!has(rules.lt) && !has(rules.lte) && this <= rules.gt? \'value must be greater than %s\'.format([rules.gt]) : \'\'\n\265\001\n\rfixed64.gt_lt\032\243\001has(rules.lt) && rules.lt >= rules.gt && (this >= rules.lt || this <= rules.gt)? \'value must be greater than %s and less than %s\'.format([rules.gt, rules.lt]) : \'\'\n\275\001\n\027fixed64.gt_lt_exclusive\032\241\001has(rules.lt) && rules.lt < rules.gt && (rules.lt <= this && this <= rules.gt)? \'value must be greater than %s or less than %s\'.format([rules.gt, rules.lt]) : \'\'\n\305\001\n\016fixed64.gt_lte\032\262\001has(rules.lte) && rules.lte >= rules.gt && (this > rules.lte || this <= rules.gt)? \'value must be greater than %s and less than or equal to %s\'.format([rules.gt, rules.lte]) : \'\'\n\315\001\n\030fixed64.gt_lte_exclusive\032\260\001has(rules.lte) && rules.lte < rules.gt && (rules.lte < this && this <= rules.gt)? \'value must be greater than %s or less than or equal to %s\'.format([rules.gt, rules.lte]) : \'\''
  _globals['_FIXED64RULES'].fields_by_name['gte']._options = None
  _globals['_FIXED64RULES'].fields_by_name['gte']._serialized_options = b'\302H\331\007\n\212\001\n\013fixed64.gte\032{!has(rules.lt) && !has(rules.lte) && this < rules.gte? \'value must be greater than or equal to %s\'.format([rules.gte]) : \'\'\n\304\001\n\016fixed64.gte_lt\032\261\001has(rules.lt) && rules.lt >= rules.gte && (this >= rules.lt || this < rules.gte)? \'value must be greater than or equal to %s and less than %s\'.format([rules.gte, rules.lt]) : \'\'\n\314\001\n\030fixed64.gte_lt_exclusive\032\257\001has(rules.lt) && rules.lt < rules.gte && (rules.lt <= this && this < rules.gte)? \'value must be greater than or equal to %s or less than %s\'.format([rules.gte, rules.lt]) : \'\'\n\324\001\n\017fixed64.gte_lte\032\300\001has(rules.lte) && rules.lte >= rules.gte && (this > rules.lte || this < rules.gte)? \'value must be greater than or equal to %s and less than or equal to %s\'.format([rules.gte, rules.lte]) : \'\'\n\334\001\n\031fixed64.gte_lte_exclusive\032\276\001has(rules.lte) && rules.lte < rules.gte && (rules.lte < this && this < rules.gte)? \'value must be greater than or equal to %s or less than or equal to %s\'.format([rules.gte, rules.lte]) : \'\''
  _globals['_FIXED64RULES'].fields_by_name['in']._options = None
  _globals['_FIXED64RULES'].fields_by_name['in']._serialized_options = b'\302Hr\np\n\nfixed64.in\032b!(this in getField(rules, \'in\')) ? \'value must be in list %s\'.format([getField(rules, \'in\')]) : \'\''
  _globals['_FIXED64RULES'].fields_by_name['not_in']._options = None
  _globals['_FIXED64RULES'].fields_by_name['not_in']._serialized_options = b'\302He\nc\n\016fixed64.not_in\032Qthis in rules.not_in ? \'value must not be in list %s\'.format([rules.not_in]) : \'\''
  _globals['_FIXED64RULES'].fields_by_name['example']._options = None
  _globals['_FIXED64RULES'].fields_by_name['example']._serialized_options = b'\302H\031\n\027\n\017fixed64.example\032\004true'
  _globals['_SFIXED32RULES'].fields_by_name['const']._options = None
  _globals['_SFIXED32RULES'].fields_by_name['const']._serialized_options = b'\302Ht\nr\n\016sfixed32.const\032`this != getField(rules, \'const\') ? \'value must equal %s\'.format([getField(rules, \'const\')]) : \'\''
  _globals['_SFIXED32RULES'].fields_by_name['lt']._options = None
  _globals['_SFIXED32RULES'].fields_by_name['lt']._serialized_options = b'\302H|\nz\n\013sfixed32.lt\032k!has(rules.gte) && !has(rules.gt) && this >= rules.lt? \'value must be less than %s\'.format([rules.lt]) : \'\''
  _globals['_SFIXED32RULES'].fields_by_name['lte']._options = None
  _globals['_SFIXED32RULES'].fields_by_name['lte']._serialized_options = b'\302H\213\001\n\210\001\n\014sfixed32.lte\032x!has(rules.gte) && !has(rules.gt) && this > rules.lte? \'value must be less than or equal to %s\'.format([rules.lte]) : \'\''
  _globals['_SFIXED32RULES'].fields_by_name['gt']._options = None
  _globals['_SFIXED32RULES'].fields_by_name['gt']._serialized_options = b'\302H\223\007\n}\n\013sfixed32.gt\032n!has(rules.lt) && !has(rules.lte) && this <= rules.gt? \'value must be greater than %s\'.format([rules.gt]) : \'\'\n\266\001\n\016sfixed32.gt_lt\032\243\001has(rules.lt) && rules.lt >= rules.gt && (this >= rules.lt || this <= rules.gt)? \'value must be greater than %s and less than %s\'.format([rules.gt, rules.lt]) : \'\'\n\276\001\n\030sfixed32.gt_lt_exclusive\032\241\001has(rules.lt) && rules.lt < rules.gt && (rules.lt <= this && this <= rules.gt)? \'value must be greater than %s or less than %s\'.format([rules.gt, rules.lt]) : \'\'\n\306\001\n\017sfixed32.gt_lte\032\262\001has(rules.lte) && rules.lte >= rules.gt && (this > rules.lte || this <= rules.gt)? \'value must be greater than %s and less than or equal to %s\'.format([rules.gt, rules.lte]) : \'\'\n\316\001\n\031sfixed32.gt_lte_exclusive\032\260\001has(rules.lte) && rules.lte < rules.gt && (rules.lte < this && this <= rules.gt)? \'value must be greater than %s or less than or equal to %s\'.format([rules.gt, rules.lte]) : \'\''
  _globals['_SFIXED32RULES'].fields_by_name['gte']._options = None
  _globals['_SFIXED32RULES'].fields_by_name['gte']._serialized_options = b'\302H\336\007\n\213\001\n\014sfixed32.gte\032{!has(rules.lt) && !has(rules.lte) && this < rules.gte? \'value must be greater than or equal to %s\'.format([rules.gte]) : \'\'\n\305\001\n\017sfixed32.gte_lt\032\261\001has(rules.lt) && rules.lt >= rules.gte && (this >= rules.lt || this < rules.gte)? \'value must be greater than or equal to %s and less than %s\'.format([rules.gte, rules.lt]) : \'\'\n\315\001\n\031sfixed32.gte_lt_exclusive\032\257\001has(rules.lt) && rules.lt < rules.gte && (rules.lt <= this && this < rules.gte)? \'value must be greater than or equal to %s or less than %s\'.format([rules.gte, rules.lt]) : \'\'\n\325\001\n\020sfixed32.gte_lte\032\300\001has(rules.lte) && rules.lte >= rules.gte && (this > rules.lte || this < rules.gte)? \'value must be greater than or equal to %s and less than or equal to %s\'.format([rules.gte, rules.lte]) : \'\'\n\335\001\n\032sfixed32.gte_lte_exclusive\032\276\001has(rules.lte) && rules.lte < rules.gte && (rules.lte < this && this < rules.gte)? \'value must be greater than or equal to %s or less than or equal to %s\'.format([rules.gte, rules.lte]) : \'\''
  _globals['_SFIXED32RULES'].fields_by_name['in']._options = None
  _globals['_SFIXED32RULES'].fields_by_name['in']._serialized_options = b'\302Hs\nq\n\013sfixed32.in\032b!(this in getField(rules, \'in\')) ? \'value must be in list %s\'.format([getField(rules, \'in\')]) : \'\''
  _globals['_SFIXED32RULES'].fields_by_name['not_in']._options = None
  _globals['_SFIXED32RULES'].fields_by_name['not_in']._serialized_options = b'\302Hf\nd\n\017sfixed32.not_in\032Qthis in rules.not_in ? \'value must not be in list %s\'.format([rules.not_in]) : \'\''
  _globals['_SFIXED32RULES'].fields_by_name['example']._options = None
  _globals['_SFIXED32RULES'].fields_by_name['example']._serialized_options = b'\302H\032\n\030\n\020sfixed32.example\032\004true'
  _globals['_SFIXED64RULES'].fields_by_name['const']._options = None
  _globals['_SFIXED64RULES'].fields_by_name['const']._serialized_options = b'\302Ht\nr\n\016sfixed64.const\032`this != getField(rules, \'const\') ? \'value must equal %s\'.format([getField(rules, \'const\')]) : \'\''
  _globals['_SFIXED64RULES'].fields_by_name['lt']._options = None
  _globals['_SFIXED64RULES'].fields_by_name['lt']._serialized_options = b'\302H|\nz\n\013sfixed64.lt\032k!has(rules.gte) && !has(rules.gt) && this >= rules.lt? \'value must be less than %s\'.format([rules.lt]) : \'\''
  _globals['_SFIXED64RULES'].fields_by_name['lte']._options = None
  _globals['_SFIXED64RULES'].fields_by_name['lte']._serialized_options = b'\302H\213\001\n\210\001\n\014sfixed64.lte\032x!has(rules.gte) && !has(rules.gt) && this > rules.lte? \'value must be less than or equal to %s\'.format([rules.lte]) : \'\''
  _globals['_SFIXED64RULES'].fields_by_name['gt']._options = None
  _globals['_SFIXED64RULES'].fields_by_name['gt']._serialized_options = b'\302H\223\007\n}\n\013sfixed64.gt\032n!has(rules.lt) && !has(rules.lte) && this <= rules.gt? \'value must be greater than %s\'.format([rules.gt]) : \'\'\n\266\001\n\016sfixed64.gt_lt\032\243\001has(rules.lt) && rules.lt >= rules.gt && (this >= rules.lt || this <= rules.gt)? \'value must be greater than %s and less than %s\'.format([rules.gt, rules.lt]) : \'\'\n\276\001\n\030sfixed64.gt_lt_exclusive\032\241\001has(rules.lt) && rules.lt < rules.gt && (rules.lt <= this && this <= rules.gt)? \'value must be greater than %s or less than %s\'.format([rules.gt, rules.lt]) : \'\'\n\306\001\n\017sfixed64.gt_lte\032\262\001has(rules.lte) && rules.lte >= rules.gt && (this > rules.lte || this <= rules.gt)? \'value must be greater than %s and less than or equal to %s\'.format([rules.gt, rules.lte]) : \'\'\n\316\001\n\031sfixed64.gt_lte_exclusive\032\260\001has(rules.lte) && rules.lte < rules.gt && (rules.lte < this && this <= rules.gt)? \'value must be greater than %s or less than or equal to %s\'.format([rules.gt, rules.lte]) : \'\''
  _globals['_SFIXED64RULES'].fields_by_name['gte']._options = None
  _globals['_SFIXED64RULES'].fields_by_name['gte']._serialized_options = b'\302H\336\007\n\213\001\n\014sfixed64.gte\032{!has(rules.lt) && !has(rules.lte) && this < rules.gte? \'value must be greater than or equal to %s\'.format([rules.gte]) : \'\'\n\305\001\n\017sfixed64.gte_lt\032\261\001has(rules.lt) && rules.lt >= rules.gte && (this >= rules.lt || this < rules.gte)? \'value must be greater than or equal to %s and less than %s\'.format([rules.gte, rules.lt]) : \'\'\n\315\001\n\031sfixed64.gte_lt_exclusive\032\257\001has(rules.lt) && rules.lt < rules.gte && (rules.lt <= this && this < rules.gte)? \'value must be greater than or equal to %s or less than %s\'.format([rules.gte, rules.lt]) : \'\'\n\325\001\n\020sfixed64.gte_lte\032\300\001has(rules.lte) && rules.lte >= rules.gte && (this > rules.lte || this < rules.gte)? \'value must be greater than or equal to %s and less than or equal to %s\'.format([rules.gte, rules.lte]) : \'\'\n\335\001\n\032sfixed64.gte_lte_exclusive\032\276\001has(rules.lte) && rules.lte < rules.gte && (rules.lte < this && this < rules.gte)? \'value must be greater than or equal to %s or less than or equal to %s\'.format([rules.gte, rules.lte]) : \'\''
  _globals['_SFIXED64RULES'].fields_by_name['in']._options = None
  _globals['_SFIXED64RULES'].fields_by_name['in']._serialized_options = b'\302Hs\nq\n\013sfixed64.in\032b!(this in getField(rules, \'in\')) ? \'value must be in list %s\'.format([getField(rules, \'in\')]) : \'\''
  _globals['_SFIXED64RULES'].fields_by_name['not_in']._options = None
  _globals['_SFIXED64RULES'].fields_by_name['not_in']._serialized_options = b'\302Hf\nd\n\017sfixed64.not_in\032Qthis in rules.not_in ? \'value must not be in list %s\'.format([rules.not_in]) : \'\''
  _globals['_SFIXED64RULES'].fields_by_name['example']._options = None
  _globals['_SFIXED64RULES'].fields_by_name['example']._serialized_options = b'\302H\032\n\030\n\020sfixed64.example\032\004true'
  _globals['_BOOLRULES'].fields_by_name['const']._options = None
  _globals['_BOOLRULES'].fields_by_name['const']._serialized_options = b'\302Hp\nn\n\nbool.const\032`this != getField(rules, \'const\') ? \'value must equal %s\'.format([getField(rules, \'const\')]) : \'\''
  _globals['_BOOLRULES'].fields_by_name['example']._options = None
  _globals['_BOOLRULES'].fields_by_name['example']._serialized_options = b'\302H\026\n\024\n\014bool.example\032\004true'
  _globals['_STRINGRULES'].fields_by_name['const']._options = None
  _globals['_STRINGRULES'].fields_by_name['const']._serialized_options = b'\302Ht\nr\n\014string.const\032bthis != getField(rules, \'const\') ? \'value must equal `%s`\'.format([getField(rules, \'const\')]) : \'\''
  _globals['_STRINGRULES'].fields_by_name['len']._options = None
  _globals['_STRINGRULES'].fields_by_name['len']._serialized_options = b'\302Hn\nl\n\nstring.len\032^uint(this.size()) != rules.len ? \'value length must be %s characters\'.format([rules.len]) : \'\''
  _globals['_STRINGRULES'].fields_by_name['min_len']._options = None
  _globals['_STRINGRULES'].fields_by_name['min_len']._serialized_options = b'\302H\203\001\n\200\001\n\016string.min_len\032nuint(this.size()) < rules.min_len ? \'value length must be at least %s characters\'.format([rules.min_len]) : \'\''
  _globals['_STRINGRULES'].fields_by_name['max_len']._options = None
  _globals['_STRINGRULES'].fields_by_name['max_len']._serialized_options = b'\302H\201\001\n\177\n\016string.max_len\032muint(this.size()) > rules.max_len ? \'value length must be at most %s characters\'.format([rules.max_len]) : \'\''
  _globals['_STRINGRULES'].fields_by_name['len_bytes']._options = None
  _globals['_STRINGRULES'].fields_by_name['len_bytes']._serialized_options = b'\302H\203\001\n\200\001\n\020string.len_bytes\032luint(bytes(this).size()) != rules.len_bytes ? \'value length must be %s bytes\'.format([rules.len_bytes]) : \'\''
  _globals['_STRINGRULES'].fields_by_name['min_bytes']._options = None
  _globals['_STRINGRULES'].fields_by_name['min_bytes']._serialized_options = b'\302H\213\001\n\210\001\n\020string.min_bytes\032tuint(bytes(this).size()) < rules.min_bytes ? \'value length must be at least %s bytes\'.format([rules.min_bytes]) : \'\''
  _globals['_STRINGRULES'].fields_by_name['max_bytes']._options = None
  _globals['_STRINGRULES'].fields_by_name['max_bytes']._serialized_options = b'\302H\212\001\n\207\001\n\020string.max_bytes\032suint(bytes(this).size()) > rules.max_bytes ? \'value length must be at most %s bytes\'.format([rules.max_bytes]) : \'\''
  _globals['_STRINGRULES'].fields_by_name['pattern']._options = None
  _globals['_STRINGRULES'].fields_by_name['pattern']._serialized_options = b'\302Hy\nw\n\016string.pattern\032e!this.matches(rules.pattern) ? \'value does not match regex pattern `%s`\'.format([rules.pattern]) : \'\''
  _globals['_STRINGRULES'].fields_by_name['prefix']._options = None
  _globals['_STRINGRULES'].fields_by_name['prefix']._serialized_options = b'\302Hq\no\n\rstring.prefix\032^!this.startsWith(rules.prefix) ? \'value does not have prefix `%s`\'.format([rules.prefix]) : \'\''
  _globals['_STRINGRULES'].fields_by_name['suffix']._options = None
  _globals['_STRINGRULES'].fields_by_name['suffix']._serialized_options = b'\302Ho\nm\n\rstring.suffix\032\\!this.endsWith(rules.suffix) ? \'value does not have suffix `%s`\'.format([rules.suffix]) : \'\''
  _globals['_STRINGRULES'].fields_by_name['contains']._options = None
  _globals['_STRINGRULES'].fields_by_name['contains']._serialized_options = b'\302H{\ny\n\017string.contains\032f!this.contains(rules.contains) ? \'value does not contain substring `%s`\'.format([rules.contains]) : \'\''
  _globals['_STRINGRULES'].fields_by_name['not_contains']._options = None
  _globals['_STRINGRULES'].fields_by_name['not_contains']._serialized_options = b'\302H~\n|\n\023string.not_contains\032ethis.contains(rules.not_contains) ? \'value contains substring `%s`\'.format([rules.not_contains]) : \'\''
  _globals['_STRINGRULES'].fields_by_name['in']._options = None
  _globals['_STRINGRULES'].fields_by_name['in']._serialized_options = b'\302Hq\no\n\tstring.in\032b!(this in getField(rules, \'in\')) ? \'value must be in list %s\'.format([getField(rules, \'in\')]) : \'\''
  _globals['_STRINGRULES'].fields_by_name['not_in']._options = None
  _globals['_STRINGRULES'].fields_by_name['not_in']._serialized_options = b'\302Hd\nb\n\rstring.not_in\032Qthis in rules.not_in ? \'value must not be in list %s\'.format([rules.not_in]) : \'\''
  _globals['_STRINGRULES'].fields_by_name['email']._options = None
  _globals['_STRINGRULES'].fields_by_name['email']._serialized_options = b'\302H\311\001\na\n\014string.email\022#value must be a valid email address\032,!rules.email || this == \'\' || this.isEmail()\nd\n\022string.email_empty\0222value is empty, which is not a valid email address\032\032!rules.email || this != \'\''
  _globals['_STRINGRULES'].fields_by_name['hostname']._options = None
  _globals['_STRINGRULES'].fields_by_name['hostname']._serialized_options = b'\302H\316\001\ne\n\017string.hostname\022\036value must be a valid hostname\0322!rules.hostname || this == \'\' || this.isHostname()\ne\n\025string.hostname_empty\022-value is empty, which is not a valid hostname\032\035!rules.hostname || this != \'\''
  _globals['_STRINGRULES'].fields_by_name['ip']._options = None
  _globals['_STRINGRULES'].fields_by_name['ip']._serialized_options = b'\302H\264\001\nU\n\tstring.ip\022 value must be a valid IP address\032&!rules.ip || this == \'\' || this.isIp()\n[\n\017string.ip_empty\022/value is empty, which is not a valid IP address\032\027!rules.ip || this != \'\''
  _globals['_STRINGRULES'].fields_by_name['ipv4']._options = None
  _globals['_STRINGRULES'].fields_by_name['ipv4']._serialized_options = b'\302H\301\001\n\\\n\013string.ipv4\022\"value must be a valid IPv4 address\032)!rules.ipv4 || this == \'\' || this.isIp(4)\na\n\021string.ipv4_empty\0221value is empty, which is not a valid IPv4 address\032\031!rules.ipv4 || this != \'\''
  _globals['_STRINGRULES'].fields_by_name['ipv6']._options = None
  _globals['_STRINGRULES'].fields_by_name['ipv6']._serialized_options = b'\302H\301\001\n\\\n\013string.ipv6\022\"value must be a valid IPv6 address\032)!rules.ipv6 || this == \'\' || this.isIp(6)\na\n\021string.ipv6_empty\0221value is empty, which is not a valid IPv6 address\032\031!rules.ipv6 || this != \'\''
  _globals['_STRINGRULES'].fields_by_name['uri']._options = None
  _globals['_STRINGRULES'].fields_by_name['uri']._serialized_options = b'\302H\253\001\nQ\n\nstring.uri\022\031value must be a valid URI\032(!rules.uri || this == \'\' || this.isUri()\nV\n\020string.uri_empty\022(value is empty, which is not a valid URI\032\030!rules.uri || this != \'\''
  _globals['_STRINGRULES'].fields_by_name['uri_ref']._options = None
  _globals['_STRINGRULES'].fields_by_name['uri_ref']._serialized_options = b'\302HZ\nX\n\016string.uri_ref\022#value must be a valid URI Reference\032!!rules.uri_ref || this.isUriRef()'
  _globals['_STRINGRULES'].fields_by_name['address']._options = None
  _globals['_STRINGRULES'].fields_by_name['address']._serialized_options = b'\302H\370\001\n\201\001\n\016string.address\022-value must be a valid hostname, or ip address\032@!rules.address || this == \'\' || this.isHostname() || this.isIp()\nr\n\024string.address_empty\022<value is empty, which is not a valid hostname, or ip address\032\034!rules.address || this != \'\''
  _globals['_STRINGRULES'].fields_by_name['uuid']._options = None
  _globals['_STRINGRULES'].fields_by_name['uuid']._serialized_options = b'\302H\203\002\n\245\001\n\013string.uuid\022\032value must be a valid UUID\032z!rules.uuid || this == \'\' || this.matches(\'^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$\')\nY\n\021string.uuid_empty\022)value is empty, which is not a valid UUID\032\031!rules.uuid || this != \'\''
  _globals['_STRINGRULES'].fields_by_name['tuuid']._options = None
  _globals['_STRINGRULES'].fields_by_name['tuuid']._serialized_options = b'\302H\332\001\ns\n\014string.tuuid\022\"value must be a valid trimmed UUID\032?!rules.tuuid || this == \'\' || this.matches(\'^[0-9a-fA-F]{32}$\')\nc\n\022string.tuuid_empty\0221value is empty, which is not a valid trimmed UUID\032\032!rules.tuuid || this != \'\''
  _globals['_STRINGRULES'].fields_by_name['ip_with_prefixlen']._options = None
  _globals['_STRINGRULES'].fields_by_name['ip_with_prefixlen']._serialized_options = b'\302H\364\001\nx\n\030string.ip_with_prefixlen\022\037value must be a valid IP prefix\032;!rules.ip_with_prefixlen || this == \'\' || this.isIpPrefix()\nx\n\036string.ip_with_prefixlen_empty\022.value is empty, which is not a valid IP prefix\032&!rules.ip_with_prefixlen || this != \'\''
  _globals['_STRINGRULES'].fields_by_name['ipv4_with_prefixlen']._options = None
  _globals['_STRINGRULES'].fields_by_name['ipv4_with_prefixlen']._serialized_options = b'\302H\253\002\n\223\001\n\032string.ipv4_with_prefixlen\0225value must be a valid IPv4 address with prefix length\032>!rules.ipv4_with_prefixlen || this == \'\' || this.isIpPrefix(4)\n\222\001\n string.ipv4_with_prefixlen_empty\022Dvalue is empty, which is not a valid IPv4 address with prefix length\032(!rules.ipv4_with_prefixlen || this != \'\''
  _globals['_STRINGRULES'].fields_by_name['ipv6_with_prefixlen']._options = None
  _globals['_STRINGRULES'].fields_by_name['ipv6_with_prefixlen']._serialized_options = b'\302H\253\002\n\223\001\n\032string.ipv6_with_prefixlen\0225value must be a valid IPv6 address with prefix length\032>!rules.ipv6_with_prefixlen || this == \'\' || this.isIpPrefix(6)\n\222\001\n string.ipv6_with_prefixlen_empty\022Dvalue is empty, which is not a valid IPv6 address with prefix length\032(!rules.ipv6_with_prefixlen || this != \'\''
  _globals['_STRINGRULES'].fields_by_name['ip_prefix']._options = None
  _globals['_STRINGRULES'].fields_by_name['ip_prefix']._serialized_options = b'\302H\330\001\nl\n\020string.ip_prefix\022\037value must be a valid IP prefix\0327!rules.ip_prefix || this == \'\' || this.isIpPrefix(true)\nh\n\026string.ip_prefix_empty\022.value is empty, which is not a valid IP prefix\032\036!rules.ip_prefix || this != \'\''
  _globals['_STRINGRULES'].fields_by_name['ipv4_prefix']._options = None
  _globals['_STRINGRULES'].fields_by_name['ipv4_prefix']._serialized_options = b'\302H\347\001\nu\n\022string.ipv4_prefix\022!value must be a valid IPv4 prefix\032<!rules.ipv4_prefix || this == \'\' || this.isIpPrefix(4, true)\nn\n\030string.ipv4_prefix_empty\0220value is empty, which is not a valid IPv4 prefix\032 !rules.ipv4_prefix || this != \'\''
  _globals['_STRINGRULES'].fields_by_name['ipv6_prefix']._options = None
  _globals['_STRINGRULES'].fields_by_name['ipv6_prefix']._serialized_options = b'\302H\347\001\nu\n\022string.ipv6_prefix\022!value must be a valid IPv6 prefix\032<!rules.ipv6_prefix || this == \'\' || this.isIpPrefix(6, true)\nn\n\030string.ipv6_prefix_empty\0220value is empty, which is not a valid IPv6 prefix\032 !rules.ipv6_prefix || this != \'\''
  _globals['_STRINGRULES'].fields_by_name['host_and_port']._options = None
  _globals['_STRINGRULES'].fields_by_name['host_and_port']._serialized_options = b'\302H\227\002\n\231\001\n\024string.host_and_port\022Avalue must be a valid host (hostname or IP address) and port pair\032>!rules.host_and_port || this == \'\' || this.isHostAndPort(true)\ny\n\032string.host_and_port_empty\0227value is empty, which is not a valid host and port pair\032\"!rules.host_and_port || this != \'\''
  _globals['_STRINGRULES'].fields_by_name['well_known_regex']._options = None
  _globals['_STRINGRULES'].fields_by_name['well_known_regex']._serialized_options = b'\302H\355\004\n\360\001\n#string.well_known_regex.header_name\022&value must be a valid HTTP header name\032\240\001rules.well_known_regex != 1 || this == \'\' || this.matches(!has(rules.strict) || rules.strict ?\'^:?[0-9a-zA-Z!#$%&\\\'*+-.^_|~\\x60]+$\' :\'^[^\\u0000\\u000A\\u000D]+$\')\n\215\001\n)string.well_known_regex.header_name_empty\0225value is empty, which is not a valid HTTP header name\032)rules.well_known_regex != 1 || this != \'\'\n\347\001\n$string.well_known_regex.header_value\022\'value must be a valid HTTP header value\032\225\001rules.well_known_regex != 2 || this.matches(!has(rules.strict) || rules.strict ?\'^[^\\u0000-\\u0008\\u000A-\\u001F\\u007F]*$\' :\'^[^\\u0000\\u000A\\u000D]*$\')'
  _globals['_STRINGRULES'].fields_by_name['example']._options = None
  _globals['_STRINGRULES'].fields_by_name['example']._serialized_options = b'\302H\030\n\026\n\016string.example\032\004true'
  _globals['_BYTESRULES'].fields_by_name['const']._options = None
  _globals['_BYTESRULES'].fields_by_name['const']._serialized_options = b'\302Hn\nl\n\013bytes.const\032]this != getField(rules, \'const\') ? \'value must be %x\'.format([getField(rules, \'const\')]) : \'\''
  _globals['_BYTESRULES'].fields_by_name['len']._options = None
  _globals['_BYTESRULES'].fields_by_name['len']._serialized_options = b'\302Hh\nf\n\tbytes.len\032Yuint(this.size()) != rules.len ? \'value length must be %s bytes\'.format([rules.len]) : \'\''
  _globals['_BYTESRULES'].fields_by_name['min_len']._options = None
  _globals['_BYTESRULES'].fields_by_name['min_len']._serialized_options = b'\302H|\nz\n\rbytes.min_len\032iuint(this.size()) < rules.min_len ? \'value length must be at least %s bytes\'.format([rules.min_len]) : \'\''
  _globals['_BYTESRULES'].fields_by_name['max_len']._options = None
  _globals['_BYTESRULES'].fields_by_name['max_len']._serialized_options = b'\302Ht\nr\n\rbytes.max_len\032auint(this.size()) > rules.max_len ? \'value must be at most %s bytes\'.format([rules.max_len]) : \'\''
  _globals['_BYTESRULES'].fields_by_name['pattern']._options = None
  _globals['_BYTESRULES'].fields_by_name['pattern']._serialized_options = b'\302H|\nz\n\rbytes.pattern\032i!string(this).matches(rules.pattern) ? \'value must match regex pattern `%s`\'.format([rules.pattern]) : \'\''
  _globals['_BYTESRULES'].fields_by_name['prefix']._options = None
  _globals['_BYTESRULES'].fields_by_name['prefix']._serialized_options = b'\302Hn\nl\n\014bytes.prefix\032\\!this.startsWith(rules.prefix) ? \'value does not have prefix %x\'.format([rules.prefix]) : \'\''
  _globals['_BYTESRULES'].fields_by_name['suffix']._options = None
  _globals['_BYTESRULES'].fields_by_name['suffix']._serialized_options = b'\302Hl\nj\n\014bytes.suffix\032Z!this.endsWith(rules.suffix) ? \'value does not have suffix %x\'.format([rules.suffix]) : \'\''
  _globals['_BYTESRULES'].fields_by_name['contains']._options = None
  _globals['_BYTESRULES'].fields_by_name['contains']._serialized_options = b'\302Hn\nl\n\016bytes.contains\032Z!this.contains(rules.contains) ? \'value does not contain %x\'.format([rules.contains]) : \'\''
  _globals['_BYTESRULES'].fields_by_name['in']._options = None
  _globals['_BYTESRULES'].fields_by_name['in']._serialized_options = b'\302H\226\001\n\223\001\n\010bytes.in\032\206\001getField(rules, \'in\').size() > 0 && !(this in getField(rules, \'in\')) ? \'value must be in list %s\'.format([getField(rules, \'in\')]) : \'\''
  _globals['_BYTESRULES'].fields_by_name['not_in']._options = None
  _globals['_BYTESRULES'].fields_by_name['not_in']._serialized_options = b'\302Hc\na\n\014bytes.not_in\032Qthis in rules.not_in ? \'value must not be in list %s\'.format([rules.not_in]) : \'\''
  _globals['_BYTESRULES'].fields_by_name['ip']._options = None
  _globals['_BYTESRULES'].fields_by_name['ip']._serialized_options = b'\302H\330\001\nt\n\010bytes.ip\022 value must be a valid IP address\032F!rules.ip || this.size() == 0 || this.size() == 4 || this.size() == 16\n`\n\016bytes.ip_empty\022/value is empty, which is not a valid IP address\032\035!rules.ip || this.size() != 0'
  _globals['_BYTESRULES'].fields_by_name['ipv4']._options = None
  _globals['_BYTESRULES'].fields_by_name['ipv4']._serialized_options = b'\302H\317\001\ne\n\nbytes.ipv4\022\"value must be a valid IPv4 address\0323!rules.ipv4 || this.size() == 0 || this.size() == 4\nf\n\020bytes.ipv4_empty\0221value is empty, which is not a valid IPv4 address\032\037!rules.ipv4 || this.size() != 0'
  _globals['_BYTESRULES'].fields_by_name['ipv6']._options = None
  _globals['_BYTESRULES'].fields_by_name['ipv6']._serialized_options = b'\302H\320\001\nf\n\nbytes.ipv6\022\"value must be a valid IPv6 address\0324!rules.ipv6 || this.size() == 0 || this.size() == 16\nf\n\020bytes.ipv6_empty\0221value is empty, which is not a valid IPv6 address\032\037!rules.ipv6 || this.size() != 0'
  _globals['_BYTESRULES'].fields_by_name['example']._options = None
  _globals['_BYTESRULES'].fields_by_name['example']._serialized_options = b'\302H\027\n\025\n\rbytes.example\032\004true'
  _globals['_ENUMRULES'].fields_by_name['const']._options = None
  _globals['_ENUMRULES'].fields_by_name['const']._serialized_options = b'\302Hp\nn\n\nenum.const\032`this != getField(rules, \'const\') ? \'value must equal %s\'.format([getField(rules, \'const\')]) : \'\''
  _globals['_ENUMRULES'].fields_by_name['in']._options = None
  _globals['_ENUMRULES'].fields_by_name['in']._serialized_options = b'\302Ho\nm\n\007enum.in\032b!(this in getField(rules, \'in\')) ? \'value must be in list %s\'.format([getField(rules, \'in\')]) : \'\''
  _globals['_ENUMRULES'].fields_by_name['not_in']._options = None
  _globals['_ENUMRULES'].fields_by_name['not_in']._serialized_options = b'\302Hb\n`\n\013enum.not_in\032Qthis in rules.not_in ? \'value must not be in list %s\'.format([rules.not_in]) : \'\''
  _globals['_ENUMRULES'].fields_by_name['example']._options = None
  _globals['_ENUMRULES'].fields_by_name['example']._serialized_options = b'\302H\026\n\024\n\014enum.example\032\004true'
  _globals['_REPEATEDRULES'].fields_by_name['min_items']._options = None
  _globals['_REPEATEDRULES'].fields_by_name['min_items']._serialized_options = b'\302H\206\001\n\203\001\n\022repeated.min_items\032muint(this.size()) < rules.min_items ? \'value must contain at least %d item(s)\'.format([rules.min_items]) : \'\''
  _globals['_REPEATEDRULES'].fields_by_name['max_items']._options = None
  _globals['_REPEATEDRULES'].fields_by_name['max_items']._serialized_options = b'\302H\212\001\n\207\001\n\022repeated.max_items\032quint(this.size()) > rules.max_items ? \'value must contain no more than %s item(s)\'.format([rules.max_items]) : \'\''
  _globals['_REPEATEDRULES'].fields_by_name['unique']._options = None
  _globals['_REPEATEDRULES'].fields_by_name['unique']._serialized_options = b'\302H]\n[\n\017repeated.unique\022(repeated value must contain unique items\032\036!rules.unique || this.unique()'
  _globals['_MAPRULES'].fields_by_name['min_pairs']._options = None
  _globals['_MAPRULES'].fields_by_name['min_pairs']._serialized_options = b'\302Hy\nw\n\rmap.min_pairs\032fuint(this.size()) < rules.min_pairs ? \'map must be at least %d entries\'.format([rules.min_pairs]) : \'\''
  _globals['_MAPRULES'].fields_by_name['max_pairs']._options = None
  _globals['_MAPRULES'].fields_by_name['max_pairs']._serialized_options = b'\302Hx\nv\n\rmap.max_pairs\032euint(this.size()) > rules.max_pairs ? \'map must be at most %d entries\'.format([rules.max_pairs]) : \'\''
  _globals['_DURATIONRULES'].fields_by_name['const']._options = None
  _globals['_DURATIONRULES'].fields_by_name['const']._serialized_options = b'\302Ht\nr\n\016duration.const\032`this != getField(rules, \'const\') ? \'value must equal %s\'.format([getField(rules, \'const\')]) : \'\''
  _globals['_DURATIONRULES'].fields_by_name['lt']._options = None
  _globals['_DURATIONRULES'].fields_by_name['lt']._serialized_options = b'\302H|\nz\n\013duration.lt\032k!has(rules.gte) && !has(rules.gt) && this >= rules.lt? \'value must be less than %s\'.format([rules.lt]) : \'\''
  _globals['_DURATIONRULES'].fields_by_name['lte']._options = None
  _globals['_DURATIONRULES'].fields_by_name['lte']._serialized_options = b'\302H\213\001\n\210\001\n\014duration.lte\032x!has(rules.gte) && !has(rules.gt) && this > rules.lte? \'value must be less than or equal to %s\'.format([rules.lte]) : \'\''
  _globals['_DURATIONRULES'].fields_by_name['gt']._options = None
  _globals['_DURATIONRULES'].fields_by_name['gt']._serialized_options = b'\302H\223\007\n}\n\013duration.gt\032n!has(rules.lt) && !has(rules.lte) && this <= rules.gt? \'value must be greater than %s\'.format([rules.gt]) : \'\'\n\266\001\n\016duration.gt_lt\032\243\001has(rules.lt) && rules.lt >= rules.gt && (this >= rules.lt || this <= rules.gt)? \'value must be greater than %s and less than %s\'.format([rules.gt, rules.lt]) : \'\'\n\276\001\n\030duration.gt_lt_exclusive\032\241\001has(rules.lt) && rules.lt < rules.gt && (rules.lt <= this && this <= rules.gt)? \'value must be greater than %s or less than %s\'.format([rules.gt, rules.lt]) : \'\'\n\306\001\n\017duration.gt_lte\032\262\001has(rules.lte) && rules.lte >= rules.gt && (this > rules.lte || this <= rules.gt)? \'value must be greater than %s and less than or equal to %s\'.format([rules.gt, rules.lte]) : \'\'\n\316\001\n\031duration.gt_lte_exclusive\032\260\001has(rules.lte) && rules.lte < rules.gt && (rules.lte < this && this <= rules.gt)? \'value must be greater than %s or less than or equal to %s\'.format([rules.gt, rules.lte]) : \'\''
  _globals['_DURATIONRULES'].fields_by_name['gte']._options = None
  _globals['_DURATIONRULES'].fields_by_name['gte']._serialized_options = b'\302H\336\007\n\213\001\n\014duration.gte\032{!has(rules.lt) && !has(rules.lte) && this < rules.gte? \'value must be greater than or equal to %s\'.format([rules.gte]) : \'\'\n\305\001\n\017duration.gte_lt\032\261\001has(rules.lt) && rules.lt >= rules.gte && (this >= rules.lt || this < rules.gte)? \'value must be greater than or equal to %s and less than %s\'.format([rules.gte, rules.lt]) : \'\'\n\315\001\n\031duration.gte_lt_exclusive\032\257\001has(rules.lt) && rules.lt < rules.gte && (rules.lt <= this && this < rules.gte)? \'value must be greater than or equal to %s or less than %s\'.format([rules.gte, rules.lt]) : \'\'\n\325\001\n\020duration.gte_lte\032\300\001has(rules.lte) && rules.lte >= rules.gte && (this > rules.lte || this < rules.gte)? \'value must be greater than or equal to %s and less than or equal to %s\'.format([rules.gte, rules.lte]) : \'\'\n\335\001\n\032duration.gte_lte_exclusive\032\276\001has(rules.lte) && rules.lte < rules.gte && (rules.lte < this && this < rules.gte)? \'value must be greater than or equal to %s or less than or equal to %s\'.format([rules.gte, rules.lte]) : \'\''
  _globals['_DURATIONRULES'].fields_by_name['in']._options = None
  _globals['_DURATIONRULES'].fields_by_name['in']._serialized_options = b'\302Hs\nq\n\013duration.in\032b!(this in getField(rules, \'in\')) ? \'value must be in list %s\'.format([getField(rules, \'in\')]) : \'\''
  _globals['_DURATIONRULES'].fields_by_name['not_in']._options = None
  _globals['_DURATIONRULES'].fields_by_name['not_in']._serialized_options = b'\302Hf\nd\n\017duration.not_in\032Qthis in rules.not_in ? \'value must not be in list %s\'.format([rules.not_in]) : \'\''
  _globals['_DURATIONRULES'].fields_by_name['example']._options = None
  _globals['_DURATIONRULES'].fields_by_name['example']._serialized_options = b'\302H\032\n\030\n\020duration.example\032\004true'
  _globals['_TIMESTAMPRULES'].fields_by_name['const']._options = None
  _globals['_TIMESTAMPRULES'].fields_by_name['const']._serialized_options = b'\302Hu\ns\n\017timestamp.const\032`this != getField(rules, \'const\') ? \'value must equal %s\'.format([getField(rules, \'const\')]) : \'\''
  _globals['_TIMESTAMPRULES'].fields_by_name['lt']._options = None
  _globals['_TIMESTAMPRULES'].fields_by_name['lt']._serialized_options = b'\302H}\n{\n\014timestamp.lt\032k!has(rules.gte) && !has(rules.gt) && this >= rules.lt? \'value must be less than %s\'.format([rules.lt]) : \'\''
  _globals['_TIMESTAMPRULES'].fields_by_name['lte']._options = None
  _globals['_TIMESTAMPRULES'].fields_by_name['lte']._serialized_options = b'\302H\214\001\n\211\001\n\rtimestamp.lte\032x!has(rules.gte) && !has(rules.gt) && this > rules.lte? \'value must be less than or equal to %s\'.format([rules.lte]) : \'\''
  _globals['_TIMESTAMPRULES'].fields_by_name['lt_now']._options = None
  _globals['_TIMESTAMPRULES'].fields_by_name['lt_now']._serialized_options = b'\302HW\nU\n\020timestamp.lt_now\032A(rules.lt_now && this > now) ? \'value must be less than now\' : \'\''
  _globals['_TIMESTAMPRULES'].fields_by_name['gt']._options = None
  _globals['_TIMESTAMPRULES'].fields_by_name['gt']._serialized_options = b'\302H\230\007\n~\n\014timestamp.gt\032n!has(rules.lt) && !has(rules.lte) && this <= rules.gt? \'value must be greater than %s\'.format([rules.gt]) : \'\'\n\267\001\n\017timestamp.gt_lt\032\243\001has(rules.lt) && rules.lt >= rules.gt && (this >= rules.lt || this <= rules.gt)? \'value must be greater than %s and less than %s\'.format([rules.gt, rules.lt]) : \'\'\n\277\001\n\031timestamp.gt_lt_exclusive\032\241\001has(rules.lt) && rules.lt < rules.gt && (rules.lt <= this && this <= rules.gt)? \'value must be greater than %s or less than %s\'.format([rules.gt, rules.lt]) : \'\'\n\307\001\n\020timestamp.gt_lte\032\262\001has(rules.lte) && rules.lte >= rules.gt && (this > rules.lte || this <= rules.gt)? \'value must be greater than %s and less than or equal to %s\'.format([rules.gt, rules.lte]) : \'\'\n\317\001\n\032timestamp.gt_lte_exclusive\032\260\001has(rules.lte) && rules.lte < rules.gt && (rules.lte < this && this <= rules.gt)? \'value must be greater than %s or less than or equal to %s\'.format([rules.gt, rules.lte]) : \'\''
  _globals['_TIMESTAMPRULES'].fields_by_name['gte']._options = None
  _globals['_TIMESTAMPRULES'].fields_by_name['gte']._serialized_options = b'\302H\343\007\n\214\001\n\rtimestamp.gte\032{!has(rules.lt) && !has(rules.lte) && this < rules.gte? \'value must be greater than or equal to %s\'.format([rules.gte]) : \'\'\n\306\001\n\020timestamp.gte_lt\032\261\001has(rules.lt) && rules.lt >= rules.gte && (this >= rules.lt || this < rules.gte)? \'value must be greater than or equal to %s and less than %s\'.format([rules.gte, rules.lt]) : \'\'\n\316\001\n\032timestamp.gte_lt_exclusive\032\257\001has(rules.lt) && rules.lt < rules.gte && (rules.lt <= this && this < rules.gte)? \'value must be greater than or equal to %s or less than %s\'.format([rules.gte, rules.lt]) : \'\'\n\326\001\n\021timestamp.gte_lte\032\300\001has(rules.lte) && rules.lte >= rules.gte && (this > rules.lte || this < rules.gte)? \'value must be greater than or equal to %s and less than or equal to %s\'.format([rules.gte, rules.lte]) : \'\'\n\336\001\n\033timestamp.gte_lte_exclusive\032\276\001has(rules.lte) && rules.lte < rules.gte && (rules.lte < this && this < rules.gte)? \'value must be greater than or equal to %s or less than or equal to %s\'.format([rules.gte, rules.lte]) : \'\''
  _globals['_TIMESTAMPRULES'].fields_by_name['gt_now']._options = None
  _globals['_TIMESTAMPRULES'].fields_by_name['gt_now']._serialized_options = b'\302HZ\nX\n\020timestamp.gt_now\032D(rules.gt_now && this < now) ? \'value must be greater than now\' : \'\''
  _globals['_TIMESTAMPRULES'].fields_by_name['within']._options = None
  _globals['_TIMESTAMPRULES'].fields_by_name['within']._serialized_options = b'\302H\210\001\n\205\001\n\020timestamp.within\032qthis < now-rules.within || this > now+rules.within ? \'value must be within %s of now\'.format([rules.within]) : \'\''
  _globals['_TIMESTAMPRULES'].fields_by_name['example']._options = None
  _globals['_TIMESTAMPRULES'].fields_by_name['example']._serialized_options = b'\302H\033\n\031\n\021timestamp.example\032\004true'
  _globals['_IGNORE']._serialized_start=52405
  _globals['_IGNORE']._serialized_end=52540
  _globals['_KNOWNREGEX']._serialized_start=52542
  _globals['_KNOWNREGEX']._serialized_end=52652
  _globals['_RULE']._serialized_start=155
  _globals['_RULE']._serialized_end=210
  _globals['_MESSAGERULES']._serialized_start=212
  _globals['_MESSAGERULES']._serialized_end=277
  _globals['_ONEOFRULES']._serialized_start=279
  _globals['_ONEOFRULES']._serialized_end=309
  _globals['_FIELDRULES']._serialized_start=312
  _globals['_FIELDRULES']._serialized_end=1399
  _globals['_PREDEFINEDRULES']._serialized_start=1401
  _globals['_PREDEFINEDRULES']._serialized_end=1484
  _globals['_FLOATRULES']._serialized_start=1487
  _globals['_FLOATRULES']._serialized_end=4521
  _globals['_DOUBLERULES']._serialized_start=4524
  _globals['_DOUBLERULES']._serialized_end=7577
  _globals['_INT32RULES']._serialized_start=7580
  _globals['_INT32RULES']._serialized_end=10280
  _globals['_INT64RULES']._serialized_start=10283
  _globals['_INT64RULES']._serialized_end=12983
  _globals['_UINT32RULES']._serialized_start=12986
  _globals['_UINT32RULES']._serialized_end=15704
  _globals['_UINT64RULES']._serialized_start=15707
  _globals['_UINT64RULES']._serialized_end=18425
  _globals['_SINT32RULES']._serialized_start=18428
  _globals['_SINT32RULES']._serialized_end=21146
  _globals['_SINT64RULES']._serialized_start=21149
  _globals['_SINT64RULES']._serialized_end=23867
  _globals['_FIXED32RULES']._serialized_start=23870
  _globals['_FIXED32RULES']._serialized_end=26605
  _globals['_FIXED64RULES']._serialized_start=26608
  _globals['_FIXED64RULES']._serialized_end=29343
  _globals['_SFIXED32RULES']._serialized_start=29346
  _globals['_SFIXED32RULES']._serialized_end=32098
  _globals['_SFIXED64RULES']._serialized_start=32101
  _globals['_SFIXED64RULES']._serialized_end=34853
  _globals['_BOOLRULES']._serialized_start=34856
  _globals['_BOOLRULES']._serialized_end=35055
  _globals['_STRINGRULES']._serialized_start=35058
  _globals['_STRINGRULES']._serialized_end=42114
  _globals['_BYTESRULES']._serialized_start=42117
  _globals['_BYTESRULES']._serialized_end=44271
  _globals['_ENUMRULES']._serialized_start=44274
  _globals['_ENUMRULES']._serialized_end=44742
  _globals['_REPEATEDRULES']._serialized_start=44745
  _globals['_REPEATEDRULES']._serialized_end=45252
  _globals['_MAPRULES']._serialized_start=45255
  _globals['_MAPRULES']._serialized_end=45649
  _globals['_ANYRULES']._serialized_start=45651
  _globals['_ANYRULES']._serialized_end=45689
  _globals['_DURATIONRULES']._serialized_start=45692
  _globals['_DURATIONRULES']._serialized_end=48661
  _globals['_TIMESTAMPRULES']._serialized_start=48664
  _globals['_TIMESTAMPRULES']._serialized_end=51754
  _globals['_VIOLATIONS']._serialized_start=51756
  _globals['_VIOLATIONS']._serialized_end=51813
  _globals['_VIOLATION']._serialized_start=51816
  _globals['_VIOLATION']._serialized_end=51975
  _globals['_FIELDPATH']._serialized_start=51977
  _globals['_FIELDPATH']._serialized_end=52038
  _globals['_FIELDPATHELEMENT']._serialized_start=52041
  _globals['_FIELDPATHELEMENT']._serialized_end=52402
# @@protoc_insertion_point(module_scope)
