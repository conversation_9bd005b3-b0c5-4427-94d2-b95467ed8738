# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: api-protos/v1/ai_config/ai_config_service.proto
# Protobuf Python Version: 4.25.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n/api-protos/v1/ai_config/ai_config_service.proto\x12\x19monkeyfx.api.v1.ai_config\"\x85\x04\n\x15\x41IProviderConfigProto\x12\x13\n\x0bprovider_id\x18\x01 \x01(\t\x12\x15\n\rprovider_type\x18\x02 \x01(\t\x12\x14\n\x0c\x64isplay_name\x18\x03 \x01(\t\x12\x12\n\nis_enabled\x18\x04 \x01(\x08\x12V\n\x0b\x63redentials\x18\x05 \x03(\x0b\x32\x41.monkeyfx.api.v1.ai_config.AIProviderConfigProto.CredentialsEntry\x12T\n\nattributes\x18\x06 \x03(\x0b\<EMAIL>.v1.ai_config.AIProviderConfigProto.AttributesEntry\x12P\n\x08metadata\x18\x07 \x03(\x0b\x32>.monkeyfx.api.v1.ai_config.AIProviderConfigProto.MetadataEntry\x1a\x32\n\x10\x43redentialsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\x1a\x31\n\x0f\x41ttributesEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\x1a/\n\rMetadataEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"b\n\x1dUpdateAIConfigurationsRequest\x12\x41\n\x07\x63onfigs\x18\x01 \x03(\x0b\x32\x30.monkeyfx.api.v1.ai_config.AIProviderConfigProto\"B\n\x1eUpdateAIConfigurationsResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\"I\n\x1aGetAIConfigurationsRequest\x12\x15\n\rprovider_type\x18\x01 \x01(\t\x12\x14\n\x0c\x65nabled_only\x18\x02 \x01(\x08\"\x82\x01\n\x1bGetAIConfigurationsResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12\x41\n\x07\x63onfigs\x18\x03 \x03(\x0b\x32\x30.monkeyfx.api.v1.ai_config.AIProviderConfigProto\"%\n\x0bPingRequest\x12\x16\n\x0e\x63lient_version\x18\x01 \x01(\t\"`\n\x0cPingResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12\x19\n\x11server_start_time\x18\x03 \x01(\x03\x12\x13\n\x0bhas_configs\x18\x04 \x01(\x08\x32\x88\x03\n\x16\x41IConfigurationService\x12\x8d\x01\n\x16UpdateAIConfigurations\x12\x38.monkeyfx.api.v1.ai_config.UpdateAIConfigurationsRequest\x1a\x39.monkeyfx.api.v1.ai_config.UpdateAIConfigurationsResponse\x12\x84\x01\n\x13GetAIConfigurations\x12\x35.monkeyfx.api.v1.ai_config.GetAIConfigurationsRequest\x1a\x36.monkeyfx.api.v1.ai_config.GetAIConfigurationsResponse\x12W\n\x04Ping\x12&.monkeyfx.api.v1.ai_config.PingRequest\x1a\'.monkeyfx.api.v1.ai_config.PingResponseB\x7f\n\"com.monkeyfx.grpc.api.v1.ai_configB\x14\x41IConfigServiceProtoP\x01ZAgithub.com/monkeyfx/electron-go-grpc-demo/gen/go/api/v1/ai_configb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'api_protos.v1.ai_config.ai_config_service_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  _globals['DESCRIPTOR']._options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n\"com.monkeyfx.grpc.api.v1.ai_configB\024AIConfigServiceProtoP\001ZAgithub.com/monkeyfx/electron-go-grpc-demo/gen/go/api/v1/ai_config'
  _globals['_AIPROVIDERCONFIGPROTO_CREDENTIALSENTRY']._options = None
  _globals['_AIPROVIDERCONFIGPROTO_CREDENTIALSENTRY']._serialized_options = b'8\001'
  _globals['_AIPROVIDERCONFIGPROTO_ATTRIBUTESENTRY']._options = None
  _globals['_AIPROVIDERCONFIGPROTO_ATTRIBUTESENTRY']._serialized_options = b'8\001'
  _globals['_AIPROVIDERCONFIGPROTO_METADATAENTRY']._options = None
  _globals['_AIPROVIDERCONFIGPROTO_METADATAENTRY']._serialized_options = b'8\001'
  _globals['_AIPROVIDERCONFIGPROTO']._serialized_start=79
  _globals['_AIPROVIDERCONFIGPROTO']._serialized_end=596
  _globals['_AIPROVIDERCONFIGPROTO_CREDENTIALSENTRY']._serialized_start=446
  _globals['_AIPROVIDERCONFIGPROTO_CREDENTIALSENTRY']._serialized_end=496
  _globals['_AIPROVIDERCONFIGPROTO_ATTRIBUTESENTRY']._serialized_start=498
  _globals['_AIPROVIDERCONFIGPROTO_ATTRIBUTESENTRY']._serialized_end=547
  _globals['_AIPROVIDERCONFIGPROTO_METADATAENTRY']._serialized_start=549
  _globals['_AIPROVIDERCONFIGPROTO_METADATAENTRY']._serialized_end=596
  _globals['_UPDATEAICONFIGURATIONSREQUEST']._serialized_start=598
  _globals['_UPDATEAICONFIGURATIONSREQUEST']._serialized_end=696
  _globals['_UPDATEAICONFIGURATIONSRESPONSE']._serialized_start=698
  _globals['_UPDATEAICONFIGURATIONSRESPONSE']._serialized_end=764
  _globals['_GETAICONFIGURATIONSREQUEST']._serialized_start=766
  _globals['_GETAICONFIGURATIONSREQUEST']._serialized_end=839
  _globals['_GETAICONFIGURATIONSRESPONSE']._serialized_start=842
  _globals['_GETAICONFIGURATIONSRESPONSE']._serialized_end=972
  _globals['_PINGREQUEST']._serialized_start=974
  _globals['_PINGREQUEST']._serialized_end=1011
  _globals['_PINGRESPONSE']._serialized_start=1013
  _globals['_PINGRESPONSE']._serialized_end=1109
  _globals['_AICONFIGURATIONSERVICE']._serialized_start=1112
  _globals['_AICONFIGURATIONSERVICE']._serialized_end=1504
# @@protoc_insertion_point(module_scope)
