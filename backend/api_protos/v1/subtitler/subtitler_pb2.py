# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: api-protos/v1/subtitler/subtitler.proto
# Protobuf Python Version: 5.29.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    5,
    29,
    0,
    '',
    'api-protos/v1/subtitler/subtitler.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import struct_pb2 as google_dot_protobuf_dot_struct__pb2
from google.protobuf import any_pb2 as google_dot_protobuf_dot_any__pb2
from api_protos.buf.validate import validate_pb2 as api__protos_dot_buf_dot_validate_dot_validate__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\'api-protos/v1/subtitler/subtitler.proto\x12\x19monkeyfx.api.v1.subtitler\x1a\x1cgoogle/protobuf/struct.proto\x1a\x19google/protobuf/any.proto\x1a&api-protos/buf/validate/validate.proto\"\xc8\x01\n\x0b\x45rrorDetail\x12\x12\n\nerror_code\x18\x01 \x01(\t\x12\x19\n\x11technical_message\x18\x02 \x01(\t\x12\x14\n\x0cuser_message\x18\x03 \x01(\t\x12\x44\n\x07\x63ontext\x18\x04 \x03(\x0b\x32\x33.monkeyfx.api.v1.subtitler.ErrorDetail.ContextEntry\x1a.\n\x0c\x43ontextEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"\xaa\x06\n\x0eProgressUpdate\x12.\n\x08trace_id\x18\x01 \x01(\tB\x1c\xbaH\x19r\x17\x32\x15^[a-zA-Z0-9_-]{8,64}$\x12\x1b\n\nstage_name\x18\x02 \x01(\tB\x07\xbaH\x04r\x02\x10\x01\x12\x1d\n\npercentage\x18\x03 \x01(\x05\x42\t\xbaH\x06\x1a\x04\x18\x64(\x00\x12\x0f\n\x07message\x18\x04 \x01(\t\x12:\n\x06status\x18\x05 \x01(\x0e\x32*.monkeyfx.api.v1.subtitler.OperationStatus\x12<\n\x0c\x65rror_detail\x18\x06 \x01(\x0b\x32&.monkeyfx.api.v1.subtitler.ErrorDetail\x12\x14\n\x08is_error\x18\x08 \x01(\x08\x42\x02\x18\x01\x12\x19\n\rerror_message\x18\t \x01(\tB\x02\x18\x01\x12R\n\x17video_to_audio_response\x18\n \x01(\x0b\x32/.monkeyfx.api.v1.subtitler.VideoToAudioResponseH\x00\x12P\n\x16\x61udio_to_text_response\x18\x0b \x01(\x0b\x32..monkeyfx.api.v1.subtitler.AudioToTextResponseH\x00\x12[\n\x1bgenerate_subtitles_response\x18\x0c \x01(\x0b\x32\x34.monkeyfx.api.v1.subtitler.GenerateSubtitlesResponseH\x00\x12]\n\x1ctranslate_subtitles_response\x18\r \x01(\x0b\x32\x35.monkeyfx.api.v1.subtitler.TranslateSubtitlesResponseH\x00\x12~\n.process_video_to_translated_subtitles_response\x18\x0e \x01(\x0b\x32\x44.monkeyfx.api.v1.subtitler.ProcessVideoToTranslatedSubtitlesResponseH\x00\x42\x0e\n\x0c\x66inal_result\"b\n\x13VideoToAudioRequest\x12\x1b\n\nvideo_path\x18\x01 \x01(\tB\x07\xbaH\x04r\x02\x10\x01\x12.\n\x08trace_id\x18\x02 \x01(\tB\x1c\xbaH\x19r\x17\x32\x15^[a-zA-Z0-9_-]{8,64}$\"w\n\x14VideoToAudioResponse\x12\x1b\n\naudio_path\x18\x01 \x01(\tB\x07\xbaH\x04r\x02\x10\x01\x12\x12\n\naudio_data\x18\x02 \x01(\x0c\x12.\n\x08trace_id\x18\x03 \x01(\tB\x1c\xbaH\x19r\x17\x32\x15^[a-zA-Z0-9_-]{8,64}$\"\xfa\x01\n\x12\x41udioToTextRequest\x12\x12\n\naudio_path\x18\x01 \x01(\t\x12\x12\n\naudio_data\x18\x02 \x01(\x0c\x12\x1f\n\x17request_word_timestamps\x18\x03 \x01(\x08\x12\x12\n\nskip_cache\x18\x04 \x01(\x08\x12.\n\x08trace_id\x18\x05 \x01(\tB\x1c\xbaH\x19r\x17\x32\x15^[a-zA-Z0-9_-]{8,64}$\x12&\n\x05model\x18\x06 \x01(\tB\x17\xbaH\x14r\x12R\x06\x42IJIANR\x08JIANYING\x12/\n\x08language\x18\x07 \x01(\tB\x1d\xbaH\x1ar\x18\x32\x16^[a-z]{2}(-[A-Z]{2})?$\"\xcc\x01\n\x16TimestampedTextSegment\x12\x0c\n\x04text\x18\x01 \x01(\t\x12\x15\n\rstart_time_ms\x18\x02 \x01(\x03\x12\x13\n\x0b\x65nd_time_ms\x18\x03 \x01(\x03\x12:\n\x06status\x18\x04 \x01(\x0e\x32*.monkeyfx.api.v1.subtitler.OperationStatus\x12<\n\x0c\x65rror_detail\x18\x05 \x01(\x0b\x32&.monkeyfx.api.v1.subtitler.ErrorDetail\"\x91\x02\n\x13\x41udioToTextResponse\x12\x43\n\x08segments\x18\x01 \x03(\x0b\x32\x31.monkeyfx.api.v1.subtitler.TimestampedTextSegment\x12.\n\x08trace_id\x18\x02 \x01(\tB\x1c\xbaH\x19r\x17\x32\x15^[a-zA-Z0-9_-]{8,64}$\x12)\n\x18total_segments_processed\x18\x03 \x01(\x05\x42\x07\xbaH\x04\x1a\x02(\x00\x12$\n\x13successful_segments\x18\x04 \x01(\x05\x42\x07\xbaH\x04\x1a\x02(\x00\x12 \n\x0f\x66\x61iled_segments\x18\x05 \x01(\x05\x42\x07\xbaH\x04\x1a\x02(\x00\x12\x12\n\ntranscript\x18\x06 \x01(\t\"\x92\x01\n\x18GenerateSubtitlesRequest\x12\x15\n\x04text\x18\x01 \x01(\tB\x07\xbaH\x04r\x02\x10\x01\x12\x1b\n\naudio_path\x18\x02 \x01(\tB\x07\xbaH\x04r\x02\x10\x01\x12\x12\n\nskip_cache\x18\x03 \x01(\x08\x12.\n\x08trace_id\x18\x04 \x01(\tB\x1c\xbaH\x19r\x17\x32\x15^[a-zA-Z0-9_-]{8,64}$\"u\n\x19GenerateSubtitlesResponse\x12\x13\n\x0bsrt_content\x18\x01 \x01(\t\x12\x13\n\x0b\x61ss_content\x18\x02 \x01(\t\x12.\n\x08trace_id\x18\x03 \x01(\tB\x1c\xbaH\x19r\x17\x32\x15^[a-zA-Z0-9_-]{8,64}$\"\xba\x01\n\x19TranslateSubtitlesRequest\x12!\n\x10subtitle_content\x18\x01 \x01(\tB\x07\xbaH\x04r\x02\x10\x01\x12\x36\n\x0ftarget_language\x18\x02 \x01(\tB\x1d\xbaH\x1ar\x18\x32\x16^[a-z]{2}(-[A-Z]{2})?$\x12\x12\n\nskip_cache\x18\x03 \x01(\x08\x12.\n\x08trace_id\x18\x04 \x01(\tB\x1c\xbaH\x19r\x17\x32\x15^[a-zA-Z0-9_-]{8,64}$\"\x98\x02\n\x11TranslatedSegment\x12\x1b\n\nsegment_id\x18\x01 \x01(\tB\x07\xbaH\x04r\x02\x10\x01\x12\x15\n\roriginal_text\x18\x02 \x01(\t\x12\x17\n\x0ftranslated_text\x18\x03 \x01(\t\x12:\n\x06status\x18\x04 \x01(\x0e\x32*.monkeyfx.api.v1.subtitler.OperationStatus\x12<\n\x0c\x65rror_detail\x18\x05 \x01(\x0b\x32&.monkeyfx.api.v1.subtitler.ErrorDetail\x12\x1e\n\rstart_time_ms\x18\x06 \x01(\x03\x42\x07\xbaH\x04\"\x02(\x00\x12\x1c\n\x0b\x65nd_time_ms\x18\x07 \x01(\x03\x42\x07\xbaH\x04\"\x02(\x00\"\xab\x02\n\x1aTranslateSubtitlesResponse\x12#\n\x1btranslated_subtitle_content\x18\x01 \x01(\t\x12.\n\x08trace_id\x18\x02 \x01(\tB\x1c\xbaH\x19r\x17\x32\x15^[a-zA-Z0-9_-]{8,64}$\x12\x45\n\x0fsegment_results\x18\x03 \x03(\x0b\x32,.monkeyfx.api.v1.subtitler.TranslatedSegment\x12)\n\x18total_segments_processed\x18\x04 \x01(\x05\x42\x07\xbaH\x04\x1a\x02(\x00\x12$\n\x13successful_segments\x18\x05 \x01(\x05\x42\x07\xbaH\x04\x1a\x02(\x00\x12 \n\x0f\x66\x61iled_segments\x18\x06 \x01(\x05\x42\x07\xbaH\x04\x1a\x02(\x00\"R\n\x0fTextToTranslate\x12\x1b\n\nsegment_id\x18\x01 \x01(\tB\x07\xbaH\x04r\x02\x10\x01\x12\"\n\x11text_to_translate\x18\x02 \x01(\tB\x07\xbaH\x04r\x02\x10\x01\"\x99\x02\n\x10TranslateRequest\x12\x18\n\x07task_id\x18\x01 \x01(\tB\x07\xbaH\x04r\x02\x10\x01\x12K\n\rtext_segments\x18\x02 \x03(\x0b\x32*.monkeyfx.api.v1.subtitler.TextToTranslateB\x08\xbaH\x05\x92\x01\x02\x08\x01\x12\x36\n\x0fsource_language\x18\x03 \x01(\tB\x1d\xbaH\x1ar\x18\x32\x16^[a-z]{2}(-[A-Z]{2})?$\x12\x36\n\x0ftarget_language\x18\x04 \x01(\tB\x1d\xbaH\x1ar\x18\x32\x16^[a-z]{2}(-[A-Z]{2})?$\x12.\n\x08trace_id\x18\x05 \x01(\tB\x1c\xbaH\x19r\x17\x32\x15^[a-zA-Z0-9_-]{8,64}$\"\xe6\x01\n\x11TranslateResponse\x12\x18\n\x07task_id\x18\x01 \x01(\tB\x07\xbaH\x04r\x02\x10\x01\x12I\n\x13translated_segments\x18\x02 \x03(\x0b\x32,.monkeyfx.api.v1.subtitler.TranslatedSegment\x12<\n\x0c\x65rror_detail\x18\x03 \x01(\x0b\x32&.monkeyfx.api.v1.subtitler.ErrorDetail\x12.\n\x08trace_id\x18\x04 \x01(\tB\x1c\xbaH\x19r\x17\x32\x15^[a-zA-Z0-9_-]{8,64}$\"\xaf\x01\n(ProcessVideoToTranslatedSubtitlesRequest\x12\x1b\n\nvideo_path\x18\x01 \x01(\tB\x07\xbaH\x04r\x02\x10\x01\x12\x36\n\x0ftarget_language\x18\x02 \x01(\tB\x1d\xbaH\x1ar\x18\x32\x16^[a-z]{2}(-[A-Z]{2})?$\x12.\n\x08trace_id\x18\x03 \x01(\tB\x1c\xbaH\x19r\x17\x32\x15^[a-zA-Z0-9_-]{8,64}$\"\x80\x01\n)ProcessVideoToTranslatedSubtitlesResponse\x12#\n\x1btranslated_subtitle_content\x18\x01 \x01(\t\x12.\n\x08trace_id\x18\x02 \x01(\tB\x1c\xbaH\x19r\x17\x32\x15^[a-zA-Z0-9_-]{8,64}$\"\xe4\x03\n\x13SaveSubtitleRequest\x12\x18\n\x10subtitle_content\x18\x01 \x01(\t\x12/\n\x06\x66ormat\x18\x02 \x01(\tB\x1f\xbaH\x1cr\x1aR\x03srtR\x03\x61ssR\x03txtR\x04jsonR\x03vtt\x12U\n\x06layout\x18\x03 \x01(\tBE\xbaHBr@R\x0c\xe5\x8e\x9f\xe6\x96\x87\xe5\x9c\xa8\xe4\xb8\x8aR\x0c\xe8\xaf\x91\xe6\x96\x87\xe5\x9c\xa8\xe4\xb8\x8aR\t\xe4\xbb\x85\xe5\x8e\x9f\xe6\x96\x87R\t\xe4\xbb\x85\xe8\xaf\x91\xe6\x96\x87R\x0c\xe5\x8f\x8c\xe8\xaf\xad\xe5\xb9\xb6\xe6\x8e\x92\x12\x1a\n\tfile_name\x18\x04 \x01(\tB\x07\xbaH\x04r\x02\x10\x01\x12\x18\n\x10original_content\x18\x05 \x01(\t\x12\x1a\n\x12translated_content\x18\x06 \x01(\t\x12<\n\x08segments\x18\x07 \x03(\x0b\x32*.monkeyfx.api.v1.subtitler.SubtitleSegment\x12\x1c\n\x14\x61uto_save_to_default\x18\x08 \x01(\x08\x12\x37\n\x11\x61ss_style_options\x18\t \x01(\x0b\x32\x17.google.protobuf.StructH\x00\x88\x01\x01\x12.\n\x08trace_id\x18\n \x01(\tB\x1c\xbaH\x19r\x17\x32\x15^[a-zA-Z0-9_-]{8,64}$B\x14\n\x12_ass_style_options\"\xb5\x03\n\x14SaveSubtitleResponse\x12\x11\n\tfile_path\x18\x01 \x01(\t\x12\x11\n\tfile_data\x18\x02 \x01(\x0c\x12\x1a\n\tfile_name\x18\x03 \x01(\tB\x07\xbaH\x04r\x02\x10\x01\x12\x1a\n\tfile_size\x18\x04 \x01(\x03\x42\x07\xbaH\x04\"\x02(\x00\x12\x18\n\x10saved_to_default\x18\x05 \x01(\x08\x12/\n\x06\x66ormat\x18\x06 \x01(\tB\x1f\xbaH\x1cr\x1aR\x03srtR\x03\x61ssR\x03txtR\x04jsonR\x03vtt\x12\x0e\n\x06layout\x18\x07 \x01(\t\x12\x16\n\x0e\x63ontent_source\x18\x08 \x01(\t\x12\"\n\x1aoriginal_filename_or_title\x18\t \x01(\t\x12.\n\x08trace_id\x18\n \x01(\tB\x1c\xbaH\x19r\x17\x32\x15^[a-zA-Z0-9_-]{8,64}$\x12:\n\x06status\x18\x0b \x01(\x0e\x32*.monkeyfx.api.v1.subtitler.OperationStatus\x12<\n\x0c\x65rror_detail\x18\x0c \x01(\x0b\x32&.monkeyfx.api.v1.subtitler.ErrorDetail\"\xf3\x01\n\x0fSubtitleSegment\x12\x1b\n\nstart_time\x18\x01 \x01(\x05\x42\x07\xbaH\x04\x1a\x02(\x00\x12\x19\n\x08\x65nd_time\x18\x02 \x01(\x05\x42\x07\xbaH\x04\x1a\x02(\x00\x12\x15\n\roriginal_text\x18\x03 \x01(\t\x12\x17\n\x0ftranslated_text\x18\x04 \x01(\t\x12:\n\x06status\x18\x05 \x01(\x0e\x32*.monkeyfx.api.v1.subtitler.OperationStatus\x12<\n\x0c\x65rror_detail\x18\x06 \x01(\x0b\x32&.monkeyfx.api.v1.subtitler.ErrorDetail\"\xd0\x03\n\x18\x42\x61tchSaveSubtitleRequest\x12\x37\n\x07\x66ormats\x18\x01 \x03(\tB&\xbaH#\x92\x01 \x08\x01\"\x1cr\x1aR\x03srtR\x03\x61ssR\x03txtR\x04jsonR\x03vtt\x12\x0f\n\x07layouts\x18\x02 \x03(\t\x12\x17\n\x0f\x63ontent_sources\x18\x03 \x03(\t\x12!\n\x10\x66ile_name_prefix\x18\x04 \x01(\tB\x07\xbaH\x04r\x02\x10\x01\x12\x18\n\x10original_content\x18\x05 \x01(\t\x12\x1a\n\x12translated_content\x18\x06 \x01(\t\x12<\n\x08segments\x18\x07 \x03(\x0b\x32*.monkeyfx.api.v1.subtitler.SubtitleSegment\x12\x1c\n\x14\x61uto_save_to_default\x18\x08 \x01(\x08\x12\x1d\n\x15translation_requested\x18\t \x01(\x08\x12\x37\n\x11\x61ss_style_options\x18\n \x01(\x0b\x32\x17.google.protobuf.StructH\x00\x88\x01\x01\x12.\n\x08trace_id\x18\x0b \x01(\tB\x1c\xbaH\x19r\x17\x32\x15^[a-zA-Z0-9_-]{8,64}$B\x14\n\x12_ass_style_options\"\x85\x02\n\x19\x42\x61tchSaveSubtitleResponse\x12>\n\x05\x66iles\x18\x01 \x03(\x0b\x32/.monkeyfx.api.v1.subtitler.SaveSubtitleResponse\x12.\n\x08trace_id\x18\x02 \x01(\tB\x1c\xbaH\x19r\x17\x32\x15^[a-zA-Z0-9_-]{8,64}$\x12:\n\x06status\x18\x03 \x01(\x0e\x32*.monkeyfx.api.v1.subtitler.OperationStatus\x12<\n\x0c\x65rror_detail\x18\x04 \x01(\x0b\x32&.monkeyfx.api.v1.subtitler.ErrorDetail\"\x89\x01\n\x11\x43learCacheRequest\x12\x44\n\ncache_type\x18\x01 \x01(\tB0\xbaH-r+R\x03\x61llR\rtranscriptionR\x0btranslationR\x08subtitle\x12.\n\x08trace_id\x18\x02 \x01(\tB\x1c\xbaH\x19r\x17\x32\x15^[a-zA-Z0-9_-]{8,64}$\"\xe0\x01\n\x12\x43learCacheResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12.\n\x08trace_id\x18\x03 \x01(\tB\x1c\xbaH\x19r\x17\x32\x15^[a-zA-Z0-9_-]{8,64}$\x12:\n\x06status\x18\x04 \x01(\x0e\x32*.monkeyfx.api.v1.subtitler.OperationStatus\x12<\n\x0c\x65rror_detail\x18\x05 \x01(\x0b\x32&.monkeyfx.api.v1.subtitler.ErrorDetail*\xb5\x01\n\x0fOperationStatus\x12 \n\x1cOPERATION_STATUS_UNSPECIFIED\x10\x00\x12\x1c\n\x18OPERATION_STATUS_SUCCESS\x10\x01\x12\x1a\n\x16OPERATION_STATUS_ERROR\x10\x02\x12$\n OPERATION_STATUS_PARTIAL_SUCCESS\x10\x03\x12 \n\x1cOPERATION_STATUS_IN_PROGRESS\x10\x04\x32\xaf\x08\n\tSubtitler\x12k\n\x0cVideoToAudio\x12..monkeyfx.api.v1.subtitler.VideoToAudioRequest\x1a).monkeyfx.api.v1.subtitler.ProgressUpdate0\x01\x12i\n\x0b\x41udioToText\x12-.monkeyfx.api.v1.subtitler.AudioToTextRequest\x1a).monkeyfx.api.v1.subtitler.ProgressUpdate0\x01\x12u\n\x11GenerateSubtitles\x12\x33.monkeyfx.api.v1.subtitler.GenerateSubtitlesRequest\x1a).monkeyfx.api.v1.subtitler.ProgressUpdate0\x01\x12w\n\x12TranslateSubtitles\x12\x34.monkeyfx.api.v1.subtitler.TranslateSubtitlesRequest\x1a).monkeyfx.api.v1.subtitler.ProgressUpdate0\x01\x12\x66\n\tTranslate\x12+.monkeyfx.api.v1.subtitler.TranslateRequest\x1a,.monkeyfx.api.v1.subtitler.TranslateResponse\x12\x95\x01\n!ProcessVideoToTranslatedSubtitles\x12\x43.monkeyfx.api.v1.subtitler.ProcessVideoToTranslatedSubtitlesRequest\x1a).monkeyfx.api.v1.subtitler.ProgressUpdate0\x01\x12o\n\x0cSaveSubtitle\x12..monkeyfx.api.v1.subtitler.SaveSubtitleRequest\x1a/.monkeyfx.api.v1.subtitler.SaveSubtitleResponse\x12~\n\x11\x42\x61tchSaveSubtitle\x12\x33.monkeyfx.api.v1.subtitler.BatchSaveSubtitleRequest\x1a\x34.monkeyfx.api.v1.subtitler.BatchSaveSubtitleResponse\x12i\n\nClearCache\x12,.monkeyfx.api.v1.subtitler.ClearCacheRequest\x1a-.monkeyfx.api.v1.subtitler.ClearCacheResponseBy\n\"com.monkeyfx.grpc.api.v1.subtitlerB\x0eSubtitlerProtoP\x01ZAgithub.com/monkeyfx/electron-go-grpc-demo/gen/go/api/v1/subtitlerb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'api_protos.v1.subtitler.subtitler_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n\"com.monkeyfx.grpc.api.v1.subtitlerB\016SubtitlerProtoP\001ZAgithub.com/monkeyfx/electron-go-grpc-demo/gen/go/api/v1/subtitler'
  _globals['_ERRORDETAIL_CONTEXTENTRY']._loaded_options = None
  _globals['_ERRORDETAIL_CONTEXTENTRY']._serialized_options = b'8\001'
  _globals['_PROGRESSUPDATE'].fields_by_name['trace_id']._loaded_options = None
  _globals['_PROGRESSUPDATE'].fields_by_name['trace_id']._serialized_options = b'\272H\031r\0272\025^[a-zA-Z0-9_-]{8,64}$'
  _globals['_PROGRESSUPDATE'].fields_by_name['stage_name']._loaded_options = None
  _globals['_PROGRESSUPDATE'].fields_by_name['stage_name']._serialized_options = b'\272H\004r\002\020\001'
  _globals['_PROGRESSUPDATE'].fields_by_name['percentage']._loaded_options = None
  _globals['_PROGRESSUPDATE'].fields_by_name['percentage']._serialized_options = b'\272H\006\032\004\030d(\000'
  _globals['_PROGRESSUPDATE'].fields_by_name['is_error']._loaded_options = None
  _globals['_PROGRESSUPDATE'].fields_by_name['is_error']._serialized_options = b'\030\001'
  _globals['_PROGRESSUPDATE'].fields_by_name['error_message']._loaded_options = None
  _globals['_PROGRESSUPDATE'].fields_by_name['error_message']._serialized_options = b'\030\001'
  _globals['_VIDEOTOAUDIOREQUEST'].fields_by_name['video_path']._loaded_options = None
  _globals['_VIDEOTOAUDIOREQUEST'].fields_by_name['video_path']._serialized_options = b'\272H\004r\002\020\001'
  _globals['_VIDEOTOAUDIOREQUEST'].fields_by_name['trace_id']._loaded_options = None
  _globals['_VIDEOTOAUDIOREQUEST'].fields_by_name['trace_id']._serialized_options = b'\272H\031r\0272\025^[a-zA-Z0-9_-]{8,64}$'
  _globals['_VIDEOTOAUDIORESPONSE'].fields_by_name['audio_path']._loaded_options = None
  _globals['_VIDEOTOAUDIORESPONSE'].fields_by_name['audio_path']._serialized_options = b'\272H\004r\002\020\001'
  _globals['_VIDEOTOAUDIORESPONSE'].fields_by_name['trace_id']._loaded_options = None
  _globals['_VIDEOTOAUDIORESPONSE'].fields_by_name['trace_id']._serialized_options = b'\272H\031r\0272\025^[a-zA-Z0-9_-]{8,64}$'
  _globals['_AUDIOTOTEXTREQUEST'].fields_by_name['trace_id']._loaded_options = None
  _globals['_AUDIOTOTEXTREQUEST'].fields_by_name['trace_id']._serialized_options = b'\272H\031r\0272\025^[a-zA-Z0-9_-]{8,64}$'
  _globals['_AUDIOTOTEXTREQUEST'].fields_by_name['model']._loaded_options = None
  _globals['_AUDIOTOTEXTREQUEST'].fields_by_name['model']._serialized_options = b'\272H\024r\022R\006BIJIANR\010JIANYING'
  _globals['_AUDIOTOTEXTREQUEST'].fields_by_name['language']._loaded_options = None
  _globals['_AUDIOTOTEXTREQUEST'].fields_by_name['language']._serialized_options = b'\272H\032r\0302\026^[a-z]{2}(-[A-Z]{2})?$'
  _globals['_AUDIOTOTEXTRESPONSE'].fields_by_name['trace_id']._loaded_options = None
  _globals['_AUDIOTOTEXTRESPONSE'].fields_by_name['trace_id']._serialized_options = b'\272H\031r\0272\025^[a-zA-Z0-9_-]{8,64}$'
  _globals['_AUDIOTOTEXTRESPONSE'].fields_by_name['total_segments_processed']._loaded_options = None
  _globals['_AUDIOTOTEXTRESPONSE'].fields_by_name['total_segments_processed']._serialized_options = b'\272H\004\032\002(\000'
  _globals['_AUDIOTOTEXTRESPONSE'].fields_by_name['successful_segments']._loaded_options = None
  _globals['_AUDIOTOTEXTRESPONSE'].fields_by_name['successful_segments']._serialized_options = b'\272H\004\032\002(\000'
  _globals['_AUDIOTOTEXTRESPONSE'].fields_by_name['failed_segments']._loaded_options = None
  _globals['_AUDIOTOTEXTRESPONSE'].fields_by_name['failed_segments']._serialized_options = b'\272H\004\032\002(\000'
  _globals['_GENERATESUBTITLESREQUEST'].fields_by_name['text']._loaded_options = None
  _globals['_GENERATESUBTITLESREQUEST'].fields_by_name['text']._serialized_options = b'\272H\004r\002\020\001'
  _globals['_GENERATESUBTITLESREQUEST'].fields_by_name['audio_path']._loaded_options = None
  _globals['_GENERATESUBTITLESREQUEST'].fields_by_name['audio_path']._serialized_options = b'\272H\004r\002\020\001'
  _globals['_GENERATESUBTITLESREQUEST'].fields_by_name['trace_id']._loaded_options = None
  _globals['_GENERATESUBTITLESREQUEST'].fields_by_name['trace_id']._serialized_options = b'\272H\031r\0272\025^[a-zA-Z0-9_-]{8,64}$'
  _globals['_GENERATESUBTITLESRESPONSE'].fields_by_name['trace_id']._loaded_options = None
  _globals['_GENERATESUBTITLESRESPONSE'].fields_by_name['trace_id']._serialized_options = b'\272H\031r\0272\025^[a-zA-Z0-9_-]{8,64}$'
  _globals['_TRANSLATESUBTITLESREQUEST'].fields_by_name['subtitle_content']._loaded_options = None
  _globals['_TRANSLATESUBTITLESREQUEST'].fields_by_name['subtitle_content']._serialized_options = b'\272H\004r\002\020\001'
  _globals['_TRANSLATESUBTITLESREQUEST'].fields_by_name['target_language']._loaded_options = None
  _globals['_TRANSLATESUBTITLESREQUEST'].fields_by_name['target_language']._serialized_options = b'\272H\032r\0302\026^[a-z]{2}(-[A-Z]{2})?$'
  _globals['_TRANSLATESUBTITLESREQUEST'].fields_by_name['trace_id']._loaded_options = None
  _globals['_TRANSLATESUBTITLESREQUEST'].fields_by_name['trace_id']._serialized_options = b'\272H\031r\0272\025^[a-zA-Z0-9_-]{8,64}$'
  _globals['_TRANSLATEDSEGMENT'].fields_by_name['segment_id']._loaded_options = None
  _globals['_TRANSLATEDSEGMENT'].fields_by_name['segment_id']._serialized_options = b'\272H\004r\002\020\001'
  _globals['_TRANSLATEDSEGMENT'].fields_by_name['start_time_ms']._loaded_options = None
  _globals['_TRANSLATEDSEGMENT'].fields_by_name['start_time_ms']._serialized_options = b'\272H\004\"\002(\000'
  _globals['_TRANSLATEDSEGMENT'].fields_by_name['end_time_ms']._loaded_options = None
  _globals['_TRANSLATEDSEGMENT'].fields_by_name['end_time_ms']._serialized_options = b'\272H\004\"\002(\000'
  _globals['_TRANSLATESUBTITLESRESPONSE'].fields_by_name['trace_id']._loaded_options = None
  _globals['_TRANSLATESUBTITLESRESPONSE'].fields_by_name['trace_id']._serialized_options = b'\272H\031r\0272\025^[a-zA-Z0-9_-]{8,64}$'
  _globals['_TRANSLATESUBTITLESRESPONSE'].fields_by_name['total_segments_processed']._loaded_options = None
  _globals['_TRANSLATESUBTITLESRESPONSE'].fields_by_name['total_segments_processed']._serialized_options = b'\272H\004\032\002(\000'
  _globals['_TRANSLATESUBTITLESRESPONSE'].fields_by_name['successful_segments']._loaded_options = None
  _globals['_TRANSLATESUBTITLESRESPONSE'].fields_by_name['successful_segments']._serialized_options = b'\272H\004\032\002(\000'
  _globals['_TRANSLATESUBTITLESRESPONSE'].fields_by_name['failed_segments']._loaded_options = None
  _globals['_TRANSLATESUBTITLESRESPONSE'].fields_by_name['failed_segments']._serialized_options = b'\272H\004\032\002(\000'
  _globals['_TEXTTOTRANSLATE'].fields_by_name['segment_id']._loaded_options = None
  _globals['_TEXTTOTRANSLATE'].fields_by_name['segment_id']._serialized_options = b'\272H\004r\002\020\001'
  _globals['_TEXTTOTRANSLATE'].fields_by_name['text_to_translate']._loaded_options = None
  _globals['_TEXTTOTRANSLATE'].fields_by_name['text_to_translate']._serialized_options = b'\272H\004r\002\020\001'
  _globals['_TRANSLATEREQUEST'].fields_by_name['task_id']._loaded_options = None
  _globals['_TRANSLATEREQUEST'].fields_by_name['task_id']._serialized_options = b'\272H\004r\002\020\001'
  _globals['_TRANSLATEREQUEST'].fields_by_name['text_segments']._loaded_options = None
  _globals['_TRANSLATEREQUEST'].fields_by_name['text_segments']._serialized_options = b'\272H\005\222\001\002\010\001'
  _globals['_TRANSLATEREQUEST'].fields_by_name['source_language']._loaded_options = None
  _globals['_TRANSLATEREQUEST'].fields_by_name['source_language']._serialized_options = b'\272H\032r\0302\026^[a-z]{2}(-[A-Z]{2})?$'
  _globals['_TRANSLATEREQUEST'].fields_by_name['target_language']._loaded_options = None
  _globals['_TRANSLATEREQUEST'].fields_by_name['target_language']._serialized_options = b'\272H\032r\0302\026^[a-z]{2}(-[A-Z]{2})?$'
  _globals['_TRANSLATEREQUEST'].fields_by_name['trace_id']._loaded_options = None
  _globals['_TRANSLATEREQUEST'].fields_by_name['trace_id']._serialized_options = b'\272H\031r\0272\025^[a-zA-Z0-9_-]{8,64}$'
  _globals['_TRANSLATERESPONSE'].fields_by_name['task_id']._loaded_options = None
  _globals['_TRANSLATERESPONSE'].fields_by_name['task_id']._serialized_options = b'\272H\004r\002\020\001'
  _globals['_TRANSLATERESPONSE'].fields_by_name['trace_id']._loaded_options = None
  _globals['_TRANSLATERESPONSE'].fields_by_name['trace_id']._serialized_options = b'\272H\031r\0272\025^[a-zA-Z0-9_-]{8,64}$'
  _globals['_PROCESSVIDEOTOTRANSLATEDSUBTITLESREQUEST'].fields_by_name['video_path']._loaded_options = None
  _globals['_PROCESSVIDEOTOTRANSLATEDSUBTITLESREQUEST'].fields_by_name['video_path']._serialized_options = b'\272H\004r\002\020\001'
  _globals['_PROCESSVIDEOTOTRANSLATEDSUBTITLESREQUEST'].fields_by_name['target_language']._loaded_options = None
  _globals['_PROCESSVIDEOTOTRANSLATEDSUBTITLESREQUEST'].fields_by_name['target_language']._serialized_options = b'\272H\032r\0302\026^[a-z]{2}(-[A-Z]{2})?$'
  _globals['_PROCESSVIDEOTOTRANSLATEDSUBTITLESREQUEST'].fields_by_name['trace_id']._loaded_options = None
  _globals['_PROCESSVIDEOTOTRANSLATEDSUBTITLESREQUEST'].fields_by_name['trace_id']._serialized_options = b'\272H\031r\0272\025^[a-zA-Z0-9_-]{8,64}$'
  _globals['_PROCESSVIDEOTOTRANSLATEDSUBTITLESRESPONSE'].fields_by_name['trace_id']._loaded_options = None
  _globals['_PROCESSVIDEOTOTRANSLATEDSUBTITLESRESPONSE'].fields_by_name['trace_id']._serialized_options = b'\272H\031r\0272\025^[a-zA-Z0-9_-]{8,64}$'
  _globals['_SAVESUBTITLEREQUEST'].fields_by_name['format']._loaded_options = None
  _globals['_SAVESUBTITLEREQUEST'].fields_by_name['format']._serialized_options = b'\272H\034r\032R\003srtR\003assR\003txtR\004jsonR\003vtt'
  _globals['_SAVESUBTITLEREQUEST'].fields_by_name['layout']._loaded_options = None
  _globals['_SAVESUBTITLEREQUEST'].fields_by_name['layout']._serialized_options = b'\272HBr@R\014\345\216\237\346\226\207\345\234\250\344\270\212R\014\350\257\221\346\226\207\345\234\250\344\270\212R\t\344\273\205\345\216\237\346\226\207R\t\344\273\205\350\257\221\346\226\207R\014\345\217\214\350\257\255\345\271\266\346\216\222'
  _globals['_SAVESUBTITLEREQUEST'].fields_by_name['file_name']._loaded_options = None
  _globals['_SAVESUBTITLEREQUEST'].fields_by_name['file_name']._serialized_options = b'\272H\004r\002\020\001'
  _globals['_SAVESUBTITLEREQUEST'].fields_by_name['trace_id']._loaded_options = None
  _globals['_SAVESUBTITLEREQUEST'].fields_by_name['trace_id']._serialized_options = b'\272H\031r\0272\025^[a-zA-Z0-9_-]{8,64}$'
  _globals['_SAVESUBTITLERESPONSE'].fields_by_name['file_name']._loaded_options = None
  _globals['_SAVESUBTITLERESPONSE'].fields_by_name['file_name']._serialized_options = b'\272H\004r\002\020\001'
  _globals['_SAVESUBTITLERESPONSE'].fields_by_name['file_size']._loaded_options = None
  _globals['_SAVESUBTITLERESPONSE'].fields_by_name['file_size']._serialized_options = b'\272H\004\"\002(\000'
  _globals['_SAVESUBTITLERESPONSE'].fields_by_name['format']._loaded_options = None
  _globals['_SAVESUBTITLERESPONSE'].fields_by_name['format']._serialized_options = b'\272H\034r\032R\003srtR\003assR\003txtR\004jsonR\003vtt'
  _globals['_SAVESUBTITLERESPONSE'].fields_by_name['trace_id']._loaded_options = None
  _globals['_SAVESUBTITLERESPONSE'].fields_by_name['trace_id']._serialized_options = b'\272H\031r\0272\025^[a-zA-Z0-9_-]{8,64}$'
  _globals['_SUBTITLESEGMENT'].fields_by_name['start_time']._loaded_options = None
  _globals['_SUBTITLESEGMENT'].fields_by_name['start_time']._serialized_options = b'\272H\004\032\002(\000'
  _globals['_SUBTITLESEGMENT'].fields_by_name['end_time']._loaded_options = None
  _globals['_SUBTITLESEGMENT'].fields_by_name['end_time']._serialized_options = b'\272H\004\032\002(\000'
  _globals['_BATCHSAVESUBTITLEREQUEST'].fields_by_name['formats']._loaded_options = None
  _globals['_BATCHSAVESUBTITLEREQUEST'].fields_by_name['formats']._serialized_options = b'\272H#\222\001 \010\001\"\034r\032R\003srtR\003assR\003txtR\004jsonR\003vtt'
  _globals['_BATCHSAVESUBTITLEREQUEST'].fields_by_name['file_name_prefix']._loaded_options = None
  _globals['_BATCHSAVESUBTITLEREQUEST'].fields_by_name['file_name_prefix']._serialized_options = b'\272H\004r\002\020\001'
  _globals['_BATCHSAVESUBTITLEREQUEST'].fields_by_name['trace_id']._loaded_options = None
  _globals['_BATCHSAVESUBTITLEREQUEST'].fields_by_name['trace_id']._serialized_options = b'\272H\031r\0272\025^[a-zA-Z0-9_-]{8,64}$'
  _globals['_BATCHSAVESUBTITLERESPONSE'].fields_by_name['trace_id']._loaded_options = None
  _globals['_BATCHSAVESUBTITLERESPONSE'].fields_by_name['trace_id']._serialized_options = b'\272H\031r\0272\025^[a-zA-Z0-9_-]{8,64}$'
  _globals['_CLEARCACHEREQUEST'].fields_by_name['cache_type']._loaded_options = None
  _globals['_CLEARCACHEREQUEST'].fields_by_name['cache_type']._serialized_options = b'\272H-r+R\003allR\rtranscriptionR\013translationR\010subtitle'
  _globals['_CLEARCACHEREQUEST'].fields_by_name['trace_id']._loaded_options = None
  _globals['_CLEARCACHEREQUEST'].fields_by_name['trace_id']._serialized_options = b'\272H\031r\0272\025^[a-zA-Z0-9_-]{8,64}$'
  _globals['_CLEARCACHERESPONSE'].fields_by_name['trace_id']._loaded_options = None
  _globals['_CLEARCACHERESPONSE'].fields_by_name['trace_id']._serialized_options = b'\272H\031r\0272\025^[a-zA-Z0-9_-]{8,64}$'
  _globals['_OPERATIONSTATUS']._serialized_start=6364
  _globals['_OPERATIONSTATUS']._serialized_end=6545
  _globals['_ERRORDETAIL']._serialized_start=168
  _globals['_ERRORDETAIL']._serialized_end=368
  _globals['_ERRORDETAIL_CONTEXTENTRY']._serialized_start=322
  _globals['_ERRORDETAIL_CONTEXTENTRY']._serialized_end=368
  _globals['_PROGRESSUPDATE']._serialized_start=371
  _globals['_PROGRESSUPDATE']._serialized_end=1181
  _globals['_VIDEOTOAUDIOREQUEST']._serialized_start=1183
  _globals['_VIDEOTOAUDIOREQUEST']._serialized_end=1281
  _globals['_VIDEOTOAUDIORESPONSE']._serialized_start=1283
  _globals['_VIDEOTOAUDIORESPONSE']._serialized_end=1402
  _globals['_AUDIOTOTEXTREQUEST']._serialized_start=1405
  _globals['_AUDIOTOTEXTREQUEST']._serialized_end=1655
  _globals['_TIMESTAMPEDTEXTSEGMENT']._serialized_start=1658
  _globals['_TIMESTAMPEDTEXTSEGMENT']._serialized_end=1862
  _globals['_AUDIOTOTEXTRESPONSE']._serialized_start=1865
  _globals['_AUDIOTOTEXTRESPONSE']._serialized_end=2138
  _globals['_GENERATESUBTITLESREQUEST']._serialized_start=2141
  _globals['_GENERATESUBTITLESREQUEST']._serialized_end=2287
  _globals['_GENERATESUBTITLESRESPONSE']._serialized_start=2289
  _globals['_GENERATESUBTITLESRESPONSE']._serialized_end=2406
  _globals['_TRANSLATESUBTITLESREQUEST']._serialized_start=2409
  _globals['_TRANSLATESUBTITLESREQUEST']._serialized_end=2595
  _globals['_TRANSLATEDSEGMENT']._serialized_start=2598
  _globals['_TRANSLATEDSEGMENT']._serialized_end=2878
  _globals['_TRANSLATESUBTITLESRESPONSE']._serialized_start=2881
  _globals['_TRANSLATESUBTITLESRESPONSE']._serialized_end=3180
  _globals['_TEXTTOTRANSLATE']._serialized_start=3182
  _globals['_TEXTTOTRANSLATE']._serialized_end=3264
  _globals['_TRANSLATEREQUEST']._serialized_start=3267
  _globals['_TRANSLATEREQUEST']._serialized_end=3548
  _globals['_TRANSLATERESPONSE']._serialized_start=3551
  _globals['_TRANSLATERESPONSE']._serialized_end=3781
  _globals['_PROCESSVIDEOTOTRANSLATEDSUBTITLESREQUEST']._serialized_start=3784
  _globals['_PROCESSVIDEOTOTRANSLATEDSUBTITLESREQUEST']._serialized_end=3959
  _globals['_PROCESSVIDEOTOTRANSLATEDSUBTITLESRESPONSE']._serialized_start=3962
  _globals['_PROCESSVIDEOTOTRANSLATEDSUBTITLESRESPONSE']._serialized_end=4090
  _globals['_SAVESUBTITLEREQUEST']._serialized_start=4093
  _globals['_SAVESUBTITLEREQUEST']._serialized_end=4577
  _globals['_SAVESUBTITLERESPONSE']._serialized_start=4580
  _globals['_SAVESUBTITLERESPONSE']._serialized_end=5017
  _globals['_SUBTITLESEGMENT']._serialized_start=5020
  _globals['_SUBTITLESEGMENT']._serialized_end=5263
  _globals['_BATCHSAVESUBTITLEREQUEST']._serialized_start=5266
  _globals['_BATCHSAVESUBTITLEREQUEST']._serialized_end=5730
  _globals['_BATCHSAVESUBTITLERESPONSE']._serialized_start=5733
  _globals['_BATCHSAVESUBTITLERESPONSE']._serialized_end=5994
  _globals['_CLEARCACHEREQUEST']._serialized_start=5997
  _globals['_CLEARCACHEREQUEST']._serialized_end=6134
  _globals['_CLEARCACHERESPONSE']._serialized_start=6137
  _globals['_CLEARCACHERESPONSE']._serialized_end=6361
  _globals['_SUBTITLER']._serialized_start=6548
  _globals['_SUBTITLER']._serialized_end=7619
# @@protoc_insertion_point(module_scope)
