# =============================================================================
# 后端gRPC服务生产环境依赖
# 版本锁定确保生产环境一致性和安全性
# =============================================================================

# gRPC核心框架和protobuf (版本锁定确保兼容性)
protobuf==4.25.8
grpcio==1.60.0
grpcio-tools==1.60.0
grpcio-health-checking==1.60.0
grpcio-reflection==1.60.0
grpcio-status==1.60.0

# AI服务集成
openai>=1.0.0,<2.0.0

# 网络请求和HTTP客户端
urllib3>=2.0.0,<3.0.0
requests>=2.31.0,<3.0.0

# 数据库ORM
sqlalchemy>=2.0.0,<3.0.0

# 配置管理和验证
pydantic>=2.0.0,<3.0.0
python-dotenv>=1.0.0,<2.0.0
protovalidate>=0.9.0,<1.0.0  # protovalidate v2.0 for field validation

# 基础测试框架 (生产环境健康检查)
pytest>=7.0.0,<8.0.0
structlog>=23.1.0
