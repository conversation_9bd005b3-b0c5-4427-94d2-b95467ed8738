# =============================================================================
# 后端gRPC服务生产环境依赖
# 版本锁定确保生产环境一致性和安全性
# =============================================================================

# gRPC核心框架
grpcio>=1.60.0,<2.0.0
grpcio-tools>=1.60.0,<2.0.0

# AI服务集成
openai>=1.0.0,<2.0.0

# 网络请求和HTTP客户端
urllib3>=2.0.0,<3.0.0
requests>=2.31.0,<3.0.0

# 数据库ORM
sqlalchemy>=2.0.0,<3.0.0

# 配置管理和验证
pydantic>=2.0.0,<3.0.0
python-dotenv>=1.0.0,<2.0.0

# 基础测试框架 (生产环境健康检查)
pytest>=7.0.0,<8.0.0
structlog>=23.1.0
