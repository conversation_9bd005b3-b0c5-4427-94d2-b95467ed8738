# -*- coding: utf-8 -*-
"""
字幕服务实现模块。

该模块定义了 SubtitleServiceImpl 类，它实现了 gRPC 服务 SubtitlerServicer 中定义的所有 RPC 方法。
这些方法处理与字幕生成相关的各种任务，包括：
- 从视频中提取音频 (VideoToAudio)
- 将音频转换为文本 (AudioToText)
- 根据文本生成字幕 (GenerateSubtitles)
- 翻译字幕内容 (TranslateSubtitles)
- 处理从视频到翻译字幕的完整流程 (ProcessVideoToTranslatedSubtitles)
- 保存单个字幕文件 (SaveSubtitle)
- 批量保存字幕文件 (BatchSaveSubtitle)

服务依赖于 TranscriptThread 来执行核心的字幕处理逻辑，并通过 ProgressUpdate 消息流式返回处理进度和结果。
"""
import traceback # 用于捕获和格式化异常信息
import grpc
from pathlib import Path # 用于路径操作
import uuid
import json
from google.protobuf.struct_pb2 import Struct # For data field
import time

# 导入新的统一日志系统
from utils.universal_logging import get_logger, ensure_trace_id, log_operation, LogContext, set_trace_id

from api_protos.v1.subtitler import subtitler_pb2
from api_protos.v1.subtitler import subtitler_pb2_grpc
from subtitle.workflow.transcriptThread import TranscriptThread
from subtitle_processor import SubtitleProcessor

# DTO Imports
from data_models.dtos.subtitler_dtos import AudioToTextRequest as AudioToTextRequestDto
from data_models.dtos.subtitler_dtos import VideoToAudioRequest as VideoToAudioRequestDto # Added
from data_models.dtos.subtitler_dtos import ProgressUpdate as ProgressUpdateDto
from data_models.dtos.subtitler_dtos import AudioToTextResponse as AudioToTextResponseDto
# VideoToAudioResponseDto is already imported via the ( ... ) import on line 40
from data_models.dtos.subtitler_dtos import TimestampedTextSegment as TimestampedTextSegmentDto
from data_models.dtos.subtitler_dtos import SaveSubtitleRequest as SaveSubtitleRequestDto
from data_models.dtos.subtitler_dtos import SaveSubtitleResponse as SaveSubtitleResponseDto
# Other response DTOs for ProgressUpdateDto that might be used by other methods
from data_models.dtos.subtitler_dtos import (
    VideoToAudioResponse as VideoToAudioResponseDto,
    GenerateSubtitlesResponse as GenerateSubtitlesResponseDto,
    TranslateSubtitlesResponse as TranslateSubtitlesResponseDto, # DTO for the streaming TranslateSubtitles RPC
    ProcessVideoToTranslatedSubtitlesResponse as ProcessVideoToTranslatedSubtitlesResponseDto,
    # TranslatedSegmentResultDto is now TranslatedSegmentDto
    # DTOs for the new unary Translate RPC
    TextToTranslateDto, # New DTO
    TranslateRequestDto, # New DTO (was previously an alias)
    TranslateResponseDto, # New DTO (was previously an alias)
    TranslatedSegmentDto, # Updated DTO (was TranslatedSegmentResultDto, and also an alias for TranslateResponse)
    SubtitleSegment as SubtitleSegmentDto, # Still used by SaveSubtitle etc.
    TimestampedTextSegment as TimestampedTextSegmentDto # Still used by AudioToText etc.
)
from data_models.dtos.dto_common import ErrorDetailDto, OperationStatusEnum

# 导入新的错误处理系统
from subtitle.exceptions import (
    SubtitleProcessingError,
    FileProcessingError,
    APIError,
    ConfigurationError,
    create_file_error,
    create_api_error
)
from subtitle.error_handler import (
    handle_grpc_errors,
    handle_subtitle_errors,
    create_progress_error,
    error_reporter
)

# 导入标准化常量和工厂
from subtitle.constants import (
    OperationStatus as OperationStatusStrings, ErrorCode, StageNames, LogMessages, # Renamed OperationStatus to avoid conflict with Enum
    DefaultValues, ResponseFieldNames, ProgressKeys
)
from subtitle.utils.progress_factory import create_error_progress

# 导入新的转换工具函数
from utils.conversion_utils import convert_proto_to_dto, convert_dto_to_proto, ProtoToDtoConversionError, DtoToProtoConversionError

# 导入trace_id管理器
from utils.trace_id_manager import ensure_trace_id, propagate_trace_id


# 获取统一日志系统的 logger 实例
logger = get_logger("subtitle_service")

# _convert_progress_update_dto_to_pb 函数将被移除，因为它的功能由 convert_dto_to_proto 替代

class SubtitleServiceImpl(subtitler_pb2_grpc.SubtitlerServicer):
    """
    字幕服务 gRPC 实现类。

    实现了 SubtitlerServicer 接口中定义的所有方法，用于处理字幕相关的请求。
    """
    def __init__(self):
        """
        初始化 SubtitleServiceImpl。
        创建一个 SubtitleProcessor 实例用于处理字幕保存等逻辑。
        """
        self.processor = SubtitleProcessor(logger_instance=logger) # 使用全局 logger

    def _handle_streaming_request(self, request_handler_func, error_stage_name: str, trace_id: str = None):
        """
        通用的流式请求处理逻辑。

        Args:
            request_handler_func (Callable): 一个生成器函数，用于处理具体的请求逻辑并 yield 进度字典。
            error_stage_name (str): 当服务层发生意外错误时，用于 ProgressUpdate 的阶段名称。
            trace_id (str, optional): 请求的跟踪ID，用于关联请求和响应。

        Yields:
            subtitler_pb2.ProgressUpdate: 进度更新消息。
        """
        # 确保trace_id有效
        effective_trace_id = ensure_trace_id(trace_id, f"_handle_streaming_request-{error_stage_name}")
        try:
            for progress_dto_or_dict in request_handler_func():
                # 检查是否为None，跳过None值
                if progress_dto_or_dict is None:
                    logger.warning(f"跳过None进度更新")
                    continue
                
                # 调试信息：记录收到的数据类型和内容
                logger.info(f"🔍 [_handle_streaming_request] 收到progress_dto_or_dict: type={type(progress_dto_or_dict)}, value={progress_dto_or_dict}")
                    
                if isinstance(progress_dto_or_dict, ProgressUpdateDto):
                    progress_dto = progress_dto_or_dict
                elif isinstance(progress_dto_or_dict, dict) and progress_dto_or_dict is not None: # Handle legacy dict returns from other methods for now
                    # Basic conversion from dict to DTO for non-AudioToText methods
                    error_detail_dto = None
                    if progress_dto_or_dict.get(ProgressKeys.ERROR_DETAIL):
                        ed = progress_dto_or_dict[ProgressKeys.ERROR_DETAIL]
                        error_detail_dto = ErrorDetailDto(
                            error_code=ed.get("error_code", ErrorCode.UNEXPECTED_ERROR.value),
                            technical_message=ed.get("technical_message", "Unknown technical message"),
                            user_message=ed.get("user_message", "An unknown error occurred."),
                            context=ed.get("context", {})
                        )
                    
                    status_val = (progress_dto_or_dict and progress_dto_or_dict.get(ProgressKeys.STATUS)) or OperationStatusStrings.IN_PROGRESS.value
                    try:
                        status_enum = OperationStatusEnum[status_val.upper()]
                    except KeyError:
                        status_enum = OperationStatusEnum.OPERATION_STATUS_UNSPECIFIED

                    # --- BEGIN DTO conversion for final_result fields ---
                    # 确保当前trace_id有效，优先使用progress中的trace_id，然后是外部传入的，最后生成新的
                    current_trace_id = propagate_trace_id(
                        progress_dto_or_dict and progress_dto_or_dict.get(ProgressKeys.TRACE_ID),
                        effective_trace_id,
                        "DTO_conversion_for_final_result"
                    )
                    
                    video_to_audio_response_dto = None
                    
                    # 添加详细调试信息
                    final_result = (progress_dto_or_dict and progress_dto_or_dict.get(ProgressKeys.FINAL_RESULT)) or {}
                    logger.debug("progress_dto_or_dict keys", progress_dto_or_dict_keys=list(progress_dto_or_dict.keys()) if progress_dto_or_dict else [], module="service")
                    logger.debug(f"🔍 [_handle_streaming_request] final_result: {final_result}", flush=True, module="service")
                    logger.debug(f"🔍 [_handle_streaming_request] ProgressKeys.FINAL_RESULT = '{ProgressKeys.FINAL_RESULT}'", flush=True, module="service")
                    logger.debug(f"🔍 [_handle_streaming_request] ResponseFieldNames.VIDEO_TO_AUDIO_RESPONSE = '{ResponseFieldNames.VIDEO_TO_AUDIO_RESPONSE}'", flush=True, module="service")

                    # 添加详细的数据流调试
                    logger.info(f"🔍 [_handle_streaming_request] 检查progress_dto_or_dict结构:", flush=True, module="service")
                    logger.info(f"   - progress_dto_or_dict存在: {progress_dto_or_dict is not None}", flush=True, module="service")
                    if progress_dto_or_dict:
                        logger.info(f"   - progress_dto_or_dict类型: {type(progress_dto_or_dict)}", flush=True, module="service")
                        logger.info(f"   - progress_dto_or_dict键: {list(progress_dto_or_dict.keys()) if hasattr(progress_dto_or_dict, 'keys') else 'N/A'}", flush=True, module="service")
                        final_result = progress_dto_or_dict.get(ProgressKeys.FINAL_RESULT, {})
                        logger.info(f"   - final_result存在: {final_result is not None}", flush=True, module="service")
                        logger.info(f"   - final_result类型: {type(final_result)}", flush=True, module="service")
                        if final_result:
                            logger.info(f"   - final_result键: {list(final_result.keys()) if hasattr(final_result, 'keys') else 'N/A'}", flush=True, module="service")
                            vtar_exists = final_result.get(ResponseFieldNames.VIDEO_TO_AUDIO_RESPONSE)
                            logger.info(f"   - video_to_audio_response存在: {vtar_exists is not None}", flush=True, module="service")
                            if vtar_exists:
                                logger.info(f"   - video_to_audio_response内容: {vtar_exists}", flush=True, module="service")

                    if progress_dto_or_dict and progress_dto_or_dict.get(ProgressKeys.FINAL_RESULT, {}).get(ResponseFieldNames.VIDEO_TO_AUDIO_RESPONSE):
                        vtar_dict = progress_dto_or_dict[ProgressKeys.FINAL_RESULT][ResponseFieldNames.VIDEO_TO_AUDIO_RESPONSE]
                        logger.debug(f"🔍 [_handle_streaming_request] 找到 video_to_audio_response: {vtar_dict}", flush=True, module="service")
                        video_to_audio_response_dto = VideoToAudioResponseDto(
                            audio_path=vtar_dict.get("audio_path", ""),
                            audio_data=vtar_dict.get("audio_data", b""),
                            trace_id=vtar_dict.get("trace_id", current_trace_id)
                        )
                        logger.debug(f"🔍 [_handle_streaming_request] 创建的 video_to_audio_response_dto: {video_to_audio_response_dto}", flush=True, module="service")
                    else:
                        logger.debug(f"🔍 [_handle_streaming_request] 未找到 video_to_audio_response", flush=True, module="service")
                    
                    audio_to_text_response_dto = None
                    if progress_dto_or_dict and progress_dto_or_dict.get(ProgressKeys.FINAL_RESULT, {}).get(ResponseFieldNames.AUDIO_TO_TEXT_RESPONSE):
                        attr_dict = progress_dto_or_dict[ProgressKeys.FINAL_RESULT][ResponseFieldNames.AUDIO_TO_TEXT_RESPONSE]
                        # Convert segments if present
                        segments_dto_list = []
                        if "segments" in attr_dict:
                            for seg_dict in attr_dict["segments"]:
                                segment_dto = TimestampedTextSegmentDto(
                                    text=seg_dict.get("text", ""),
                                    start_time_ms=seg_dict.get("start_time", 0),
                                    end_time_ms=seg_dict.get("end_time", 0)
                                )
                                segments_dto_list.append(segment_dto)
                        
                        audio_to_text_response_dto = AudioToTextResponseDto(
                            segments=segments_dto_list,
                            trace_id=attr_dict.get("trace_id", current_trace_id),
                            total_segments_processed=attr_dict.get("total_segments_processed", len(segments_dto_list)),
                            successful_segments=attr_dict.get("successful_segments", len(segments_dto_list)),
                            failed_segments=attr_dict.get("failed_segments", 0),
                            transcript=attr_dict.get("transcript", "")  # 添加完整的转录文本
                        )
                    
                    generate_subtitles_response_dto = None
                    if progress_dto_or_dict and progress_dto_or_dict.get(ProgressKeys.FINAL_RESULT, {}).get(ResponseFieldNames.GENERATE_SUBTITLES_RESPONSE):
                        gsr_dict = progress_dto_or_dict[ProgressKeys.FINAL_RESULT][ResponseFieldNames.GENERATE_SUBTITLES_RESPONSE]
                        generate_subtitles_response_dto = GenerateSubtitlesResponseDto(
                            srt_content=gsr_dict.get("srt_content", ""),
                            ass_content=gsr_dict.get("ass_content", ""),
                            trace_id=gsr_dict.get("trace_id", current_trace_id)
                        )
                    
                    translate_subtitles_response_dto = None
                    # 增强翻译响应提取逻辑：从多个位置查找翻译结果
                    tsr_dict = None
                    
                    # 1. 从 final_result 中查找
                    if progress_dto_or_dict and progress_dto_or_dict.get(ProgressKeys.FINAL_RESULT, {}).get(ResponseFieldNames.TRANSLATE_SUBTITLES_RESPONSE):
                        tsr_dict = progress_dto_or_dict[ProgressKeys.FINAL_RESULT][ResponseFieldNames.TRANSLATE_SUBTITLES_RESPONSE]
                        logger.info(f"🔥 [_handle_streaming_request] 从final_result找到翻译响应: {tsr_dict}", flush=True)
                    
                    # 2. 从 data 字段中查找
                    elif progress_dto_or_dict and (progress_dto_or_dict.get(ProgressKeys.DATA) or {}).get(ResponseFieldNames.TRANSLATE_SUBTITLES_RESPONSE):
                        tsr_dict = progress_dto_or_dict[ProgressKeys.DATA][ResponseFieldNames.TRANSLATE_SUBTITLES_RESPONSE]
                        logger.info(f"🔥 [_handle_streaming_request] 从data找到翻译响应: {tsr_dict}", flush=True)
                    
                    # 3. 直接从progress_dto_or_dict根级查找
                    elif progress_dto_or_dict and progress_dto_or_dict.get(ResponseFieldNames.TRANSLATE_SUBTITLES_RESPONSE):
                        tsr_dict = progress_dto_or_dict[ResponseFieldNames.TRANSLATE_SUBTITLES_RESPONSE]
                        logger.info(f"🔥 [_handle_streaming_request] 从根级找到翻译响应: {tsr_dict}", flush=True)
                    
                    if tsr_dict:
                        # 记录详细的翻译响应信息
                        translated_content = tsr_dict.get("translated_subtitle_content", "")
                        logger.info(f"🔥 [_handle_streaming_request] 翻译内容长度: {len(translated_content)}", flush=True)
                        if translated_content:
                            content_sample = translated_content[:200] + ("..." if len(translated_content) > 200 else "")
                            logger.info(f"🔥 [_handle_streaming_request] 翻译内容样本: {repr(content_sample)}", flush=True)
                        
                        translate_subtitles_response_dto = TranslateSubtitlesResponseDto(
                            translated_subtitle_content=translated_content,
                            trace_id=tsr_dict.get("trace_id", current_trace_id),
                            segment_results=tsr_dict.get("segment_results", []),
                            total_segments_processed=tsr_dict.get("total_segments_processed", 0),
                            successful_segments=tsr_dict.get("successful_segments", 0),
                            failed_segments=tsr_dict.get("failed_segments", 0)
                        )
                        logger.info(f"🔥 [_handle_streaming_request] 创建翻译响应DTO成功: {translate_subtitles_response_dto}", flush=True)
                    else:
                        logger.debug(f"🔍 [_handle_streaming_request] 未找到翻译响应数据", flush=True)
                    
                    process_video_to_translated_subtitles_response_dto = None
                    if progress_dto_or_dict and progress_dto_or_dict.get(ProgressKeys.FINAL_RESULT, {}).get(ResponseFieldNames.PROCESS_VIDEO_TO_TRANSLATED_SUBTITLES_RESPONSE):
                        pvttsr_dict = progress_dto_or_dict[ProgressKeys.FINAL_RESULT][ResponseFieldNames.PROCESS_VIDEO_TO_TRANSLATED_SUBTITLES_RESPONSE]
                        process_video_to_translated_subtitles_response_dto = ProcessVideoToTranslatedSubtitlesResponseDto(
                            translated_subtitle_content=pvttsr_dict.get("translated_subtitle_content", ""),
                            trace_id=pvttsr_dict.get("trace_id", current_trace_id)
                        )
                    # --- END DTO conversion for final_result fields ---

                    progress_dto = ProgressUpdateDto(
                        trace_id=current_trace_id,
                        stage_name=(progress_dto_or_dict and progress_dto_or_dict.get(ProgressKeys.STAGE_NAME)) or StageNames.UNKNOWN,
                        percentage=int((progress_dto_or_dict and progress_dto_or_dict.get(ProgressKeys.PERCENTAGE)) or 0),
                        message=(progress_dto_or_dict and progress_dto_or_dict.get(ProgressKeys.MESSAGE)) or "",
                        status=status_enum,
                        error_detail=error_detail_dto,
                        # 注意：移除了 data 字段，现在通过 oneof final_result 传递数据
                        is_error=(progress_dto_or_dict and progress_dto_or_dict.get(ProgressKeys.IS_ERROR)) or False,
                        error_message=(progress_dto_or_dict and progress_dto_or_dict.get(ProgressKeys.ERROR_MESSAGE)) or "",
                        video_to_audio_response=video_to_audio_response_dto,
                        audio_to_text_response=audio_to_text_response_dto,
                        generate_subtitles_response=generate_subtitles_response_dto,
                        translate_subtitles_response=translate_subtitles_response_dto,
                        process_video_to_translated_subtitles_response=process_video_to_translated_subtitles_response_dto
                    )

                    # 详细日志：检查创建的 ProgressUpdateDto
                    logger.info(f"🔍 [_handle_streaming_request] 创建的 progress_dto基本信息:", flush=True, module="service")
                    logger.info(f"   - stage_name: {progress_dto.stage_name}", flush=True, module="service")
                    logger.info(f"   - percentage: {progress_dto.percentage}", flush=True, module="service")
                    logger.info(f"   - status: {progress_dto.status}", flush=True, module="service")
                    logger.info(f"   - message: {progress_dto.message}", flush=True, module="service")
                    
                    # 特别检查翻译响应字段
                    if progress_dto.translate_subtitles_response:
                        logger.info(f"🔥 [_handle_streaming_request] ProgressUpdateDto包含翻译响应:", flush=True, module="service")
                        logger.info(f"   - translate_subtitles_response.translated_subtitle_content长度: {len(progress_dto.translate_subtitles_response.translated_subtitle_content) if progress_dto.translate_subtitles_response.translated_subtitle_content else 0}", flush=True, module="service")
                        logger.info(f"   - translate_subtitles_response.trace_id: {progress_dto.translate_subtitles_response.trace_id}", flush=True, module="service")
                        if progress_dto.translate_subtitles_response.translated_subtitle_content:
                            content_sample = progress_dto.translate_subtitles_response.translated_subtitle_content[:100] + ("..." if len(progress_dto.translate_subtitles_response.translated_subtitle_content) > 100 else "")
                            logger.info(f"   - 翻译内容样本: {repr(content_sample)}", flush=True, module="service")
                    else:
                        logger.debug(f"🔍 [_handle_streaming_request] ProgressUpdateDto不包含翻译响应", flush=True, module="service")
                    
                    # 检查其他响应字段
                    logger.debug(f"🔍 [_handle_streaming_request] 其他响应字段状态:", flush=True, module="service")
                    if progress_dto.video_to_audio_response:
                        logger.info(f"🎵 [_handle_streaming_request] ProgressUpdateDto包含video_to_audio_response:", flush=True, module="service")
                        logger.info(f"   - video_to_audio_response.audio_path: {progress_dto.video_to_audio_response.audio_path}", flush=True, module="service")
                        logger.info(f"   - video_to_audio_response.trace_id: {progress_dto.video_to_audio_response.trace_id}", flush=True, module="service")
                    else:
                        logger.debug(f"🔍 [_handle_streaming_request] ProgressUpdateDto不包含video_to_audio_response", flush=True, module="service")
                    logger.debug(f"   - audio_to_text_response: {progress_dto.audio_to_text_response is not None}", flush=True, module="service")
                    logger.debug(f"   - generate_subtitles_response: {progress_dto.generate_subtitles_response is not None}", flush=True, module="service")
                else:
                    logger.error(f"处理流式请求时遇到意外的类型: {type(progress_dto_or_dict)}")
                    # Create an error DTO
                    error_detail_dto = ErrorDetailDto(
                        error_code=ErrorCode.SERVICE_ERROR.value,
                        technical_message=f"Unexpected progress type: {type(progress_dto_or_dict)}",
                        user_message="服务内部处理进度时发生错误。",
                        context={"type": str(type(progress_dto_or_dict))}
                    )
                    progress_dto = ProgressUpdateDto(
                        trace_id=ensure_trace_id(trace_id, f"unexpected_type_error-{error_stage_name}"),
                        stage_name=error_stage_name,
                        percentage=0,
                        message="服务内部错误",
                        status=OperationStatusEnum.ERROR,
                        error_detail=error_detail_dto,
                        is_error=True
                    )

                try:
                    # 确保 current_trace_id 在所有分支中都有定义
                    current_trace_id = getattr(progress_dto, 'trace_id', None) or ensure_trace_id(trace_id, "dto_to_proto_conversion")
                    logger.info(f"🔄 [_handle_streaming_request] 开始转换 DTO 到 protobuf, trace_id: {current_trace_id}", flush=True, module="service", operation="operation_start")
                    
                    # 在转换前记录DTO的详细信息
                    if progress_dto.translate_subtitles_response:
                        logger.info(f"🔥 [_handle_streaming_request] 转换前DTO包含翻译响应，内容长度: {len(progress_dto.translate_subtitles_response.translated_subtitle_content) if progress_dto.translate_subtitles_response.translated_subtitle_content else 0}", flush=True, module="service")
                    if progress_dto.video_to_audio_response:
                        logger.info(f"🎵 [_handle_streaming_request] 转换前DTO包含视频转音频响应，音频路径: {progress_dto.video_to_audio_response.audio_path}", flush=True, module="service")
                    
                    proto_message = convert_dto_to_proto(progress_dto, subtitler_pb2.ProgressUpdate)
                    logger.info(f"🔄 [_handle_streaming_request] DTO 到 protobuf 转换成功, trace_id: {current_trace_id}", flush=True, module="service")
                    logger.info(f"🔄 [_handle_streaming_request] proto_message 类型: {type(proto_message)}", flush=True, module="service")

                    # 检查 protobuf 消息的字段
                    if hasattr(proto_message, 'translate_subtitles_response'):
                        tsr_proto = proto_message.translate_subtitles_response
                        if tsr_proto:
                            logger.info(f"🔥 [_handle_streaming_request] proto_message包含翻译响应字段", flush=True, module="service")
                            if hasattr(tsr_proto, 'translated_subtitle_content'):
                                content = tsr_proto.translated_subtitle_content
                                logger.info(f"🔥 [_handle_streaming_request] proto翻译内容长度: {len(content) if content else 0}", flush=True, module="service")
                                if content:
                                    content_sample = content[:100] + ("..." if len(content) > 100 else "")
                                    logger.info(f"🔥 [_handle_streaming_request] proto翻译内容样本: {repr(content_sample)}", flush=True, module="service")
                            else:
                                logger.warning(f"⚠️ [_handle_streaming_request] proto翻译响应没有translated_subtitle_content字段", flush=True, module="service")
                        else:
                            logger.warning(f"⚠️ [_handle_streaming_request] proto翻译响应字段为空", flush=True, module="service")
                    else:
                        logger.debug(f"🔍 [_handle_streaming_request] proto_message 没有 translate_subtitles_response 字段", flush=True, module="service")
                    
                    if hasattr(proto_message, 'video_to_audio_response'):
                        vtar_proto = proto_message.video_to_audio_response
                        if vtar_proto:
                            logger.info(f"🎵 [_handle_streaming_request] proto_message包含video_to_audio_response字段", flush=True, module="service")
                            if hasattr(vtar_proto, 'audio_path'):
                                audio_path = vtar_proto.audio_path
                                logger.info(f"🎵 [_handle_streaming_request] proto音频路径: {audio_path}", flush=True, module="service")
                                if audio_path:
                                    logger.info(f"🎵 [_handle_streaming_request] proto音频路径非空: {len(audio_path)} 字符", flush=True, module="service")
                                else:
                                    logger.warning(f"⚠️ [_handle_streaming_request] proto音频路径为空", flush=True, module="service")
                            else:
                                logger.warning(f"⚠️ [_handle_streaming_request] proto video_to_audio_response没有audio_path字段", flush=True, module="service")
                        else:
                            logger.warning(f"⚠️ [_handle_streaming_request] proto video_to_audio_response字段为空", flush=True, module="service")
                    else:
                        logger.debug(f"🔍 [_handle_streaming_request] proto_message 没有 video_to_audio_response 字段", flush=True, module="service")

                    yield proto_message
                except DtoToProtoConversionError as e:
                    # Log the error and try to send a simplified progress update
                    logger.error(f"DTO to Proto ProgressUpdate conversion failed: {e}", exc_info=True)
                    
                    # 如果这是一个最终成功的结果，我们需要特别处理，不能简单跳过
                    is_final_success = (
                        getattr(progress_dto, 'video_to_audio_response', None) or
                        getattr(progress_dto, 'audio_to_text_response', None) or
                        getattr(progress_dto, 'generate_subtitles_response', None) or
                        getattr(progress_dto, 'translate_subtitles_response', None) or
                        getattr(progress_dto, 'process_video_to_translated_subtitles_response', None)
                    )
                    
                    # 创建一个极简版本的进度更新，但保留关键的成功信息
                    try:
                        # 如果是最终成功结果，尝试保留响应数据
                        preserved_response_fields = {}
                        if is_final_success:
                            # 尝试保留原始的响应字段
                            preserved_response_fields = {
                                'video_to_audio_response': getattr(progress_dto, 'video_to_audio_response', None),
                                'audio_to_text_response': getattr(progress_dto, 'audio_to_text_response', None),
                                'generate_subtitles_response': getattr(progress_dto, 'generate_subtitles_response', None),
                                'translate_subtitles_response': getattr(progress_dto, 'translate_subtitles_response', None),
                                'process_video_to_translated_subtitles_response': getattr(progress_dto, 'process_video_to_translated_subtitles_response', None)
                            }

                        simple_progress_dto = ProgressUpdateDto(
                            trace_id=getattr(progress_dto, 'trace_id', None) or DefaultValues.DEFAULT_TRACE_ID,
                            stage_name=getattr(progress_dto, 'stage_name', None) or error_stage_name,
                            percentage=getattr(progress_dto, 'percentage', 0),
                            message=getattr(progress_dto, 'message', "处理中..."),
                            status=getattr(progress_dto, 'status', OperationStatusEnum.IN_PROGRESS),
                            # 对于非最终成功结果，不设置复杂字段，避免转换问题
                            # 注意：移除了 data 字段，现在通过 oneof final_result 传递数据
                            error_detail=None,
                            video_to_audio_response=preserved_response_fields.get('video_to_audio_response'),
                            audio_to_text_response=preserved_response_fields.get('audio_to_text_response'),
                            generate_subtitles_response=preserved_response_fields.get('generate_subtitles_response'),
                            translate_subtitles_response=preserved_response_fields.get('translate_subtitles_response'),
                            process_video_to_translated_subtitles_response=preserved_response_fields.get('process_video_to_translated_subtitles_response'),
                            is_error=False,
                            error_message=""
                        )
                        
                        # 如果是最终成功结果，尝试特殊处理以保留核心数据
                        if is_final_success:
                            logger.warning(f"Final success result conversion failed, but attempting to preserve core data for stage: {progress_dto.stage_name}")
                            # 尝试至少传递成功状态和百分比
                            simple_progress_dto.status = OperationStatusEnum.SUCCESS
                            simple_progress_dto.percentage = 100
                            simple_progress_dto.message = f"{progress_dto.stage_name}完成"
                        
                        yield convert_dto_to_proto(simple_progress_dto, subtitler_pb2.ProgressUpdate)
                        # 继续处理下一个进度更新，而不是停止整个流
                        continue
                    except Exception as final_e:
                        logger.critical(f"CRITICAL: Failed to convert even simplified ProgressUpdate: {final_e}", exc_info=True)
                        # 如果是最终成功结果，这是一个严重问题，但我们仍然继续
                        if is_final_success:
                            logger.critical(f"CRITICAL: Lost final success result for stage: {getattr(progress_dto, 'stage_name', 'unknown')}")
                        # 继续处理下一个进度更新
                        continue

                if progress_dto.is_error or (progress_dto.status == OperationStatusEnum.ERROR):
                    logger.warning(f"处理流式请求时在阶段 '{progress_dto.stage_name}' 遇到错误: {progress_dto.message or progress_dto.error_message}")
                    return # 发生错误时停止流式传输
        except Exception as e:
            error_message_str = f"{error_stage_name} 服务层发生意外错误: {str(e)}\n{traceback.format_exc()}"
            logger.error(error_message_str)
            
            # 使用标准化错误创建 DTO
            error_detail_dto = ErrorDetailDto(
                error_code=ErrorCode.SERVICE_ERROR.value,
                technical_message=str(e),
                user_message=f"{error_stage_name}过程中发生服务错误",
                context={"exception_type": type(e).__name__}
            )
            error_progress_dto = ProgressUpdateDto(
                stage_name=error_stage_name,
                message=str(e), # Main message for the progress DTO
                trace_id=ensure_trace_id(trace_id, f"general_exception-{error_stage_name}"),
                percentage=0,
                status=OperationStatusEnum.ERROR,
                error_detail=error_detail_dto,
                is_error=True, # Keep for compatibility
                error_message=str(e) # Keep for compatibility
            )
            try:
                yield convert_dto_to_proto(error_progress_dto, subtitler_pb2.ProgressUpdate)
            except DtoToProtoConversionError as conv_e:
                 logger.critical(f"CRITICAL: Failed to convert general exception error ProgressUpdate DTO to Proto: {conv_e}", exc_info=True)

    def VideoToAudio(self, request: subtitler_pb2.VideoToAudioRequest, context) -> subtitler_pb2.ProgressUpdate:
        """
        gRPC 方法：从视频中提取音频。

        Args:
            request (subtitler_pb2.VideoToAudioRequest): 包含视频文件路径和追踪ID的请求。
            context: gRPC 上下文对象。

        Yields:
            subtitler_pb2.ProgressUpdate: 包含音频提取进度或结果的 protobuf 消息。
        """
        # 确保有效的trace_id并设置上下文
        trace_id = ensure_trace_id(getattr(request, 'trace_id', None), "VideoToAudio")
        set_trace_id(trace_id)
        
        with LogContext(operation="video_to_audio", module="subtitle_service", trace_id=trace_id):
            request_dto: VideoToAudioRequestDto
            try:
                request_dto = convert_proto_to_dto(request, VideoToAudioRequestDto)
            except ProtoToDtoConversionError as e:
                logger.error(f"错误：将 Proto VideoToAudioRequest 转换为 DTO 时失败: {e}", exc_info=True)
                error_detail_dto = ErrorDetailDto(
                    error_code=ErrorCode.INVALID_INPUT.value,
                    technical_message=f"请求转换失败: {e.details or str(e)}",
                    user_message="无效的请求格式",
                    context={"exception": str(e)}
                )
                error_progress_dto = ProgressUpdateDto(
                    trace_id=request.trace_id or DefaultValues.DEFAULT_TRACE_ID, # Use trace_id from original proto request
                    stage_name=StageNames.EXTRACT_AUDIO,
                    percentage=0,
                    message="无效的请求",
                    status=OperationStatusEnum.ERROR,
                    error_detail=error_detail_dto,
                    is_error=True
                )
                try:
                    yield convert_dto_to_proto(error_progress_dto, subtitler_pb2.ProgressUpdate)
                except DtoToProtoConversionError as conv_e:
                    logger.critical(f"CRITICAL: Failed to convert VideoToAudio request conversion error ProgressUpdate DTO to Proto: {conv_e}", exc_info=True)
                return
            except Exception as e: # Catch other potential errors during initial DTO creation if not ProtoToDtoConversionError
                logger.error(f"错误：将 Proto VideoToAudioRequest 转换为 DTO 时发生意外错误: {e}", exc_info=True)
                # Similar error handling as above
                error_detail_dto = ErrorDetailDto(
                    error_code=ErrorCode.INVALID_INPUT.value,
                    technical_message=f"请求解析时发生意外错误: {str(e)}",
                    user_message="无效的请求格式。",
                    context={"exception_type": type(e).__name__}
                )
                error_progress_dto = ProgressUpdateDto(
                    trace_id=ensure_trace_id(request.trace_id, "VideoToAudio-conversion-error"),
                    stage_name=StageNames.EXTRACT_AUDIO,
                    percentage=0, message="无效的请求",
                    status=OperationStatusEnum.ERROR,
                    error_detail=error_detail_dto, is_error=True
                )
                try:
                    yield convert_dto_to_proto(error_progress_dto, subtitler_pb2.ProgressUpdate)
                except DtoToProtoConversionError as conv_e:
                    logger.critical(f"CRITICAL: Failed to convert VideoToAudio general error ProgressUpdate DTO to Proto: {conv_e}", exc_info=True)
                return

        logger.info(LogMessages.SERVICE_REQUEST_RECEIVED.format(service_name="VideoToAudio"))
        logger.info(f"DTO video_path: {request_dto.video_path}, DTO trace_id: {request_dto.trace_id}")
        transcript_thread = TranscriptThread()

        def handler():
            # Assuming extract_audio_from_video can take trace_id or it's handled internally
            # For now, we pass video_path_str and assume trace_id is managed by TranscriptThread if needed,
            # or that progress_dict will contain the correct trace_id.
            for progress_dict in transcript_thread.extract_audio_from_video(
                video_path_str=request_dto.video_path, # Pass DTO's video_path
                trace_id=request_dto.trace_id # Pass trace_id for proper tracking
            ):
                # Ensure trace_id from DTO is consistently used if progress_dict doesn't override
                if ProgressKeys.TRACE_ID not in progress_dict and request_dto.trace_id:
                    progress_dict[ProgressKeys.TRACE_ID] = request_dto.trace_id

                is_final_success = (progress_dict.get(ProgressKeys.FINAL_RESULT, {}).get(ResponseFieldNames.VIDEO_TO_AUDIO_RESPONSE)
                                  and not progress_dict.get(ProgressKeys.IS_ERROR))
                if is_final_success:
                    final_path = progress_dict.get(ProgressKeys.FINAL_RESULT, {}).get(ResponseFieldNames.VIDEO_TO_AUDIO_RESPONSE, {}).get("audio_path")
                    logger.info(LogMessages.SERVICE_RESPONSE_SUCCESS.format(service_name="VideoToAudio"))
                    logger.info(f"音频路径: {final_path}")
                elif progress_dict.get(ProgressKeys.IS_ERROR):
                    logger.warning(LogMessages.SERVICE_PROCESSING_ERROR.format(
                        service_name="VideoToAudio",
                        error=progress_dict.get(ProgressKeys.ERROR_MESSAGE)
                    ))
                else:
                    stage = progress_dict.get(ProgressKeys.STAGE_NAME, "未知阶段")
                    percentage = progress_dict.get(ProgressKeys.PERCENTAGE, 0)
                    message = progress_dict.get(ProgressKeys.MESSAGE, "")
                    logger.info(f"[VideoToAudio] {stage}: {percentage}% - {message}")
                yield progress_dict

        yield from self._handle_streaming_request(handler, StageNames.EXTRACT_AUDIO, trace_id=request_dto.trace_id)


    def AudioToText(self, request: subtitler_pb2.AudioToTextRequest, context) -> subtitler_pb2.ProgressUpdate:
        """
        gRPC 方法：将音频转换为文本。

        可以接受音频文件路径或直接的音频数据。通过 TranscriptThread 执行实际的语音识别，
        并流式返回进度更新和识别结果。

        Args:
            request (subtitler_pb2.AudioToTextRequest): 包含音频路径或数据以及是否请求词级时间戳的请求。
            context: gRPC 上下文对象。

        Yields:
            subtitler_pb2.ProgressUpdate: 包含转录进度或结果的 protobuf 消息。
        """
        logger.info("🎯 [AudioToText] gRPC 方法被调用！")
        logger.info(f"🎯 [AudioToText] 请求参数: audio_path={request.audio_path}, model={request.model}, language={request.language}")
        logger.info(LogMessages.SERVICE_REQUEST_RECEIVED.format(service_name="AudioToText"))
        request_dto: AudioToTextRequestDto
        try:
            request_dto = convert_proto_to_dto(request, AudioToTextRequestDto)
        except ProtoToDtoConversionError as e:
            logger.error(f"错误：将 Proto AudioToTextRequest 转换为 DTO 时失败: {e}", exc_info=True)
            error_detail_dto = ErrorDetailDto(
                error_code=ErrorCode.INVALID_INPUT.value,
                technical_message=f"请求转换失败: {e.details or str(e)}",
                user_message="无效的请求格式",
                context={"exception": str(e)} # Keep original exception string for context
            )
            error_progress_dto = ProgressUpdateDto(
                trace_id=ensure_trace_id(request.trace_id, "AudioToText-conversion-error"),
                stage_name=StageNames.TRANSCRIBE_AUDIO,
                percentage=0,
                message="无效的请求",
                status=OperationStatusEnum.ERROR,
                error_detail=error_detail_dto,
                is_error=True
            )
            try:
                yield convert_dto_to_proto(error_progress_dto, subtitler_pb2.ProgressUpdate)
            except DtoToProtoConversionError as conv_e:
                logger.critical(f"CRITICAL: Failed to convert AudioToText request conversion error ProgressUpdate DTO to Proto: {conv_e}", exc_info=True)
            return
        except Exception as e: # Catch other potential errors during initial DTO creation
            logger.error(f"错误：准备 AudioToTextRequest DTO 时发生意外错误: {e}", exc_info=True)
            error_detail_dto = ErrorDetailDto(
                error_code=ErrorCode.INVALID_INPUT.value,
                technical_message=f"请求解析时发生意外错误: {str(e)}",
                user_message="无效的请求格式。",
                context={"exception_type": type(e).__name__}
            )
            error_progress_dto = ProgressUpdateDto(
                trace_id=ensure_trace_id(request.trace_id, "AudioToText-general-error"),
                stage_name=StageNames.TRANSCRIBE_AUDIO,
                percentage=0, message="无效的请求",
                status=OperationStatusEnum.ERROR,
                error_detail=error_detail_dto, is_error=True
            )
            try:
                yield convert_dto_to_proto(error_progress_dto, subtitler_pb2.ProgressUpdate)
            except DtoToProtoConversionError as conv_e:
                logger.critical(f"CRITICAL: Failed to convert AudioToText general error ProgressUpdate DTO to Proto: {conv_e}", exc_info=True)
            return

        logger.info(f"DTO 音频路径: '{request_dto.audio_path}', DTO 音频数据: {bool(request_dto.audio_data)}, DTO 词级时间戳: {request_dto.request_word_timestamps}, DTO trace_id: {request_dto.trace_id}")
        
        transcript_thread = TranscriptThread()

        def handler():
            # transcribe_audio now yields ProgressUpdateDto
            for progress_update_dto in transcript_thread.transcribe_audio(request_dto=request_dto):
                # 将关键进度信息改为info级别，便于观察
                # Handle both DTO and dict formats for backward compatibility
                if hasattr(progress_update_dto, 'stage_name'):
                    # DTO format
                    logger.info(f"[AudioToText] {progress_update_dto.stage_name}: {progress_update_dto.percentage}% - {progress_update_dto.message}")
                else:
                    # Dict format
                    stage_name = progress_update_dto.get('stage_name', 'Unknown')
                    percentage = progress_update_dto.get('percentage', 0)
                    message = progress_update_dto.get('message', '')
                    logger.info(f"[AudioToText] {stage_name}: {percentage}% - {message}")
                yield progress_update_dto # Yield DTO directly
        
        yield from self._handle_streaming_request(handler, StageNames.TRANSCRIBE_AUDIO, trace_id=request_dto.trace_id)

    def GenerateSubtitles(self, request: subtitler_pb2.GenerateSubtitlesRequest, context) -> subtitler_pb2.ProgressUpdate:
        """
        gRPC 方法：根据文本内容生成字幕文件（通常是 SRT 格式）。

        Args:
            request (subtitler_pb2.GenerateSubtitlesRequest): 包含文本内容和可选音频路径的请求。
            context: gRPC 上下文对象。

        Yields:
            subtitler_pb2.ProgressUpdate: 包含字幕生成进度或结果的 protobuf 消息。
        """
        # 确保有效的trace_id
        trace_id = ensure_trace_id(getattr(request, 'trace_id', None), "GenerateSubtitles")
            
        logger.info(LogMessages.SERVICE_REQUEST_RECEIVED.format(service_name="GenerateSubtitles"))
        logger.info(f"文本长度: {len(request.text)}, 音频路径: '{request.audio_path}', trace_id: {trace_id}")
        transcript_thread = TranscriptThread()
        
        def handler():
            # TranscriptThread.generate_subtitles_from_text 需要文本，以及可选的 audio_path
            # 如果文本为 None 且路径已由先前调用设置（例如在完整流程中），它将从 self.raw_srt_output_path 加载
            # 对于直接的 GenerateSubtitles RPC，我们传递 request.text 和 request.audio_path。
            for progress_dict in transcript_thread.generate_subtitles_from_text(
                text=request.text, 
                audio_path=request.audio_path,
                skip_cache=getattr(request, 'skip_cache', False),
                trace_id=trace_id
            ):
                logger.debug(f"GenerateSubtitles 进度: {progress_dict}")
                yield progress_dict

        yield from self._handle_streaming_request(handler, StageNames.GENERATE_SUBTITLES, trace_id=trace_id)

    @handle_grpc_errors
    def Translate(self, request: subtitler_pb2.TranslateRequest, context) -> subtitler_pb2.TranslateResponse:
        """
        gRPC 方法：翻译字幕片段。
        此方法内部使用Pydantic DTO进行数据处理。

        Args:
            request (subtitler_pb2.TranslateRequest): 包含任务ID、待翻译片段、源语言和目标语言的请求。
            context: gRPC 上下文对象。

        Returns:
            subtitler_pb2.TranslateResponse: 包含任务ID、翻译后的片段或错误详情的 protobuf 消息。
        """
        logger.info(f"🔥 [Translate] Method called! Request type: {type(request)}", flush=True)
        logger.info(f"🔥 [Translate] Request task_id: {getattr(request, 'task_id', 'N/A')}", flush=True)
        logger.info(f"🔥 [Translate] Request source_language: {getattr(request, 'source_language', 'N/A')}", flush=True)
        logger.info(f"🔥 [Translate] Request target_language: {getattr(request, 'target_language', 'N/A')}", flush=True)
        logger.info(f"🔥 [Translate] Request trace_id: {getattr(request, 'trace_id', 'N/A')}", flush=True)
        logger.info(f"🔥 [Translate] Request has text_segments: {hasattr(request, 'text_segments')}", flush=True)
        if hasattr(request, 'text_segments'):
            logger.info(f"🔥 [Translate] Text segments count: {len(request.text_segments)}", flush=True)
            if len(request.text_segments) > 0:
                first_segment = request.text_segments[0]
                logger.info(f"🔥 [Translate] First segment type: {type(first_segment)}", flush=True)
                logger.info("First segment segment_id", segment_id=getattr(first_segment, 'segment_id', 'N/A'))
                logger.info("First segment text_to_translate", text_to_translate=getattr(first_segment, 'text_to_translate', 'N/A'))

        logger.info(LogMessages.SERVICE_REQUEST_RECEIVED.format(service_name="Translate"))
        response_dto: TranslateResponseDto
        translate_request_dto: TranslateRequestDto

        # 1. Proto Request -> DTO Request
        try:
            logger.info(f"🔧 [Translate] Starting proto to DTO conversion...", flush=True, module="service", operation="operation_start")
            logger.info("Request fields", fields=[field.name for field, _ in request.ListFields()], module="service")
            logger.info("Request serialized size", size_bytes=len(request.SerializeToString()), module="service")

            # 尝试直接访问字段
            logger.info(f"🔧 [Translate] Direct field access:", flush=True, module="service")
            logger.info(f"🔧 [Translate] - request.task_id: '{request.task_id}'", flush=True, module="service")
            logger.info(f"🔧 [Translate] - request.source_language: '{request.source_language}'", flush=True, module="service")
            logger.info(f"🔧 [Translate] - request.target_language: '{request.target_language}'", flush=True, module="service")
            logger.info(f"🔧 [Translate] - request.trace_id: '{request.trace_id}'", flush=True, module="service")
            logger.info("Request text segments count", text_segments_count=len(request.text_segments), module="service")

            translate_request_dto = convert_proto_to_dto(request, TranslateRequestDto)
            logger.info(f"🔧 [Translate] Proto to DTO conversion successful!", flush=True, module="service")
            logger.info(f"Translate DTO validated: task_id='{translate_request_dto.task_id}', "
                        f"num_text_segments={len(translate_request_dto.text_segments)}, "
                        f"source_lang='{translate_request_dto.source_language}', "
                        f"target_lang='{translate_request_dto.target_language}'")

        except ProtoToDtoConversionError as e:
            logger.error(f"❌ [Translate] ProtoToDtoConversionError: {e}", flush=True, module="service", operation="operation_error")
            technical_message = f"TranslateRequest Proto to DTO conversion failed: {e.details or str(e)}"
            logger.error(technical_message, exc_info=True)
            # @handle_grpc_errors will convert this to appropriate gRPC error
            raise ConfigurationError(
                message=technical_message,
                user_message="无效的翻译请求格式。",
                details={"exception_type": type(e).__name__, "original_request_summary": str(request)[:200]}
            )
        except Exception as e: # Catch other Pydantic validation errors if not wrapped by ProtoToDtoConversionError
            technical_message = f"TranslateRequest DTO validation failed: {str(e)}"
            logger.error(technical_message, exc_info=True)
            raise ConfigurationError(
                message=technical_message,
                user_message="无效的翻译请求格式。",
                details={"exception_type": type(e).__name__, "original_request_summary": str(request)[:200]}
            )

        # 2. Core Logic using DTO Request, produces DTO Response
        # Use actual translation service instead of simulation
        try:
            from subtitle.core.translate import TranslatorFactory, TranslatorType
            from ai_config_manager import get_all_configs

            # Try to get AI configuration from frontend first
            ai_configs = get_all_configs()
            logger.info(f"Found {len(ai_configs)} AI configurations from frontend")

            # Find a suitable AI configuration for translation
            suitable_config = None
            for config_id, config in ai_configs.items():
                logger.info(f"Checking config {config_id}: enabled={config.is_enabled}, provider_type={config.provider_type}")

                if config.is_enabled and hasattr(config, 'credentials') and config.credentials:
                    # Extract API key from credentials map
                    api_key = None
                    if hasattr(config.credentials, 'get'):
                        api_key = config.credentials.get('api_key')
                    elif hasattr(config, 'credentials') and isinstance(config.credentials, dict):
                        api_key = config.credentials.get('api_key')

                    if api_key:
                        # Prefer OpenAI-compatible services for translation
                        if 'openai' in config.provider_type.lower() or 'gpt' in config.provider_id.lower():
                            suitable_config = config
                            logger.info(f"Selected OpenAI-compatible config: {config.provider_id}")
                            break
                        elif not suitable_config:  # Use as fallback
                            suitable_config = config
                            logger.info(f"Using fallback config: {config.provider_id}")

            if suitable_config:
                # Extract configuration details
                api_key = None
                base_url = None

                # Get API key from credentials
                if hasattr(suitable_config.credentials, 'get'):
                    api_key = suitable_config.credentials.get('api_key')
                elif isinstance(suitable_config.credentials, dict):
                    api_key = suitable_config.credentials.get('api_key')

                # Get base URL from attributes
                if hasattr(suitable_config, 'attributes') and suitable_config.attributes:
                    if hasattr(suitable_config.attributes, 'get'):
                        base_url = suitable_config.attributes.get('api_base_url')
                    elif isinstance(suitable_config.attributes, dict):
                        base_url = suitable_config.attributes.get('api_base_url')

                logger.info(f"Using AI config: {suitable_config.provider_id} ({suitable_config.provider_type}) for translation")
                logger.info(f"API key length: {len(api_key) if api_key else 0}, Base URL: {base_url}")

                # Create translator using frontend AI config
                translator = TranslatorFactory.create_translator(
                    translator_type=TranslatorType.OPENAI,  # Use OpenAI translator for all AI services
                    target_language=translate_request_dto.target_language,
                    thread_num=1,
                    batch_num=1,
                    use_cache=True,
                    api_key=api_key,
                    base_url=base_url,
                    update_callback=None
                )
            else:
                # Fallback to config file if no frontend AI config available
                logger.warning("No suitable frontend AI config found, trying config file...")
                from subtitle.config.config_manager import get_translator_config

                try:
                    translator_config = get_translator_config()
                    logger.info(f"Using config file translator: {translator_config.default_translator}")

                    if not translator_config.openai_api_key:
                        raise ValueError("No AI configuration available. Please configure AI services in the frontend.")

                    translator_type_map = {
                        "openai": TranslatorType.OPENAI,
                        "deeplx": TranslatorType.DEEPLX
                    }
                    translator_type = translator_type_map.get(
                        translator_config.default_translator,
                        TranslatorType.OPENAI
                    )

                    translator = TranslatorFactory.create_translator(
                        translator_type=translator_type,
                        target_language=translate_request_dto.target_language,
                        thread_num=translator_config.thread_num,
                        batch_num=translator_config.batch_num,
                        use_cache=translator_config.use_cache,
                        api_key=translator_config.openai_api_key,
                        base_url=translator_config.openai_base_url,
                        update_callback=None
                    )
                except Exception as config_error:
                    logger.error(f"Failed to load config file: {config_error}")
                    raise ValueError("No AI configuration available. Please configure AI services in the frontend.")

            # Prepare text segments for translation
            subtitle_dict = {}
            for i, text_to_translate_seg_dto in enumerate(translate_request_dto.text_segments):
                subtitle_dict[str(i)] = text_to_translate_seg_dto.text_to_translate

            logger.info(f"Translating {len(subtitle_dict)} segments to {translate_request_dto.target_language}")

            # Perform translation
            logger.info(f"Starting translation with {len(subtitle_dict)} segments...")
            try:
                translated_dict = translator._translate_chunk(subtitle_dict, translate_request_dto.trace_id)
                logger.info(f"Translation completed successfully. Results: {len(translated_dict)} segments")
            except Exception as translate_error:
                logger.error(f"Translation failed: {translate_error}")
                logger.error(f"Translation error type: {type(translate_error).__name__}")
                logger.error(f"Translation error details: {str(translate_error)}")
                raise translate_error

            # Convert results to DTO format
            translated_segments_dto_list = []
            for i, text_to_translate_seg_dto in enumerate(translate_request_dto.text_segments):
                translated_text = translated_dict.get(str(i), text_to_translate_seg_dto.text_to_translate)

                translated_segment_dto = TranslatedSegmentDto(
                    segment_id=text_to_translate_seg_dto.segment_id,
                    original_text=text_to_translate_seg_dto.text_to_translate,
                    translated_text=translated_text,
                    start_time_ms=None,  # No timing info in simplified request
                    end_time_ms=None,    # No timing info in simplified request
                    status=OperationStatusEnum.OPERATION_STATUS_SUCCESS
                )
                translated_segments_dto_list.append(translated_segment_dto)

            response_dto = TranslateResponseDto(
                task_id=translate_request_dto.task_id,
                translated_segments=translated_segments_dto_list,
                trace_id=translate_request_dto.trace_id,
                error_detail=None
            )
            logger.info(f"Translation successful for task_id '{translate_request_dto.task_id}', {len(translated_segments_dto_list)} segments translated")

        except SubtitleProcessingError as spe: # Catch specific known errors
            logger.warning(f"Translate caught SubtitleProcessingError: {spe.message}", exc_info=True)
            raise # Re-raise for @handle_grpc_errors
        except Exception as e: # Catch unexpected errors
            technical_message = f"Unexpected error during Translate processing logic: {str(e)}"
            logger.error(technical_message, exc_info=True)
            logger.error(f"Translation error details - task_id: {translate_request_dto.task_id}, "
                        f"target_language: {translate_request_dto.target_language}, "
                        f"num_segments: {len(translate_request_dto.text_segments)}, "
                        f"exception_type: {type(e).__name__}")

            # Check if it's a configuration error
            if "api_key" in str(e).lower() or "openai" in str(e).lower():
                user_message = "翻译服务配置错误，请检查 AI 配置。"
            elif "unavailable" in str(e).lower() or "connection" in str(e).lower():
                user_message = "翻译服务暂时不可用，请稍后重试。"
            else:
                user_message = "翻译过程中发生意外的服务端错误。"

            # Create a generic APIError or FileProcessingError if appropriate
            raise APIError( # Or a more specific error type
                message=technical_message,
                user_message=user_message,
                details={
                    "task_id": translate_request_dto.task_id,
                    "target_language": translate_request_dto.target_language,
                    "exception_type": type(e).__name__,
                    "error_str": str(e)
                }
            ) from e

        # 3. DTO Response -> Proto Response
        try:
            proto_response = convert_dto_to_proto(response_dto, subtitler_pb2.TranslateResponse)
        except DtoToProtoConversionError as e:
            technical_message = f"TranslateResponse DTO to Proto conversion failed: {e.details or str(e)}"
            logger.error(technical_message, exc_info=True)
            # This error will be caught by @handle_grpc_errors and converted to an INTERNAL gRPC error
            raise APIError( # Or a more specific error like ConfigurationError if it's a structural issue
                message=technical_message,
                user_message="翻译结果准备失败。",
                details={
                    "task_id": response_dto.task_id,
                    "exception_type": type(e).__name__,
                    "original_dto_summary": str(response_dto)[:200]
                }
            )
        
        logger.info(LogMessages.SERVICE_RESPONSE_SUCCESS.format(service_name="Translate"))
        return proto_response

    def TranslateSubtitles(self, request: subtitler_pb2.TranslateSubtitlesRequest, context) -> subtitler_pb2.ProgressUpdate:
        """
        gRPC 方法：翻译字幕内容。

        此方法接收字幕内容，并使用配置的翻译服务将其翻译为目标语言。
        通过 TranscriptThread 执行实际的翻译操作，并流式返回进度更新和翻译结果。

        Args:
            request (subtitler_pb2.TranslateSubtitlesRequest): 包含字幕内容、目标语言等信息的请求。
            context: gRPC 上下文对象。

        Yields:
            subtitler_pb2.ProgressUpdate: 包含翻译进度或结果的 protobuf 消息。
        """
        request_start_time = time.time()
        
        # 确保有效的trace_id
        trace_id = ensure_trace_id(getattr(request, 'trace_id', None), "TranslateSubtitles")
        
        logger.info(f"🔥 [TranslateSubtitles] 方法调用开始 | trace_id: {trace_id}")
        logger.info(LogMessages.SERVICE_REQUEST_RECEIVED.format(service_name="TranslateSubtitles"))
        
        # 详细请求参数日志
        subtitle_content_length = len(request.subtitle_content) if hasattr(request, 'subtitle_content') else 0
        target_language = getattr(request, 'target_language', 'unknown')
        skip_cache = getattr(request, 'skip_cache', False)
        
        logger.info(f"📊 [TranslateSubtitles] 请求参数分析:")
        logger.info(f"   - 字幕内容长度: {subtitle_content_length} 字符")
        logger.info(f"   - 目标语言: '{target_language}'")
        logger.info(f"   - 跳过缓存: {skip_cache}")
        logger.info(f"   - trace_id: {trace_id}")
        
        # 验证字幕内容
        if subtitle_content_length == 0:
            logger.error(f"❌ [TranslateSubtitles] 字幕内容为空 | trace_id: {trace_id}")
            raise ValueError("字幕内容不能为空")
        
        # 预估处理时间（基于内容长度）
        estimated_segments = subtitle_content_length // 100  # 粗略估算
        logger.info(f"⏱️  [TranslateSubtitles] 预估处理 ~{estimated_segments} 个片段")
        
        # 显示字幕内容样本（用于调试）
        content_sample = request.subtitle_content[:200] + ("..." if subtitle_content_length > 200 else "")
        logger.debug(f"📝 [TranslateSubtitles] 字幕内容样本: {repr(content_sample)}")
        
        transcript_thread = TranscriptThread()

        def handler():
            logger.info(f"🚀 [TranslateSubtitles] 开始调用 TranscriptThread.translate_subtitle_content")
            
            try:
                progress_count = 0
                # 调用 TranscriptThread 的翻译方法
                for progress_dict in transcript_thread.translate_subtitle_content(
                    subtitle_content=request.subtitle_content,
                    target_language=request.target_language,
                    input_format="srt",  # 默认SRT格式
                    skip_cache=getattr(request, 'skip_cache', False),
                    trace_id=trace_id
                ):
                    progress_count += 1
                    elapsed_time = time.time() - request_start_time
                    
                    logger.info(f"📈 [TranslateSubtitles] 进度更新 #{progress_count} | 耗时: {elapsed_time:.2f}s | trace_id: {trace_id}")
                    logger.debug(f"📈 [TranslateSubtitles] 进度详情: {progress_dict}")
                    
                    # 检查进度字典的关键字段
                    stage_name = progress_dict.get('stage_name', 'unknown')
                    percentage = progress_dict.get('percentage', 0)
                    message = progress_dict.get('message', '')
                    
                    logger.info(f"📈 [TranslateSubtitles] 阶段: {stage_name} | 进度: {percentage}% | 消息: {message}")
                    
                    # 如果有翻译结果，记录结果统计
                    data = progress_dict.get('data') or {}
                    if 'translate_subtitles_response' in data:
                        response_data = data['translate_subtitles_response']
                        translated_content = response_data.get('translated_subtitle_content', '')
                        logger.info(f"✅ [TranslateSubtitles] 翻译完成 | 结果长度: {len(translated_content)} 字符 | trace_id: {trace_id}")
                        
                        # 显示翻译结果样本
                        result_sample = translated_content[:200] + ("..." if len(translated_content) > 200 else "")
                        logger.debug(f"📝 [TranslateSubtitles] 翻译结果样本: {repr(result_sample)}")
                    
                    yield progress_dict
                    
                total_time = time.time() - request_start_time
                logger.info(f"🏁 [TranslateSubtitles] TranscriptThread 处理完成 | 总进度更新: {progress_count} | 总耗时: {total_time:.2f}s | trace_id: {trace_id}")
                
            except Exception as e:
                error_time = time.time() - request_start_time
                logger.error(f"❌ [TranslateSubtitles] TranscriptThread 处理异常 | 耗时: {error_time:.2f}s | 错误: {str(e)} | trace_id: {trace_id}", exc_info=True)
                raise

        try:
            logger.info(f"🔄 [TranslateSubtitles] 开始流式处理")
            yield from self._handle_streaming_request(handler, StageNames.TRANSLATE_SUBTITLES, trace_id=trace_id)
            
            final_time = time.time() - request_start_time
            logger.info(f"✅ [TranslateSubtitles] 方法执行完成 | 总耗时: {final_time:.2f}s | trace_id: {trace_id}")
            
        except Exception as e:
            error_time = time.time() - request_start_time
            logger.error(f"❌ [TranslateSubtitles] 流式处理异常 | 耗时: {error_time:.2f}s | 错误: {str(e)} | trace_id: {trace_id}", exc_info=True)
            raise

    def ProcessVideoToTranslatedSubtitles(self, request: subtitler_pb2.ProcessVideoToTranslatedSubtitlesRequest, context) -> subtitler_pb2.ProgressUpdate:
        """
        gRPC 方法：处理从视频到翻译字幕的完整流程。

        这是一个复合操作，依次执行视频提取音频、音频转文字、生成字幕、翻译字幕等步骤。
        TranscriptThread.process_video_to_translated_subtitles 负责处理子步骤的进度报告。

        Args:
            request (subtitler_pb2.ProcessVideoToTranslatedSubtitlesRequest): 包含视频路径和目标翻译语言的请求。
            context: gRPC 上下文对象。

        Yields:
            subtitler_pb2.ProgressUpdate: 包含整个流程中各个阶段进度或最终结果的 protobuf 消息。
        """
        # 确保有效的trace_id
        trace_id = ensure_trace_id(getattr(request, 'trace_id', None), "ProcessVideoToTranslatedSubtitles")
            
        logger.info(LogMessages.SERVICE_REQUEST_RECEIVED.format(service_name="ProcessVideoToTranslatedSubtitles"))
        logger.info(f"视频路径: '{request.video_path}', 目标语言: '{request.target_language}', trace_id: {trace_id}")
        transcript_thread = TranscriptThread()

        def handler():
            for progress_dict in transcript_thread.process_video_to_translated_subtitles(
                video_path_str=request.video_path,
                target_language=request.target_language,
                trace_id=trace_id
                # request_word_timestamps 字段在此请求中不存在，已移除
            ):
                logger.debug(f"ProcessVideoToTranslatedSubtitles 进度: {progress_dict}")
                yield progress_dict

        yield from self._handle_streaming_request(handler, StageNames.PROCESS_VIDEO, trace_id=trace_id)

    @handle_grpc_errors
    def SaveSubtitle(self, request: subtitler_pb2.SaveSubtitleRequest, context) -> subtitler_pb2.SaveSubtitleResponse:
        """
        gRPC 方法：保存单个字幕文件。

        根据请求的格式、布局等参数，使用 SubtitleProcessor 处理并保存字幕。
        此方法内部使用Pydantic DTO进行数据处理。

        Args:
            request (subtitler_pb2.SaveSubtitleRequest): 包含字幕分段、文件名、目标格式、布局等信息的请求。
            context: gRPC 上下文对象。

        Returns:
            subtitler_pb2.SaveSubtitleResponse: 包含保存结果（如文件路径、数据等）的 protobuf 消息。
        """
        logger.info(LogMessages.SERVICE_REQUEST_RECEIVED.format(service_name="SaveSubtitle"))
        response_dto: SaveSubtitleResponseDto
        save_subtitle_request_dto: SaveSubtitleRequestDto

        # 1. Proto Request -> DTO Request
        try:
            save_subtitle_request_dto = convert_proto_to_dto(request, SaveSubtitleRequestDto)
            logger.info(f"SaveSubtitle DTO validated: file_name='{save_subtitle_request_dto.file_name}', format='{save_subtitle_request_dto.format}', trace_id='{save_subtitle_request_dto.trace_id}'")

        except ProtoToDtoConversionError as e:
            technical_message = f"SaveSubtitleRequest Proto to DTO conversion failed: {e.details or str(e)}"
            logger.error(technical_message, exc_info=True)
            raise ConfigurationError(
                message=technical_message,
                user_message="无效的保存字幕请求格式。",
                details={"exception_type": type(e).__name__, "original_request_summary": str(request)[:200]}
            )
        except Exception as e: # Catch other Pydantic validation errors
            technical_message = f"SaveSubtitleRequest DTO validation failed: {str(e)}"
            logger.error(technical_message, exc_info=True)
            raise ConfigurationError(
                message=technical_message,
                user_message="无效的保存字幕请求格式。",
                details={"exception_type": type(e).__name__, "original_request_summary": str(request)[:200]}
            )

        # 2. Core Logic using DTO Request, produces DTO Response
        try:
            # Convert DTO's content (List[TimestampedTextSegmentDto]) to list of proto TimestampedTextSegment
            # if self.processor.process_save_subtitle still expects proto segments.
            # This is an adaptation layer. Ideally, the processor would accept DTO segments.
            proto_segments_for_processor = []
            if save_subtitle_request_dto.segments:
                for seg_dto in save_subtitle_request_dto.segments:
                    proto_segments_for_processor.append(subtitler_pb2.SubtitleSegment(
                        original_text=seg_dto.original_text or "",  # SubtitleSegment fields
                        translated_text=seg_dto.translated_text or "",
                        start_time=seg_dto.start_time if seg_dto.start_time is not None else 0,
                        end_time=seg_dto.end_time if seg_dto.end_time is not None else 0
                        # TimestampedTextSegmentDto might have status/error_detail, but proto segment doesn't.
                    ))
            
            # Call the processor method
            # Note: The processor's `process_save_subtitle` signature might need to be updated
            # if it's to use `target_path` from the DTO, or if it's to accept DTOs directly.
            # Current call adapts DTO fields to existing processor signature.
            package = self.processor.process_save_subtitle(
                segments_proto_list=proto_segments_for_processor,
                original_filename_or_title=save_subtitle_request_dto.file_name, # DTO's file_name
                target_format=save_subtitle_request_dto.format,
                target_layout=save_subtitle_request_dto.layout, # DTO's layout
                auto_save_to_default=bool(save_subtitle_request_dto.auto_save_to_default), # Ensure bool
                ass_style_options=save_subtitle_request_dto.ass_style_options # DTO's ass_style_options (dict)
            )

            if package: # Processor succeeded and returned a result package
                logger.info(LogMessages.SERVICE_RESPONSE_SUCCESS.format(service_name="SaveSubtitle"))
                logger.info(f"文件已成功处理，路径: {package.get('file_path')}")
                response_dto = SaveSubtitleResponseDto(
                    trace_id=save_subtitle_request_dto.trace_id, # Carry over from request DTO
                    file_path=package.get('file_path', ''),
                    file_data=package.get('file_data', b''),
                    file_name=package.get('file_name', ''),
                    file_size=package.get('file_size', 0),
                    saved_to_default=package.get('saved_to_default', False),
                    format=package.get('format', ''),
                    layout=package.get('layout', ''),
                    content_source=package.get('content_source', 'user_input'),
                    original_filename_or_title=package.get('original_filename_or_title', ''),
                    status=OperationStatusEnum.SUCCESS,
                    error_detail=None
                )
            else: # Processor returned None, indicating a handled failure without an exception
                user_msg = "字幕保存失败，字幕处理器未能返回有效结果。"
                logger.warning(f"{LogMessages.SERVICE_PROCESSING_ERROR.format(service_name='SaveSubtitle', error=user_msg)} (processor returned None)")
                error_detail_dto = ErrorDetailDto(
                    error_code=ErrorCode.PROCESSING_ERROR.value,
                    technical_message="SubtitleProcessor.process_save_subtitle returned None, indicating an internal issue or invalid data for processing.",
                    user_message=user_msg,
                    context={
                        "file_name": save_subtitle_request_dto.file_name,
                        "format": save_subtitle_request_dto.format
                    }
                )
                response_dto = SaveSubtitleResponseDto(
                    trace_id=save_subtitle_request_dto.trace_id,
                    file_path='',
                    file_data=b'',
                    file_name='',
                    file_size=0,
                    saved_to_default=False,
                    format=save_subtitle_request_dto.format,
                    layout=save_subtitle_request_dto.layout,
                    content_source='user_input',
                    original_filename_or_title=save_subtitle_request_dto.file_name,
                    status=OperationStatusEnum.ERROR,
                    error_detail=error_detail_dto
                )
        
        except SubtitleProcessingError as spe:
            # Specific, known business logic errors from the processor
            logger.warning(f"SaveSubtitle caught SubtitleProcessingError: {spe.message}", exc_info=True)
            # Re-raise for @handle_grpc_errors to convert to gRPC status.
            # Alternatively, if the method MUST return a DTO that's then converted,
            # we'd build response_dto here. But @handle_grpc_errors expects exceptions.
            raise

        except Exception as e: # Other unexpected errors during processing
            technical_message = f"Unexpected error during SaveSubtitle processing logic: {str(e)}"
            logger.error(technical_message, exc_info=True)
            # Raise an error for @handle_grpc_errors
            raise FileProcessingError( # Or a more generic ServiceError
                message=technical_message,
                user_message="保存字幕时发生意外的服务端错误。",
                details={
                    "file_name": save_subtitle_request_dto.file_name,
                    "exception_type": type(e).__name__
                }
            ) from e # Preserve original exception context

        # 3. DTO Response -> Proto Response
        # This part is reached if no exceptions were raised that @handle_grpc_errors would catch,
        # meaning response_dto is populated (either with success=True or success=False from a handled processor failure).
        try:
            proto_response = convert_dto_to_proto(response_dto, subtitler_pb2.SaveSubtitleResponse)
        except DtoToProtoConversionError as e:
            technical_message = f"SaveSubtitleResponse DTO to Proto conversion failed: {e.details or str(e)}"
            logger.error(technical_message, exc_info=True)
            raise APIError(
                message=technical_message,
                user_message="保存字幕结果准备失败。",
                details={
                    "trace_id": response_dto.trace_id,
                    "exception_type": type(e).__name__,
                    "original_dto_summary": str(response_dto)[:200]
                }
            )
        return proto_response


    def BatchSaveSubtitle(self, request: subtitler_pb2.BatchSaveSubtitleRequest, context) -> subtitler_pb2.BatchSaveSubtitleResponse:
        """
        gRPC 方法：批量保存字幕文件。

        根据请求的格式列表、布局列表等参数，为每个组合生成并保存字幕文件。

        Args:
            request (subtitler_pb2.BatchSaveSubtitleRequest): 包含字幕分段、文件名前缀、目标格式列表、
                                                            布局列表等信息的请求。
            context: gRPC 上下文对象。

        Returns:
            subtitler_pb2.BatchSaveSubtitleResponse: 包含多个 SaveSubtitleResponse 消息的列表。
        """
        try:
            logger.info(LogMessages.SERVICE_REQUEST_RECEIVED.format(service_name="BatchSaveSubtitle"))
            logger.info(f"文件名前缀: {request.file_name_prefix}, 格式数量: {len(request.formats)}, 布局数量: {len(request.layouts)}")

            ass_options_py = None
            if request.HasField('ass_style_options') and request.ass_style_options.ListFields():
                 ass_options_py = {field.name: value for field, value in request.ass_style_options.ListFields()}

            packages = self.processor.process_batch_save_subtitles(
                segments_proto_list=list(request.segments),
                original_filename_or_title=request.file_name_prefix, # 使用 proto 定义的 file_name_prefix
                formats_to_generate=list(request.formats),
                layouts_to_generate=list(request.layouts),
                translation_requested=request.translation_requested,
                auto_save_to_default=request.auto_save_to_default,
                ass_style_options=ass_options_py
            )

            response_files = []
            for package in packages:
                if package: # 确保 package 不是 None
                    response_files.append(subtitler_pb2.SaveSubtitleResponse(
                        file_path=package.get('file_path', ""),
                        file_data=package.get('file_data', b""),
                        file_name=package.get('file_name', ""),
                        file_size=package.get('file_size', 0),
                        saved_to_default=package.get('saved_to_default', False),
                        format=package.get('format', ""),
                        layout=package.get('layout', ""),
                        original_filename_or_title=package.get('original_filename_or_title', "")
                    ))
            
            logger.info(LogMessages.SERVICE_RESPONSE_SUCCESS.format(service_name="BatchSaveSubtitle"))
            logger.info(f"成功生成 {len(response_files)} 个文件")
            return subtitler_pb2.BatchSaveSubtitleResponse(files=response_files)

        except Exception as e:
            error_message = f"批量保存字幕时发生内部错误: {str(e)}"
            logger.error(f"BatchSaveSubtitle 服务层错误: {error_message}", exc_info=True)
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(error_message)
            return subtitler_pb2.BatchSaveSubtitleResponse()

    def ClearCache(self, request: subtitler_pb2.ClearCacheRequest, context) -> subtitler_pb2.ClearCacheResponse:
        """
        gRPC 方法：清除所有AI模型缓存

        Args:
            request (subtitler_pb2.ClearCacheRequest): 清除缓存请求
            context: gRPC 上下文对象

        Returns:
            subtitler_pb2.ClearCacheResponse: 清除结果响应
        """
        logger.info("收到清除缓存请求", extra={'operation': 'request_received'})
        
        try:
            from subtitle.storage.cache_manager import CacheManager
            from subtitle.config import CACHE_PATH
            
            # 获取缓存管理器实例
            cache_manager = CacheManager(str(CACHE_PATH))
            
            # 清理所有缓存
            cache_manager.cleanup_old_cache()
            
            # 如果需要，还可以清除其他缓存
            # 例如：LLM结果缓存、翻译缓存等
            
            logger.info("所有AI模型缓存已清除")
            
            return subtitler_pb2.ClearCacheResponse(
                success=True,
                message="所有AI模型缓存已清除"
            )
            
        except Exception as e:
            error_message = f"清除缓存失败: {str(e)}"
            logger.error(error_message)
            
            return subtitler_pb2.ClearCacheResponse(
                success=False,
                message=error_message
            )
