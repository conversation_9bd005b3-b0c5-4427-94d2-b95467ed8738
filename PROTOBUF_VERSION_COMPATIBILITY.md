# Protobuf 版本兼容性指南

## 📋 **当前版本配置**

### **Python 后端**
```
protobuf==4.21.6
grpcio==1.62.1
grpcio-tools==1.62.1
grpcio-health-checking==1.62.1
grpcio-reflection==1.62.1
grpcio-status==1.62.1
```

### **JavaScript 前端**
```
google-protobuf: 3.21.4
@grpc/grpc-js: 1.9.13
@grpc/proto-loader: 0.7.10
grpc-tools: 1.12.4
```

## 🎯 **兼容性说明**

### **protobuf 协议兼容性**
- **Python protobuf 4.21.6** 和 **JavaScript google-protobuf 3.21.4** 都实现 protobuf 3.x 协议
- 虽然版本号不同，但协议兼容，可以正常进行跨语言通信
- 4.21.6 是 Python protobuf 库的版本号
- 3.21.4 是 JavaScript protobuf 库的版本号

### **gRPC 兼容性**
- **Python grpcio 1.62.1** 是稳定的生产版本
- **JavaScript @grpc/grpc-js 1.9.13** 与 Python 版本兼容
- 两者都支持相同的 gRPC 协议特性

## ⚠️ **重要注意事项**

### **不要随意升级的包**
1. **protobuf**: 版本必须与 grpcio-tools 兼容
2. **grpcio-tools**: 版本必须与 protobuf 兼容
3. **google-protobuf**: JavaScript 端的核心包

### **可以安全移除的包**
- `grpcio-testing`: 仅用于测试，生产环境不需要
- `protoc-gen-validate`: 验证生成器，可选
- `grpcio-status`: 状态码处理，可选（但建议保留）
- `grpcio-reflection`: 服务反射，主要用于调试

### **版本冲突解决原则**
1. **优先保证核心包兼容**: protobuf + grpcio + grpcio-tools
2. **移除非必需包**: 避免依赖冲突
3. **锁定版本**: 防止自动升级导致的兼容性问题

## 🔧 **环境重建步骤**

如果遇到版本冲突，按以下步骤重建环境：

### **Python 环境**
```bash
# 1. 清理环境
pip uninstall protobuf grpcio grpcio-tools grpcio-health-checking grpcio-reflection grpcio-status -y

# 2. 安装兼容版本
pip install "protobuf==4.21.6" "grpcio==1.62.1" "grpcio-tools==1.62.1" "grpcio-health-checking==1.62.1" "grpcio-reflection==1.62.1" "grpcio-status==1.62.1"

# 3. 验证安装
python -c "import google.protobuf; print('protobuf version:', google.protobuf.__version__)"
```

### **JavaScript 环境**
```bash
# 1. 清理缓存
npm cache clean --force

# 2. 重新安装
cd electron-app
npm install

# 3. 验证版本
npm list google-protobuf @grpc/grpc-js
```

## 📊 **测试验证**

安装完成后，执行以下测试：

1. **重新生成 protobuf 代码**:
   ```bash
   ./scripts/proto_sync.sh --force
   ```

2. **启动服务测试**:
   ```bash
   # 启动 Python 后端
   python backend/server.py
   
   # 启动前端应用
   cd electron-app && npm run dev
   ```

3. **功能测试**:
   - 测试视频转音频功能
   - 检查 gRPC 通信是否正常
   - 验证 protobuf 消息传输

## 🚨 **故障排除**

### **常见错误**
1. **protobuf 版本警告**: 通常可以忽略，只要功能正常
2. **gRPC 连接失败**: 检查端口和服务状态
3. **protobuf 解析错误**: 重新生成 protobuf 代码

### **调试命令**
```bash
# 检查 Python protobuf 版本
python -c "import google.protobuf; print(google.protobuf.__version__)"

# 检查 gRPC 服务状态
lsof -i :50051

# 检查 JavaScript 依赖
cd electron-app && npm list | grep -E "(protobuf|grpc)"
```

---

**最后更新**: 2024年12月10日
**维护者**: MonkeyFX
