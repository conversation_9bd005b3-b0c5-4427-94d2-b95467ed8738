const { getMainLogger } = require('../logging/main-logger.js');
const logger = getMainLogger();

const { ipcMain, dialog } = require('electron');
const fs = require('fs');
const path = require('path');

// Dependencies will be injected.
let windowManager;

function initializeFilesystemIpcHandlers(winMgr) {
  windowManager = winMgr;

  // IPC Handler for opening file or folder dialog
  ipcMain.handle('open-file-or-folder-dialog', async (event) => {
    const mainWindow = windowManager.getMainWindow();
    if (!mainWindow) {
      logger.error('[IPC Handler - FS] Main window not available for dialog.');
      return { cancelled: true, error: 'Main window not found' };
    }

    try {
      const result = await dialog.showOpenDialog(mainWindow, {
        properties: ['openFile', 'openDirectory'],
        filters: [
          { name: 'Media Files', extensions: ['mp3', 'wav', 'aac', 'm4a', 'mp4', 'mkv', 'mov', 'avi', 'webm'] },
          { name: 'All Files', extensions: ['*'] }
        ]
      });

      if (result.canceled || !result.filePaths || result.filePaths.length === 0) {
        if (windowManager) windowManager.logToRenderer('[IPC Handler - FS] File/Folder selection cancelled.');
        return { cancelled: true };
      }

      const selectedPath = result.filePaths[0];
      if (windowManager) windowManager.logToRenderer(`[IPC Handler - FS] File/Folder selected: ${selectedPath}`);

      try {
        const stats = fs.statSync(selectedPath);
        if (stats.isFile()) {
          if (windowManager) windowManager.logToRenderer(`[IPC Handler - FS] Selected path is a file.`);
          return { type: 'file', path: selectedPath, cancelled: false };
        } else if (stats.isDirectory()) {
          if (windowManager) windowManager.logToRenderer(`[IPC Handler - FS] Selected path is a directory.`);
          return { type: 'directory', path: selectedPath, cancelled: false };
        } else {
          if (windowManager) windowManager.logToRenderer(`[IPC Handler - FS] Selected path is neither a file nor a directory: ${selectedPath}`);
          return { cancelled: true, error: 'Selected path is not a file or directory' };
        }
      } catch (statError) {
        logger.error(`[IPC Handler - FS] Error getting stats for path ${selectedPath}:`, statError);
        if (windowManager) windowManager.logToRenderer(`[IPC Handler - FS] Error stating path ${selectedPath}: ${statError.message}`);
        return { cancelled: true, error: `Error accessing path: ${statError.message}` };
      }
    } catch (dialogError) {
      logger.error('[IPC Handler - FS] Error showing open dialog:', dialogError);
      if (windowManager) windowManager.logToRenderer(`[IPC Handler - FS] Error in open dialog: ${dialogError.message}`);
      return { cancelled: true, error: `Dialog error: ${dialogError.message}` };
    }
  });

  // IPC Handler for reading media files in a directory
  ipcMain.handle('read-media-files-in-directory', async (event, directoryPath) => {
    if (windowManager) windowManager.logToRenderer(`[IPC Handler - FS] Received read-media-files-in-directory request for: ${directoryPath}`);
    const mediaExtensions = ['.mp3', '.wav', '.aac', '.m4a', '.mp4', '.mkv', '.mov', '.avi', '.webm'];
    const mediaFiles = [];

    try {
      const entries = fs.readdirSync(directoryPath);
      for (const entryName of entries) {
        const fullPath = path.join(directoryPath, entryName);
        try {
          const stats = fs.statSync(fullPath);
          if (stats.isFile()) {
            const ext = path.extname(entryName).toLowerCase();
            if (mediaExtensions.includes(ext)) {
              mediaFiles.push(fullPath);
            }
          }
        } catch (statError) {
          logger.error(`[IPC Handler - FS] Error getting stats for ${fullPath}:`, statError);
          // Optionally, log to renderer or decide if this error should halt the process
          // if (windowManager) windowManager.logToRenderer(`[IPC Handler - FS] Error stating file ${fullPath}: ${statError.message}`);
        }
      }
      if (windowManager) windowManager.logToRenderer(`[IPC Handler - FS] Found ${mediaFiles.length} media files in ${directoryPath}.`);
      return mediaFiles;
    } catch (readDirError) {
      logger.error(`[IPC Handler - FS] Error reading directory ${directoryPath}:`, readDirError);
      if (windowManager) windowManager.logToRenderer(`[IPC Handler - FS] Error reading directory ${directoryPath}: ${readDirError.message}`);
      throw readDirError;
    }
  });

  // IPC Handler for getting file details
  ipcMain.handle('get-file-details', async (event, filePath) => {
    if (windowManager) windowManager.logToRenderer(`[IPC Handler - FS] Received get-file-details request for: ${filePath}`);
    try {
      const stats = fs.statSync(filePath);
      const fileName = path.basename(filePath);
      if (stats.isFile()) {
        const fileDetails = { name: fileName, path: filePath, size: stats.size };
        if (windowManager) windowManager.logToRenderer(`[IPC Handler - FS] File details for ${filePath}: ${JSON.stringify(fileDetails)}`);
        return fileDetails;
      } else {
        if (windowManager) windowManager.logToRenderer(`[IPC Handler - FS] Path is not a file: ${filePath}`);
        throw new Error('Path is not a file.');
      }
    } catch (error) {
      logger.error(`[IPC Handler - FS] Error getting file details for ${filePath}:`, error);
      if (windowManager) windowManager.logToRenderer(`[IPC Handler - FS] Error getting file details for ${filePath}: ${error.message}`);
      throw error;
    }
  });

  // IPC Handler for checking if a file exists
  ipcMain.handle('check-file-exists', async (event, filePath) => {
    if (windowManager) windowManager.logToRenderer(`[IPC Handler - FS] Checking file existence: ${filePath}`);
    try {
      const exists = fs.existsSync(filePath);
      if (windowManager) windowManager.logToRenderer(`[IPC Handler - FS] File ${filePath} exists: ${exists}`);
      return exists;
    } catch (error) {
      logger.error(`[IPC Handler - FS] Error checking file existence for ${filePath}:`, error);
      if (windowManager) windowManager.logToRenderer(`[IPC Handler - FS] Error checking file existence for ${filePath}: ${error.message}`);
      return false; // 如果检查失败，假设文件不存在
    }
  });

  // IPC Handler for selecting folder and scanning media files
  ipcMain.handle('dialog:selectFolder', async (event) => {
    const mainWindow = windowManager.getMainWindow();
    if (!mainWindow) {
      logger.error('[IPC Handler - FS] Main window not available for folder dialog.');
      return { cancelled: true, error: 'Main window not found' };
    }

    try {
      const result = await dialog.showOpenDialog(mainWindow, {
        properties: ['openDirectory'],
        title: '选择包含音视频文件的文件夹'
      });

      if (result.canceled || !result.filePaths || result.filePaths.length === 0) {
        if (windowManager) windowManager.logToRenderer('[IPC Handler - FS] Folder selection cancelled.');
        return { cancelled: true };
      }

      const folderPath = result.filePaths[0];
      if (windowManager) windowManager.logToRenderer(`[IPC Handler - FS] Folder selected: ${folderPath}`);

      // 扫描文件夹中的音视频文件
      const mediaExtensions = ['.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm', '.m4v', '.mp3', '.wav', '.flac', '.aac', '.ogg', '.wma', '.m4a'];
      const mediaFiles = [];

      try {
        const entries = fs.readdirSync(folderPath);
        for (const entryName of entries) {
          const fullPath = path.join(folderPath, entryName);
          try {
            const stats = fs.statSync(fullPath);
            if (stats.isFile()) {
              const ext = path.extname(entryName).toLowerCase();
              if (mediaExtensions.includes(ext)) {
                mediaFiles.push(entryName); // 只返回文件名，不是完整路径
              }
            }
          } catch (statError) {
            logger.error(`[IPC Handler - FS] Error getting stats for ${fullPath}:`, statError);
          }
        }

        if (windowManager) windowManager.logToRenderer(`[IPC Handler - FS] Found ${mediaFiles.length} media files in ${folderPath}.`);

        return {
          cancelled: false,
          folderPath: folderPath,
          mediaFiles: mediaFiles
        };
      } catch (readDirError) {
        logger.error(`[IPC Handler - FS] Error reading directory ${folderPath}:`, readDirError);
        if (windowManager) windowManager.logToRenderer(`[IPC Handler - FS] Error reading directory ${folderPath}: ${readDirError.message}`);
        return { cancelled: true, error: `Error reading directory: ${readDirError.message}` };
      }
    } catch (dialogError) {
      logger.error('[IPC Handler - FS] Error showing folder dialog:', dialogError);
      if (windowManager) windowManager.logToRenderer(`[IPC Handler - FS] Error in folder dialog: ${dialogError.message}`);
      return { cancelled: true, error: `Dialog error: ${dialogError.message}` };
    }
  });
}

module.exports = {
  initializeFilesystemIpcHandlers,
};