const { getMainLogger } = require('../logging/main-logger.js');
const logger = getMainLogger();

const { ipcMain } = require('electron');
const fs = require('node:fs').promises;
const path = require('node:path');
const { app } = require('electron');

// 存储相关的常量
const STORAGE_DIR = 'subtitler-storage';
const MAX_STORAGE_SIZE = 100 * 1024 * 1024; // 100MB 限制

let windowManager;

/**
 * 存储管理器类
 */
class StorageManager {
  constructor() {
    this.storageDir = null;
    this.isInitialized = false;
    this.cache = new Map();
  }

  /**
   * 初始化存储管理器
   */
  async initialize() {
    try {
      const userDataPath = app.getPath('userData');
      this.storageDir = path.join(userDataPath, STORAGE_DIR);
      
      // 确保存储目录存在
      await fs.mkdir(this.storageDir, { recursive: true });
      
      this.isInitialized = true;
      logger.info(`[Storage] 存储管理器初始化完成: ${this.storageDir}`);
    } catch (error) {
      logger.error('[Storage] 存储管理器初始化失败:', error);
      throw error;
    }
  }

  /**
   * 获取存储文件路径
   */
  getStoragePath(key) {
    if (!this.isInitialized) {
      throw new Error('存储管理器未初始化');
    }
    
    // 清理key，确保文件名安全
    const safeKey = key.replace(/[^a-zA-Z0-9._-]/g, '_');
    return path.join(this.storageDir, `${safeKey}.json`);
  }

  /**
   * 设置存储项
   */
  async setItem(key, value) {
    try {
      const filePath = this.getStoragePath(key);
      const data = {
        value,
        timestamp: Date.now(),
        version: '1.0.0'
      };
      
      const jsonData = JSON.stringify(data, null, 2);
      await fs.writeFile(filePath, jsonData, 'utf8');
      
      // 更新缓存
      this.cache.set(key, data);
      
      logger.info(`[Storage] 已保存: ${key}`);
      return true;
    } catch (error) {
      logger.error(`[Storage] 保存失败 [${key}]:`, error);
      return false;
    }
  }

  /**
   * 获取存储项
   */
  async getItem(key) {
    try {
      // 先检查缓存
      if (this.cache.has(key)) {
        const cachedData = this.cache.get(key);
        return cachedData.value;
      }

      const filePath = this.getStoragePath(key);
      
      // 检查文件是否存在
      try {
        await fs.access(filePath);
      } catch {
        return null; // 文件不存在
      }
      
      const jsonData = await fs.readFile(filePath, 'utf8');
      const data = JSON.parse(jsonData);
      
      // 更新缓存
      this.cache.set(key, data);
      
      logger.info(`[Storage] 已读取: ${key}`);
      return data.value;
    } catch (error) {
      logger.error(`[Storage] 读取失败 [${key}]:`, error);
      return null;
    }
  }

  /**
   * 移除存储项
   */
  async removeItem(key) {
    try {
      const filePath = this.getStoragePath(key);
      
      try {
        await fs.unlink(filePath);
      } catch (error) {
        if (error.code !== 'ENOENT') {
          throw error;
        }
      }
      
      // 从缓存中移除
      this.cache.delete(key);
      
      logger.info(`[Storage] 已删除: ${key}`);
      return true;
    } catch (error) {
      logger.error(`[Storage] 删除失败 [${key}]:`, error);
      return false;
    }
  }

  /**
   * 列出所有存储项
   */
  async listItems() {
    try {
      const files = await fs.readdir(this.storageDir);
      const items = [];
      
      for (const file of files) {
        if (file.endsWith('.json')) {
          const key = file.replace('.json', '');
          const filePath = path.join(this.storageDir, file);
          const stats = await fs.stat(filePath);
          
          items.push({
            key,
            size: stats.size,
            modified: stats.mtime.toISOString()
          });
        }
      }
      
      return items;
    } catch (error) {
      logger.error('[Storage] 列表获取失败:', error);
      return [];
    }
  }

  /**
   * 获取存储使用情况
   */
  async getStorageUsage() {
    try {
      const items = await this.listItems();
      const totalSize = items.reduce((sum, item) => sum + item.size, 0);
      
      return {
        totalSize,
        itemCount: items.length,
        maxSize: MAX_STORAGE_SIZE,
        usagePercent: (totalSize / MAX_STORAGE_SIZE) * 100,
        items
      };
    } catch (error) {
      logger.error('[Storage] 使用情况获取失败:', error);
      return {
        totalSize: 0,
        itemCount: 0,
        maxSize: MAX_STORAGE_SIZE,
        usagePercent: 0,
        items: []
      };
    }
  }

  /**
   * 清理所有存储
   */
  async clearAll() {
    try {
      const items = await this.listItems();
      
      for (const item of items) {
        await this.removeItem(item.key);
      }
      
      // 清空缓存
      this.cache.clear();
      
      logger.info('[Storage] 已清理所有存储项');
      return true;
    } catch (error) {
      logger.error('[Storage] 清理失败:', error);
      return false;
    }
  }
}

/**
 * 操作管理器类
 */
class OperationManager {
  constructor() {
    this.activeOperations = new Map();
    this.operationHistory = [];
    this.maxHistorySize = 100;
  }

  /**
   * 注册新操作
   */
  registerOperation(operationId, operationData) {
    const operation = {
      id: operationId,
      ...operationData,
      status: 'IN_PROGRESS',
      startTime: Date.now(),
      lastUpdate: Date.now()
    };
    
    this.activeOperations.set(operationId, operation);
    logger.info(`[Operation] 已注册操作: ${operationId}`);
    
    return operation;
  }

  /**
   * 更新操作状态
   */
  updateOperation(operationId, updateData) {
    const operation = this.activeOperations.get(operationId);
    if (!operation) {
      logger.warn(`[Operation] 操作不存在: ${operationId}`);
      return null;
    }

    Object.assign(operation, updateData, {
      lastUpdate: Date.now()
    });

    this.activeOperations.set(operationId, operation);
    return operation;
  }

  /**
   * 中断操作
   */
  interruptOperation(operationId) {
    const operation = this.activeOperations.get(operationId);
    if (!operation) {
      logger.warn(`[Operation] 要中断的操作不存在: ${operationId}`);
      return false;
    }

    operation.status = 'CANCELLED';
    operation.endTime = Date.now();
    operation.duration = operation.endTime - operation.startTime;

    // 移动到历史记录
    this.moveToHistory(operation);
    this.activeOperations.delete(operationId);

    logger.info(`[Operation] 已中断操作: ${operationId}`);
    return true;
  }

  /**
   * 完成操作
   */
  completeOperation(operationId, finalData = {}) {
    const operation = this.activeOperations.get(operationId);
    if (!operation) {
      logger.warn(`[Operation] 要完成的操作不存在: ${operationId}`);
      return false;
    }

    Object.assign(operation, finalData, {
      status: 'SUCCESS',
      endTime: Date.now()
    });
    operation.duration = operation.endTime - operation.startTime;

    // 移动到历史记录
    this.moveToHistory(operation);
    this.activeOperations.delete(operationId);

    logger.info(`[Operation] 已完成操作: ${operationId}`);
    return true;
  }

  /**
   * 操作失败
   */
  failOperation(operationId, error) {
    const operation = this.activeOperations.get(operationId);
    if (!operation) {
      logger.warn(`[Operation] 要标记失败的操作不存在: ${operationId}`);
      return false;
    }

            operation.status = 'ERROR';
    operation.error = error;
    operation.endTime = Date.now();
    operation.duration = operation.endTime - operation.startTime;

    // 移动到历史记录
    this.moveToHistory(operation);
    this.activeOperations.delete(operationId);

    logger.info(`[Operation] 操作失败: ${operationId}`, error);
    return true;
  }

  /**
   * 移动操作到历史记录
   */
  moveToHistory(operation) {
    this.operationHistory.unshift(operation);
    
    // 限制历史记录大小
    if (this.operationHistory.length > this.maxHistorySize) {
      this.operationHistory.splice(this.maxHistorySize);
    }
  }

  /**
   * 获取所有活跃操作
   */
  getActiveOperations() {
    return Array.from(this.activeOperations.values());
  }

  /**
   * 获取操作历史
   */
  getOperationHistory() {
    return [...this.operationHistory];
  }

  /**
   * 获取操作详情
   */
  getOperation(operationId) {
    return this.activeOperations.get(operationId) || 
           this.operationHistory.find(op => op.id === operationId);
  }

  /**
   * 清理旧的历史记录
   */
  cleanupHistory(maxAge = 24 * 60 * 60 * 1000) { // 默认24小时
    const cutoffTime = Date.now() - maxAge;
    this.operationHistory = this.operationHistory.filter(
      op => op.endTime > cutoffTime
    );
  }
}

// 创建管理器实例
const storageManager = new StorageManager();
const operationManager = new OperationManager();

/**
 * 初始化存储和操作管理的IPC处理器
 */
function initializeStorageIpcHandlers(winMgr) {
  windowManager = winMgr;

  // 初始化存储管理器
  storageManager.initialize().catch(error => {
    logger.error('[Storage] 初始化失败:', error);
  });

  // 存储相关的IPC处理器
  
  // 设置存储项
  ipcMain.handle('storage-set', async (event, key, value) => {
    try {
      const result = await storageManager.setItem(key, value);
      return { success: result };
    } catch (error) {
      logger.error('[IPC] storage-set 错误:', error);
      return { success: false, error: error.message };
    }
  });

  // 获取存储项
  ipcMain.handle('storage-get', async (event, key) => {
    try {
      const value = await storageManager.getItem(key);
      return { success: true, value };
    } catch (error) {
      logger.error('[IPC] storage-get 错误:', error);
      return { success: false, error: error.message, value: null };
    }
  });

  // 移除存储项
  ipcMain.handle('storage-remove', async (event, key) => {
    try {
      const result = await storageManager.removeItem(key);
      return { success: result };
    } catch (error) {
      logger.error('[IPC] storage-remove 错误:', error);
      return { success: false, error: error.message };
    }
  });

  // 列出存储项
  ipcMain.handle('storage-list', async (event) => {
    try {
      const items = await storageManager.listItems();
      return { success: true, items };
    } catch (error) {
      logger.error('[IPC] storage-list 错误:', error);
      return { success: false, error: error.message, items: [] };
    }
  });

  // 获取存储使用情况
  ipcMain.handle('storage-usage', async (event) => {
    try {
      const usage = await storageManager.getStorageUsage();
      return { success: true, usage };
    } catch (error) {
      logger.error('[IPC] storage-usage 错误:', error);
      return { success: false, error: error.message };
    }
  });

  // 清理所有存储
  ipcMain.handle('storage-clear-all', async (event) => {
    try {
      const result = await storageManager.clearAll();
      return { success: result };
    } catch (error) {
      logger.error('[IPC] storage-clear-all 错误:', error);
      return { success: false, error: error.message };
    }
  });

  // 操作管理相关的IPC处理器
  
  // 注册操作
  ipcMain.handle('operation-register', async (event, operationId, operationData) => {
    try {
      const operation = operationManager.registerOperation(operationId, operationData);
      return { success: true, operation };
    } catch (error) {
      logger.error('[IPC] operation-register 错误:', error);
      return { success: false, error: error.message };
    }
  });

  // 更新操作
  ipcMain.handle('operation-update', async (event, operationId, updateData) => {
    try {
      const operation = operationManager.updateOperation(operationId, updateData);
      return { success: true, operation };
    } catch (error) {
      logger.error('[IPC] operation-update 错误:', error);
      return { success: false, error: error.message };
    }
  });

  // 中断操作
  ipcMain.handle('operation-interrupt', async (event, operationId) => {
    try {
      const result = operationManager.interruptOperation(operationId);
      
      // 通知后端中断操作
      if (result && windowManager) {
        windowManager.sendToRenderer('operation-interrupted', { operationId });
      }
      
      return { success: result };
    } catch (error) {
      logger.error('[IPC] operation-interrupt 错误:', error);
      return { success: false, error: error.message };
    }
  });

  // 完成操作
  ipcMain.handle('operation-complete', async (event, operationId, finalData) => {
    try {
      const result = operationManager.completeOperation(operationId, finalData);
      return { success: result };
    } catch (error) {
      logger.error('[IPC] operation-complete 错误:', error);
      return { success: false, error: error.message };
    }
  });

  // 操作失败
  ipcMain.handle('operation-fail', async (event, operationId, error) => {
    try {
      const result = operationManager.failOperation(operationId, error);
      return { success: result };
    } catch (error) {
      logger.error('[IPC] operation-fail 错误:', error);
      return { success: false, error: error.message };
    }
  });

  // 获取活跃操作
  ipcMain.handle('operation-get-active', async (event) => {
    try {
      const operations = operationManager.getActiveOperations();
      return { success: true, operations };
    } catch (error) {
      logger.error('[IPC] operation-get-active 错误:', error);
      return { success: false, error: error.message, operations: [] };
    }
  });

  // 获取操作历史
  ipcMain.handle('operation-get-history', async (event) => {
    try {
      const history = operationManager.getOperationHistory();
      return { success: true, history };
    } catch (error) {
      logger.error('[IPC] operation-get-history 错误:', error);
      return { success: false, error: error.message, history: [] };
    }
  });

  // 获取特定操作
  ipcMain.handle('operation-get', async (event, operationId) => {
    try {
      const operation = operationManager.getOperation(operationId);
      return { success: true, operation };
    } catch (error) {
      logger.error('[IPC] operation-get 错误:', error);
      return { success: false, error: error.message, operation: null };
    }
  });

  // 清理操作历史
  ipcMain.handle('operation-cleanup-history', async (event, maxAge) => {
    try {
      operationManager.cleanupHistory(maxAge);
      return { success: true };
    } catch (error) {
      logger.error('[IPC] operation-cleanup-history 错误:', error);
      return { success: false, error: error.message };
    }
  });

  logger.info('[IPC] 存储和操作管理处理器已初始化');
}

module.exports = {
  initializeStorageIpcHandlers,
  storageManager,
  operationManager
}; 