const { getMainLogger } = require('../logging/main-logger.js');
const logger = getMainLogger();

const { ipcMain, dialog, app } = require('electron'); // Added dialog, app
const fs = require('node:fs').promises; // For async file operations
const path = require('node:path'); // For path operations
const subtitlerUtils = require('../utils/subtitler-utils');
const { DtoConverter, ErrorAggregator } = require('../services/dto-converter.js');

// Dependencies will be injected.
let grpcManager;
let windowManager;
// let utils; // appUtils will still be passed for now, but parseSrtToSegments comes from subtitlerUtils

// --- Top-level Data Extractors ---
const v2aDataExtractor = {
    directAccessKey: 'video_to_audio_response',
    extractorFunc: (v2aRespObject) => ({ audio_path: v2aRespObject.getAudioPath ? v2aRespObject.getAudioPath() : v2aRespObject.audio_path }),
    responseKey: 'video_to_audio_response',
    extract: function(responseProto) {
        logger.info('[DEBUG v2aDataExtractor] extract called with responseProto');
        logger.info('[DEBUG v2aDataExtractor] responseProto type:', typeof responseProto);
        logger.info('[DEBUG v2aDataExtractor] responseProto:', responseProto);
        logger.info('[DEBUG v2aDataExtractor] responseProto keys:', Object.keys(responseProto || {}));
        logger.info('[DEBUG v2aDataExtractor] responseProto methods:', Object.getOwnPropertyNames(responseProto).filter(name => typeof responseProto[name] === 'function'));

        // 详细检查所有字段
        logger.info('[DEBUG v2aDataExtractor] responseProto.video_to_audio_response:', responseProto.video_to_audio_response);
        logger.info('[DEBUG v2aDataExtractor] responseProto.final_result:', responseProto.final_result);
        logger.info('[DEBUG v2aDataExtractor] responseProto.data:', responseProto.data);
        logger.info('[DEBUG v2aDataExtractor] responseProto.percentage:', responseProto.percentage);
        logger.info('[DEBUG v2aDataExtractor] responseProto.status:', responseProto.status);
        
        // 尝试从各个可能的位置提取数据
        let v2aRespObject = null;
        
        // 方法1: 检查 getVideoToAudioResponse() (protobuf方法)
        if (typeof responseProto.getVideoToAudioResponse === 'function') {
            v2aRespObject = responseProto.getVideoToAudioResponse();
            logger.info('[DEBUG v2aDataExtractor] Method 1 - getVideoToAudioResponse():', v2aRespObject);
            if (v2aRespObject) {
                logger.info('[DEBUG v2aDataExtractor] Method 1 - v2aRespObject keys:', Object.keys(v2aRespObject));
                logger.info('[DEBUG v2aDataExtractor] Method 1 - v2aRespObject methods:', Object.getOwnPropertyNames(v2aRespObject).filter(name => typeof v2aRespObject[name] === 'function'));
                // 检查 getAudioPath 方法
                if (typeof v2aRespObject.getAudioPath === 'function') {
                    logger.info('[DEBUG v2aDataExtractor] Method 1 - getAudioPath():', v2aRespObject.getAudioPath());
                } else if (v2aRespObject.audio_path) {
                    logger.info('[DEBUG v2aDataExtractor] Method 1 - direct audio_path:', v2aRespObject.audio_path);
                }
            }
        } else {
            logger.info('[DEBUG v2aDataExtractor] Method 1 - getVideoToAudioResponse is not a function');
        }
        
        // 方法2: 检查 final_result 字段 (protobuf方法)
        if (!v2aRespObject && typeof responseProto.getFinalResult === 'function') {
            const finalResult = responseProto.getFinalResult();
            logger.info('[DEBUG v2aDataExtractor] Method 2 - getFinalResult():', finalResult);
            if (finalResult === 'video_to_audio_response' && typeof responseProto.getVideoToAudioResponse === 'function') {
                v2aRespObject = responseProto.getVideoToAudioResponse();
                logger.info('[DEBUG v2aDataExtractor] Method 2 - extracted v2aRespObject:', v2aRespObject);
            }
        }
        
        // 方法3: 检查 data 字段中的结构化数据 (protobuf方法)
        if (!v2aRespObject && typeof responseProto.getData === 'function') {
            const data = responseProto.getData();
            logger.info('[DEBUG v2aDataExtractor] Method 3 - getData():', data);
            if (data && data.audio_path) {
                v2aRespObject = { audio_path: data.audio_path };
                logger.info('[DEBUG v2aDataExtractor] Method 3 - created v2aRespObject from data:', v2aRespObject);
            }
        }
        
        // 方法4: 直接检查普通JavaScript对象的属性
        if (!v2aRespObject && responseProto) {
            logger.info('[DEBUG v2aDataExtractor] Method 4 - checking plain object properties');

            // 4.1: 检查 video_to_audio_response 字段（@grpc/proto-loader 会将 oneof 字段展平到顶级）
            if (responseProto.video_to_audio_response) {
                const vtar = responseProto.video_to_audio_response;
                logger.info('[DEBUG v2aDataExtractor] Method 4.1 - found video_to_audio_response:', vtar);
                if (vtar.audio_path) {
                    v2aRespObject = { audio_path: vtar.audio_path };
                    logger.info('[DEBUG v2aDataExtractor] Method 4.1 - extracted audio_path:', v2aRespObject);
                } else if (typeof vtar.getAudioPath === 'function') {
                    v2aRespObject = { audio_path: vtar.getAudioPath() };
                    logger.info('[DEBUG v2aDataExtractor] Method 4.1 - extracted audio_path via getAudioPath():', v2aRespObject);
                }
            }

            // 4.2: 直接检查data属性
            else if (responseProto.data && responseProto.data.audio_path) {
                v2aRespObject = { audio_path: responseProto.data.audio_path };
                logger.info('[DEBUG v2aDataExtractor] Method 4.2 - found audio_path in data:', v2aRespObject);
            }

            // 4.3: 直接检查audio_path属性
            else if (responseProto.audio_path) {
                v2aRespObject = { audio_path: responseProto.audio_path };
                logger.info('[DEBUG v2aDataExtractor] Method 4.3 - found direct audio_path:', v2aRespObject);
            }

            // 4.4: 检查nested结构
            else if (responseProto.finalResult && responseProto.finalResult.data && responseProto.finalResult.data.audio_path) {
                v2aRespObject = { audio_path: responseProto.finalResult.data.audio_path };
                logger.info('[DEBUG v2aDataExtractor] Method 4.4 - found audio_path in finalResult.data:', v2aRespObject);
            }

            // 4.5: 检查final_result中的video_to_audio_response
            else if (responseProto.final_result && responseProto.final_result.video_to_audio_response) {
                const vtar = responseProto.final_result.video_to_audio_response;
                if (vtar.audio_path) {
                    v2aRespObject = { audio_path: vtar.audio_path };
                    logger.info('[DEBUG v2aDataExtractor] Method 4.5 - found audio_path in final_result.video_to_audio_response:', v2aRespObject);
                }
            }

            // 4.6: 检查finalResult中的video_to_audio_response（兼容性）
            else if (responseProto.finalResult && responseProto.finalResult.video_to_audio_response) {
                const vtar = responseProto.finalResult.video_to_audio_response;
                if (vtar.audio_path) {
                    v2aRespObject = { audio_path: vtar.audio_path };
                    logger.info('[DEBUG v2aDataExtractor] Method 4.6 - found audio_path in finalResult.video_to_audio_response:', v2aRespObject);
                }
            }
        }
        
        if (v2aRespObject) {
            const result = this.extractorFunc(v2aRespObject);
            logger.info('[DEBUG v2aDataExtractor] Final result:', result);
            return result;
        }
        
        logger.info('[DEBUG v2aDataExtractor] No data extracted, returning null');
        return null;
    }
};
const a2tDataExtractor = {
    directAccessKey: 'audio_to_text_response',
    extractorFunc: (a2tRespObject) => {
        const segmentsList = a2tRespObject.getSegmentsList ? a2tRespObject.getSegmentsList() : (a2tRespObject.segments || []);
        const currentSegments = segmentsList.map(s => ({ text: s.getText ? s.getText() : s.text, start_time_ms: s.getStartTimeMs ? s.getStartTimeMs() : s.start_time_ms, end_time_ms: s.getEndTimeMs ? s.getEndTimeMs() : s.end_time_ms }));
        const transcript = a2tRespObject.getTranscript ? a2tRespObject.getTranscript() : (a2tRespObject.transcript || '');
        return { transcript: transcript, segments: currentSegments };
    },
    responseKey: 'audio_to_text_response',
    extract: function(responseProto) {
        let a2tRespObject = null;
        
        if (typeof responseProto.getAudioToTextResponse === 'function') {
            a2tRespObject = responseProto.getAudioToTextResponse();
        }
        
        if (!a2tRespObject && typeof responseProto.getFinalResult === 'function') {
            const finalResult = responseProto.getFinalResult();
            if (finalResult === 'audio_to_text_response' && typeof responseProto.getAudioToTextResponse === 'function') {
                a2tRespObject = responseProto.getAudioToTextResponse();
            }
        }
        
        if (a2tRespObject) {
            return this.extractorFunc(a2tRespObject);
        }
        
        return null;
    }
};
const genSubDataExtractor = {
    directAccessKey: 'generate_subtitles_response',
    extractorFunc: (genSubRespObject) => ({ srt_content: genSubRespObject.getSubtitleContent ? genSubRespObject.getSubtitleContent() : genSubRespObject.srt_content }),
    responseKey: 'generate_subtitles_response',
    extract: function(responseProto) {
        let genSubRespObject = null;
        
        if (typeof responseProto.getGenerateSubtitlesResponse === 'function') {
            genSubRespObject = responseProto.getGenerateSubtitlesResponse();
        }
        
        if (!genSubRespObject && typeof responseProto.getFinalResult === 'function') {
            const finalResult = responseProto.getFinalResult();
            if (finalResult === 'generate_subtitles_response' && typeof responseProto.getGenerateSubtitlesResponse === 'function') {
                genSubRespObject = responseProto.getGenerateSubtitlesResponse();
            }
        }
        
        if (genSubRespObject) {
            return this.extractorFunc(genSubRespObject);
        }
        
        return null;
    }
};
const transSubDataExtractor = {
    directAccessKey: 'translate_subtitles_response',
    extractorFunc: (transSubRespObject) => ({ translated_subtitle_content: transSubRespObject.getTranslatedContent ? transSubRespObject.getTranslatedContent() : transSubRespObject.translated_subtitle_content }),
    responseKey: 'translate_subtitles_response',
    extract: function(responseProto) {
        let transSubRespObject = null;
        
        if (typeof responseProto.getTranslateSubtitlesResponse === 'function') {
            transSubRespObject = responseProto.getTranslateSubtitlesResponse();
        }
        
        if (!transSubRespObject && typeof responseProto.getFinalResult === 'function') {
            const finalResult = responseProto.getFinalResult();
            if (finalResult === 'translate_subtitles_response' && typeof responseProto.getTranslateSubtitlesResponse === 'function') {
                transSubRespObject = responseProto.getTranslateSubtitlesResponse();
            }
        }
        
        if (transSubRespObject) {
            return this.extractorFunc(transSubRespObject);
        }
        
        return null;
    }
};

// 将操作状态枚举值映射为字符串
const mapOperationStatusToString = (status) => {
    if (status === null || status === undefined) return '未知';
    
    // 如果已经是字符串，直接返回
    if (typeof status === 'string') {
        // 如果是英文状态，转换为中文
        const statusMap = {
            'UNSPECIFIED': '未指定',
            'IN_PROGRESS': '处理中',
            'ERROR': '失败',
            'SUCCESS': '已完成',
            'PARTIAL_SUCCESS': '部分完成',
            'CANCELLED': '已取消',
            'PENDING': '等待中',
            'UNKNOWN': '未知'
        };
        return statusMap[status] || status;
    }
    
    // 枚举值映射
    switch (status) {
        case 0: return '未指定';
        case 1: return '处理中';
        case 2: return '失败';
        case 3: return '已完成';
        case 4: return '部分完成';
        case 5: return '已取消';
        case 6: return '等待中';
        default: return '未知';
    }
};

// --- Top-level gRPC Stream Step Function ---
const callGrpcStreamStep = (
  grpcManager,
  serviceName,
  methodName,
  requestBuilder,
  dataExtractor,
  sendProgressFunction,
  traceId,
  windowManager = null,
  errorAggregator = null
) => {
    return new Promise((resolve, reject) => {
        if (windowManager) windowManager.logToRenderer(`[Workflow Step] Starting ${methodName} with trace ID: ${traceId}`);

        let client, request;
        try {
            client = grpcManager.getSubtitlerClient();
            request = requestBuilder(); // Build the request
            
            // 调试信息：查看实际发送的请求
            logger.info('[DEBUG] Built request for', methodName, ':', request);
            logger.info('[DEBUG] Request keys:', Object.keys(request || {}));
            logger.info('[DEBUG] Request trace_id:', request ? request.trace_id : 'N/A');
            logger.info('[DEBUG] Request video_path:', request ? request.video_path : 'N/A');

            if (!client || !client[methodName]) {
                const errorMsg = `gRPC client or method ${methodName} not available.`;
                if (windowManager) windowManager.logToRenderer(`[Workflow Step] ${errorMsg}`);
                
                // Create stage error for aggregator
                if (errorAggregator) {
                    errorAggregator.addStageError({
                        serviceName,
                        errorCode: 'GRPC_METHOD_NOT_AVAILABLE',
                        message: errorMsg,
                        canRetry: false,
                        context: { methodName }
                    });
                    errorAggregator.markCriticalFailure();
                }
                
                return reject(new Error(errorMsg));
            }
        } catch (error) {
            const errorMsg = `Error setting up gRPC call for ${methodName}: ${error.message}`;
            if (windowManager) windowManager.logToRenderer(`[Workflow Step] ${errorMsg}`);
            
            if (errorAggregator) {
                errorAggregator.addStageError({
                    serviceName,
                    errorCode: 'GRPC_SETUP_ERROR',
                    message: errorMsg,
                    canRetry: true,
                    rawError: error,
                    context: { methodName, errorType: error.constructor.name }
                });
            }
            
            return reject(error);
        }

        const stream = client[methodName](request);
        let lastMeaningfulDataFromStream = null;

        stream.on('data', (responseProto) => {
            try {
                // Extract basic fields
                const stageName = typeof responseProto.getStageName === 'function' ? responseProto.getStageName() : methodName;
                const percentage = typeof responseProto.getPercentage === 'function' ? responseProto.getPercentage() : 0;
                const messageText = typeof responseProto.getMessage === 'function' ? responseProto.getMessage() : 'Processing...';
                const timestamp = new Date().toISOString();
                
                // Extract enhanced fields
                const traceId = typeof responseProto.getTraceId === 'function' ? responseProto.getTraceId() : null;
                const status = typeof responseProto.getStatus === 'function' ? responseProto.getStatus() : null;
                
                // Extract error information using error aggregator
                if (errorAggregator && responseProto.getErrorDetail && responseProto.getErrorDetail()) {
                    const stageError = ErrorAggregator.extractErrorFromProtoResponse(responseProto, serviceName);
                    if (stageError) {
                        errorAggregator.addStageError(stageError);
                        
                        // Check if this is a critical error
                        if (status && status === 2) { // OPERATION_STATUS_ERROR
                            errorAggregator.markCriticalFailure();
                        }
                    }
                }

                // Extract structured data if available
                let structuredData = null;
                let dataStatus = 'COMPLETE';
                
                if (typeof responseProto.getData === 'function' && responseProto.getData()) {
                    try {
                        const anyData = responseProto.getData();
                        if (anyData && typeof anyData.unpack === 'function') {
                            // Try to unpack Any type
                            const structData = anyData.unpack(
                                require('google-protobuf/google/protobuf/struct_pb').Struct
                            );
                            if (structData && typeof structData.toObject === 'function') {
                                const dataObj = structData.toObject();
                                
                                // Convert to structured data based on stage
                                if (stageName.includes('video_metadata') || stageName.includes('extract_metadata')) {
                                    structuredData = {
                                        type: 'videoMetadata',
                                        payload: {
                                            duration: dataObj.duration,
                                            resolution: dataObj.resolution,
                                            codec: dataObj.codec,
                                            error: stageError ? DtoConverter.convertStageErrorToFrontend(stageError) : undefined,
                                            isFallbackData: dataObj.is_fallback_data || false
                                        }
                                    };
                                } else if (stageName.includes('keyframe') || stageName.includes('thumbnail')) {
                                    structuredData = {
                                        type: 'keyframeResult',
                                        payload: {
                                            keyframeTimestamps: dataObj.keyframe_timestamps || [],
                                            previewImagePaths: dataObj.preview_image_paths || [],
                                            error: stageError ? DtoConverter.convertStageErrorToFrontend(stageError) : undefined,
                                            isFallbackData: dataObj.is_fallback_data || false
                                        }
                                    };
                                } else if (stageName.includes('audio')) {
                                    structuredData = {
                                        type: 'audioExtraction',
                                        payload: {
                                            audioPath: dataObj.audio_path,
                                            duration: dataObj.duration,
                                            sampleRate: dataObj.sample_rate,
                                            channels: dataObj.channels,
                                            error: stageError ? DtoConverter.convertStageErrorToFrontend(stageError) : undefined,
                                            isFallbackData: dataObj.is_fallback_data || false
                                        }
                                    };
                                }
                                
                                // Determine data status
                                if (dataObj.is_fallback_data) {
                                    dataStatus = 'PARTIAL_SUCCESS_WITH_FALLBACK';
                                } else if (stageError) {
                                    dataStatus = 'STAGE_ERROR';
                                }
                            }
                        }
                    } catch (error) {
                        logger.error(`[Stream Data] Error processing structured data: ${error.message}`);
                        if (errorAggregator) {
                            errorAggregator.addStageError({
                                serviceName: `${serviceName}_data_processing`,
                                errorCode: 'DATA_PROCESSING_ERROR',
                                message: `Failed to process structured data: ${error.message}`,
                                canRetry: false,
                                rawError: error,
                                context: { stageName }
                            });
                        }
                    }
                }

                // Extract final result if this is a successful completion message
                let finalResult = null;
                const isSuccessfulCompletion = percentage >= 100 && (status === 3 || status === 'SUCCESS' || (messageText && !messageText.toLowerCase().includes('failed')));
                
                logger.info('[DEBUG] Completion check:', {
                    percentage,
                    status,
                    messageText,
                    isSuccessfulCompletion,
                    hasDataExtractor: !!dataExtractor,
                    hasExtractMethod: dataExtractor && typeof dataExtractor.extract === 'function'
                });
                
                if (isSuccessfulCompletion && dataExtractor && typeof dataExtractor.extract === 'function') {
                    try {
                        logger.info('[DEBUG] Attempting to extract final result, percentage:', percentage);
                        logger.info('[DEBUG] dataExtractor.responseKey:', dataExtractor.responseKey);
                        const extractedData = dataExtractor.extract(responseProto);
                        logger.info('[DEBUG] Extracted data:', extractedData);
                        if (extractedData) {
                            finalResult = {
                                type: dataExtractor.resultType || 'unknown',
                                data: extractedData
                            };
                            lastMeaningfulDataFromStream = extractedData;
                            logger.info('[DEBUG] Set lastMeaningfulDataFromStream:', lastMeaningfulDataFromStream);
                        } else {
                            logger.info('[DEBUG] No data extracted from responseProto');
                        }
                    } catch (error) {
                        logger.error(`[Stream Data] Error extracting final result: ${error.message}`);
                        logger.error(`[Stream Data] Error stack: ${error.stack}`);
                    }
                }
                
                // 也尝试在非100%的进度中提取数据（如果有有用数据的话）
                if (!finalResult && dataExtractor && typeof dataExtractor.extract === 'function') {
                    try {
                        const extractedData = dataExtractor.extract(responseProto);
                        if (extractedData && extractedData.audio_path) {
                            logger.info('[DEBUG] Found valid data at percentage:', percentage, 'data:', extractedData);
                            lastMeaningfulDataFromStream = extractedData;
                        }
                    } catch (error) {
                        // 忽略非关键错误
                        logger.info('[DEBUG] Failed to extract at percentage', percentage, ':', error.message);
                    }
                }

                // Build enhanced main process progress update
                const mainProcessUpdate = {
                    taskId: traceId || 'unknown',
                    stageName,
                    progress: percentage,
                    message: messageText,
                    status: mapOperationStatusToString(status),
                    errorDetail: responseProto.getErrorDetail ? extractErrorDetail(responseProto.getErrorDetail()) : null,
                    data: responseProto.getData ? responseProto.getData() : null,
                    
                    // Enhanced error aggregation features
                    aggregatedError: errorAggregator && errorAggregator.hasErrors() ? 
                        errorAggregator.generateSummary() : undefined,
                    structuredData: structuredData,
                    dataStatus: dataStatus,
                    finalResult: finalResult,
                    
                    // Progress tracking
                    currentStage: stageName,
                    totalStages: 4 // Could be dynamic based on workflow
                };

                // Convert to frontend DTO and send
                const frontendUpdate = DtoConverter.convertProgressUpdateToFrontend(mainProcessUpdate);
                sendProgressFunction(frontendUpdate);

                // Log to renderer
                if (windowManager) {
                    const aggregatedErrorInfo = errorAggregator && errorAggregator.hasErrors() ? 
                        ` [Errors: ${errorAggregator.generateSummary().stageErrors.length}]` : '';
                    windowManager.logToRenderer(`[${timestamp}] [Progress Update] ${stageName}: ${percentage}% - ${messageText}${aggregatedErrorInfo}`);
                }
            } catch (processingError) {
                logger.error(`[Stream Data] Error processing stream data: ${processingError.message}`);
                if (errorAggregator) {
                    errorAggregator.addStageError({
                        serviceName: `${serviceName}_stream_processing`,
                        errorCode: 'STREAM_PROCESSING_ERROR',
                        message: `Stream data processing failed: ${processingError.message}`,
                        canRetry: false,
                        rawError: processingError,
                        context: { stageName: responseProto.getStageName ? responseProto.getStageName() : 'unknown' }
                    });
                }
            }
            
            // Check for critical failure and abort if necessary
            if (errorAggregator && errorAggregator.generateSummary().hasCriticalFailure) {
                stream.cancel();
                const errorSummary = errorAggregator.generateSummary();
                const errorMessage = errorSummary.overallUserMessage || `Critical failure in ${stageName}`;
                reject(new Error(errorMessage));
                return;
            }
        });

        stream.on('end', () => {
            if (windowManager) windowManager.logToRenderer(`[Workflow Step] ${methodName} stream ended.`);
            resolve(lastMeaningfulDataFromStream);
        });

        stream.on('error', (err) => {
            if (windowManager) windowManager.logToRenderer(`[Workflow Step] Error in ${methodName} stream: ${err.details || err.message}`);
            
            // Add to error aggregator
            if (errorAggregator) {
                errorAggregator.addStageError({
                    serviceName,
                    errorCode: 'GRPC_STREAM_ERROR',
                    message: err.details || err.message,
                    canRetry: true,
                    rawError: err,
                    context: { methodName, grpcCode: err.code }
                });
                errorAggregator.markCriticalFailure();
            }
            
            if (typeof sendProgressFunction === 'function') {
                const errorSummary = errorAggregator ? errorAggregator.generateSummary() : null;
                const frontendErrorUpdate = DtoConverter.convertProgressUpdateToFrontend({
                    taskId: traceId || 'unknown',
                    stageName: methodName,
                    progress: 0,
                    message: 'Stream error occurred',
                    status: 'ERROR',
                    aggregatedError: errorSummary,
                    dataStatus: 'STAGE_ERROR'
                });
                
                sendProgressFunction(frontendErrorUpdate);
            }
            reject(err);
        });
    });
};

// Helper function to extract error detail
function extractErrorDetail(errorDetailProto) {
    if (!errorDetailProto) return null;
    
    return {
        errorCode: errorDetailProto.getErrorCode ? errorDetailProto.getErrorCode() : '',
        technicalMessage: errorDetailProto.getTechnicalMessage ? errorDetailProto.getTechnicalMessage() : '',
        userMessage: errorDetailProto.getUserMessage ? errorDetailProto.getUserMessage() : '',
        context: extractContextMap(errorDetailProto)
    };
}

// Helper function to extract context map
function extractContextMap(errorDetailProto) {
    const context = {};
    if (errorDetailProto.getContextMap && typeof errorDetailProto.getContextMap === 'function') {
        const contextMap = errorDetailProto.getContextMap();
        if (contextMap && typeof contextMap.forEach === 'function') {
            contextMap.forEach((value, key) => {
                context[key] = value;
            });
        }
    }
    return context;
}

// 生成UUID作为trace_id
function generateTraceId() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
        const r = Math.random() * 16 | 0;
        const v = c === 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
    });
}

function initializeSubtitlerWorkflowIpcHandlers(gRPCMgr, winMgr, appUtils) {
  grpcManager = gRPCMgr;
  windowManager = winMgr;
  // utils = appUtils; // SRT utils are now directly imported


  // 完整工作流
  ipcMain.handle('subtitler-full-workflow', async (event, request) => {
    logger.info('[IPC Handler - Subtitler] Received subtitler-full-workflow request:', request);
    if (windowManager) windowManager.logToRenderer(`[IPC Handler - Workflow] Received full-workflow request: ${JSON.stringify(request)}`);

    const {
      file_path,
      workflow_type,
      target_language,
      request_word_timestamps,
      req_text_content,
      cache_control,
      trace_id: requestTraceId // 从请求中获取 trace_id
    } = request;

    // 使用请求中的 trace_id 或生成一个新的
    const traceId = requestTraceId || generateTraceId();
    if (windowManager) windowManager.logToRenderer(`[IPC Handler - Workflow] Using trace_id: ${traceId}`);

    // Debug logging for text_to_translated_srt workflow
    if (workflow_type === 'text_to_translated_srt') {
      if (windowManager) {
        windowManager.logToRenderer(`[IPC Handler - Workflow] text_to_translated_srt debug:`);
        windowManager.logToRenderer(`- req_text_content exists: ${!!req_text_content}`);
        windowManager.logToRenderer(`- req_text_content length: ${req_text_content ? req_text_content.length : 0}`);
        windowManager.logToRenderer(`- req_text_content preview: ${req_text_content ? req_text_content.substring(0, 100) : 'null'}`);
        windowManager.logToRenderer(`- target_language: ${target_language}`);
        windowManager.logToRenderer(`- trace_id: ${traceId}`);
      }
    }

    let activeLastMeaningfulResponseKey = null;
    let activeLastMeaningfulData = null;
    let finalSegmentsForPreview = null;
    let finalTextContentForPreview = null;
    
    const currentMainWindow = windowManager.getMainWindow();

    const sendProgress = (progressObject) => {
      const timestamp = new Date().toISOString();
      
      // 确保 progressObject 包含 traceId
      if (!progressObject.traceId && traceId) {
        progressObject.traceId = traceId;
      }
      
      // 增加详细的时间戳和进度信息
      if (windowManager) {
        windowManager.logToRenderer(
          `[${timestamp}] [Workflow Progress] ${progressObject.stageName} (${progressObject.percentage}%): ${progressObject.message} ` +
          `${progressObject.isError ? 'ERROR: ' + progressObject.errorMessage : ''} ` +
          `TraceID: ${progressObject.traceId || 'N/A'} ` +
          `Status: ${progressObject.status || 'N/A'}`
        );
      }
      
      if (currentMainWindow && currentMainWindow.webContents && !currentMainWindow.webContents.isDestroyed()) {
        logger.info(`[${timestamp}] Sending progress-update to renderer: ${progressObject.stageName} (${progressObject.percentage}%): ${progressObject.message}`);
        currentMainWindow.webContents.send('progress-update', progressObject);
      } else {
        logger.error(`[${timestamp}] Failed to send progress-update: Main window not available`);
      }
    };
    
    try {
      let audioPathForCurrentWorkflow = null;
      let textContentForCurrentWorkflow = null; 
      let srtContentForCurrentWorkflow = null;

      if (workflow_type.startsWith('vid_')) {
        if (!file_path) throw new Error(`File path is required for ${workflow_type}.`);
      } else if (workflow_type.startsWith('audio_')) {
        if (!file_path) throw new Error(`File path is required for ${workflow_type}.`);
        audioPathForCurrentWorkflow = file_path;
      } else if (workflow_type.startsWith('text_')) {
        if (windowManager) windowManager.logToRenderer(`[IPC Handler - Workflow] text_ workflow debug: req_text_content exists: ${!!req_text_content}, length: ${req_text_content ? req_text_content.length : 0}`);
        if (!req_text_content) throw new Error(`Text content (req_text_content) is required for ${workflow_type}.`);
        textContentForCurrentWorkflow = req_text_content;
        audioPathForCurrentWorkflow = file_path; // Optional audio for alignment
        if (windowManager) windowManager.logToRenderer(`[IPC Handler - Workflow] textContentForCurrentWorkflow set to: ${textContentForCurrentWorkflow ? textContentForCurrentWorkflow.substring(0, 100) + '...' : 'null'}`);
      } else {
        throw new Error(`Unknown or unhandled workflow_type: ${workflow_type}.`);
      }

      switch (workflow_type) {
        case 'vid_to_audio': {
          sendProgress({
            stageName: 'VideoToAudio',
            percentage: 0,
            message: 'Starting video to audio conversion...',
            isError: false,
            status: 'IN_PROGRESS',
            traceId: traceId
          });

          try {
            const videoToAudioResult = await callGrpcStreamStep(
              grpcManager,
              'SubtitlerService',
              'videoToAudio',
              () => ({ video_path: file_path, trace_id: traceId }),
              v2aDataExtractor,
              sendProgress,
              traceId,
              windowManager
            );

            // 调试信息：查看实际返回的结果
            logger.info('[DEBUG] videoToAudioResult:', videoToAudioResult);
            logger.info('[DEBUG] videoToAudioResult type:', typeof videoToAudioResult);
            if (videoToAudioResult) {
              logger.info('[DEBUG] videoToAudioResult keys:', Object.keys(videoToAudioResult));
              logger.info('[DEBUG] videoToAudioResult.audio_path:', videoToAudioResult.audio_path);
            }

            if (!videoToAudioResult || !videoToAudioResult.audio_path) {
              throw new Error('Failed to extract audio from video: No audio path returned.');
            }

            audioPathForCurrentWorkflow = videoToAudioResult.audio_path;
            if (windowManager) windowManager.logToRenderer(`[Workflow] VideoToAudio completed successfully. Audio path: ${audioPathForCurrentWorkflow}`);

            // Send completion progress and return result
            sendProgress({
              stageName: 'WorkflowComplete',
              percentage: 100,
              message: 'Video to audio conversion complete.',
              isError: false,
              status: 'SUCCESS',
              traceId: traceId,
              data: {
                final_result: v2aDataExtractor.responseKey,
                [v2aDataExtractor.responseKey]: videoToAudioResult
              }
            });

            // Return the result for the IPC call
            return {
              success: true,
              audio_path: audioPathForCurrentWorkflow,
              final_result: v2aDataExtractor.responseKey,
              [v2aDataExtractor.responseKey]: videoToAudioResult
            };
          } catch (error) {
            if (windowManager) windowManager.logToRenderer(`[Workflow] VideoToAudio failed: ${error.message}`);
            throw error;
          }
          break;
        }

        case 'vid_to_text':
        case 'vid_to_text_ts':
        case 'audio_to_text':
        case 'audio_to_text_ts': {
          if (workflow_type.startsWith('vid_')) {
            sendProgress('VideoToAudio', 0, 'Extracting audio from video for text conversion...');
            const v2aResponse = await callGrpcStreamStep(
              grpcManager,
              'SubtitlerService',
              'videoToAudio',
              () => ({ video_path: file_path, trace_id: traceId }),
              v2aDataExtractor,
              sendProgress,
              traceId,
              windowManager
            );
            if (v2aResponse && v2aResponse.audio_path) {
              audioPathForCurrentWorkflow = v2aResponse.audio_path;
              sendProgress('VideoToAudio', 100, `Audio extracted: ${audioPathForCurrentWorkflow}`, false, '', { [v2aDataExtractor.responseKey]: v2aResponse });
            } else {
              throw new Error('VideoToAudio step failed before text conversion.');
            }
          }
          if (!audioPathForCurrentWorkflow) throw new Error('Audio path is missing for AudioToText step.');
          
          sendProgress('AudioToText', 0, 'Converting audio to text...');
          const useWordTimestamps = request_word_timestamps || workflow_type.endsWith('_ts');
          const skipCache = cache_control?.skip_cache || false;
          const a2tParams = { 
            audio_path: audioPathForCurrentWorkflow, 
            request_word_timestamps: useWordTimestamps,
            skip_cache: skipCache,
            model: request.model || 'BIJIAN',  // 传递模型参数
            language: request.language || 'zh'  // 传递语言参数
          };
          const a2tResponse = await callGrpcStreamStep(
            grpcManager,
            'SubtitlerService',
            'audioToText',
            () => a2tParams,
            a2tDataExtractor,
            sendProgress,
            traceId,
            windowManager
          );
 
          if (a2tResponse) {
            textContentForCurrentWorkflow = a2tResponse.transcript;
            finalSegmentsForPreview = a2tResponse.segments; 
            finalTextContentForPreview = textContentForCurrentWorkflow;
            if (!textContentForCurrentWorkflow && finalSegmentsForPreview && finalSegmentsForPreview.length > 0) {
              textContentForCurrentWorkflow = finalSegmentsForPreview.map(s => s.text).join(' ');
              finalTextContentForPreview = textContentForCurrentWorkflow;
            }
            activeLastMeaningfulData = { transcript: textContentForCurrentWorkflow, segments: finalSegmentsForPreview };
            activeLastMeaningfulResponseKey = a2tDataExtractor.responseKey;
            sendProgress('AudioToText', 100, `Transcription complete.`, false, '', { [activeLastMeaningfulResponseKey]: activeLastMeaningfulData });
            sendProgress('WorkflowComplete', 100, 'Workflow complete: Audio to Text.', false, '', {
              final_result: activeLastMeaningfulResponseKey,
              [activeLastMeaningfulResponseKey]: activeLastMeaningfulData
            });

            // Return the result for the IPC call
            return {
              success: true,
              transcript: textContentForCurrentWorkflow,
              segments: finalSegmentsForPreview,
              final_result: activeLastMeaningfulResponseKey,
              [activeLastMeaningfulResponseKey]: activeLastMeaningfulData
            };
          } else {
            throw new Error('AudioToText step failed.');
          }
          break;
        }

        case 'vid_to_srt':
        case 'audio_to_srt':
        case 'text_to_srt': {
          if (workflow_type.startsWith('vid_')) {
            sendProgress('VideoToAudio', 0, 'Extracting audio from video for SRT generation...');
            const v2aResponse = await callGrpcStreamStep(sendProgress, 'videoToAudio', { video_path: file_path }, v2aDataExtractor);
            if (v2aResponse && v2aResponse.audio_path) {
              audioPathForCurrentWorkflow = v2aResponse.audio_path;
              sendProgress('VideoToAudio', 100, `Audio extracted: ${audioPathForCurrentWorkflow}`, false, '', { [v2aDataExtractor.responseKey]: v2aResponse });
            } else {
              throw new Error('VideoToAudio step failed before SRT generation.');
            }
          }
          
          if (workflow_type === 'vid_to_srt' || workflow_type === 'audio_to_srt') {
            if (!audioPathForCurrentWorkflow) throw new Error('Audio path is missing for ASR before SRT generation.');
            sendProgress('AudioToText', 0, 'Converting audio to text for SRT...');
            const useWordTimestampsForASR = request_word_timestamps || false; 
            const skipCache = cache_control?.skip_cache || false;
            const a2tParams = { 
              audio_path: audioPathForCurrentWorkflow, 
              request_word_timestamps: useWordTimestampsForASR,
              skip_cache: skipCache
            };
            const a2tResponse = await callGrpcStreamStep(sendProgress, 'audioToText', a2tParams, a2tDataExtractor);
            if (a2tResponse) {
              textContentForCurrentWorkflow = a2tResponse.transcript;
              if (!textContentForCurrentWorkflow && a2tResponse.segments && a2tResponse.segments.length > 0) {
                textContentForCurrentWorkflow = a2tResponse.segments.map(s => s.text).join(' ');
              }
              sendProgress('AudioToText', 100, 'Transcription complete for SRT generation.', false, '', { [a2tDataExtractor.responseKey]: { transcript: textContentForCurrentWorkflow, segments: a2tResponse.segments } });
            } else {
              throw new Error('AudioToText step failed before SRT generation.');
            }
          }
          if (windowManager) windowManager.logToRenderer(`[IPC Handler - Workflow] Before GenerateSubtitles check: textContentForCurrentWorkflow exists: ${!!textContentForCurrentWorkflow}, length: ${textContentForCurrentWorkflow ? textContentForCurrentWorkflow.length : 0}`);
          if (!textContentForCurrentWorkflow) {
             throw new Error('Text content is missing for subtitle generation.');
          }

          sendProgress('GenerateSubtitles', 0, 'Generating SRT subtitles...');
          const skipCache = cache_control?.skip_cache || false;
          const genSubParams = { 
            text: textContentForCurrentWorkflow, 
            audio_path: audioPathForCurrentWorkflow,
            skip_cache: skipCache
          };
          const genSubResponse = await callGrpcStreamStep(sendProgress, 'generateSubtitles', genSubParams, genSubDataExtractor);
 
          if (genSubResponse && genSubResponse.srt_content) {
            srtContentForCurrentWorkflow = genSubResponse.srt_content;
            activeLastMeaningfulData = genSubResponse;
            activeLastMeaningfulResponseKey = genSubDataExtractor.responseKey;
            
            if (subtitlerUtils && typeof subtitlerUtils.parseSrtToSegments === 'function') {
                finalSegmentsForPreview = subtitlerUtils.parseSrtToSegments(srtContentForCurrentWorkflow);
            } else {
                finalSegmentsForPreview = []; // Fallback or log error
                if(windowManager) windowManager.logToRenderer('[IPC Handler - Workflow] ERROR: subtitlerUtils.parseSrtToSegments is not available for SRT preview.');
            }

            if (finalSegmentsForPreview && finalSegmentsForPreview.length > 0) {
              finalTextContentForPreview = finalSegmentsForPreview.map(s => s.text).join(' ');
            } else {
              finalTextContentForPreview = textContentForCurrentWorkflow; 
              if(windowManager) windowManager.logToRenderer('[IPC Handler - Workflow] Could not parse generated SRT for preview or it was empty.');
            }
            sendProgress('GenerateSubtitles', 100, `SRT subtitles generated.`, false, '', { [activeLastMeaningfulResponseKey]: activeLastMeaningfulData });
            sendProgress('WorkflowComplete', 100, 'Workflow complete: SRT Generation.', false, '', {
              final_result: activeLastMeaningfulResponseKey,
              [activeLastMeaningfulResponseKey]: activeLastMeaningfulData,
              audio_to_text_response: { transcript: finalTextContentForPreview, segments: finalSegmentsForPreview }
            });

            // Return the result for the IPC call
            return {
              success: true,
              final_result: activeLastMeaningfulResponseKey,
              [activeLastMeaningfulResponseKey]: activeLastMeaningfulData,
              audio_to_text_response: { transcript: finalTextContentForPreview, segments: finalSegmentsForPreview }
            };
          } else {
            throw new Error('GenerateSubtitles step failed.');
          }
          break;
        }

        case 'vid_to_translated_srt':
        case 'audio_to_translated_srt':
        case 'text_to_translated_srt': {
          if (workflow_type.startsWith('vid_')) {
            sendProgress('VideoToAudio', 0, 'Extracting audio for translation workflow...');
            const v2aResponse = await callGrpcStreamStep(sendProgress, 'videoToAudio', { video_path: file_path }, v2aDataExtractor);
            if (v2aResponse && v2aResponse.audio_path) {
              audioPathForCurrentWorkflow = v2aResponse.audio_path;
              sendProgress('VideoToAudio', 100, `Audio extracted: ${audioPathForCurrentWorkflow}`, false, '', { [v2aDataExtractor.responseKey]: v2aResponse });
            } else {
              throw new Error('VideoToAudio step failed in translation workflow.');
            }
          }

          if (workflow_type === 'vid_to_translated_srt' || workflow_type === 'audio_to_translated_srt') {
            if (!audioPathForCurrentWorkflow) throw new Error('Audio path missing for ASR in translation workflow.');
            sendProgress('AudioToText', 0, 'Converting audio to text for translation...');
            const useWordTimestampsForASR = request_word_timestamps || false;
            const skipCache = cache_control?.skip_cache || false;
            const a2tParams = { 
              audio_path: audioPathForCurrentWorkflow, 
              request_word_timestamps: useWordTimestampsForASR,
              skip_cache: skipCache
            };
            const a2tResponse = await callGrpcStreamStep(sendProgress, 'audioToText', a2tParams, a2tDataExtractor);
            if (a2tResponse) {
              textContentForCurrentWorkflow = a2tResponse.transcript;
              if (!textContentForCurrentWorkflow && a2tResponse.segments && a2tResponse.segments.length > 0) {
                textContentForCurrentWorkflow = a2tResponse.segments.map(s => s.text).join(' ');
              }
              finalSegmentsForPreview = a2tResponse.segments; 
              finalTextContentForPreview = textContentForCurrentWorkflow;
              if (windowManager) windowManager.logToRenderer(`[IPC Handler - Workflow] Saved original ASR segments: ${finalSegmentsForPreview ? finalSegmentsForPreview.length : 0} segments`);
              sendProgress('AudioToText', 100, 'Transcription complete for translation.', false, '', { [a2tDataExtractor.responseKey]: { transcript: textContentForCurrentWorkflow, segments: finalSegmentsForPreview } });
            } else {
              throw new Error('AudioToText step failed in translation workflow.');
            }
          }
          if (!textContentForCurrentWorkflow) {
            throw new Error('Text content missing for SRT generation in translation workflow.');
          }

          sendProgress('GenerateSubtitles', 0, 'Generating original SRT for translation...');
          const skipCache = cache_control?.skip_cache || false;
          const genSubParams = { 
            text: textContentForCurrentWorkflow, 
            audio_path: audioPathForCurrentWorkflow,
            skip_cache: skipCache
          };
          const genSubResponse = await callGrpcStreamStep(sendProgress, 'generateSubtitles', genSubParams, genSubDataExtractor);
          if (genSubResponse && genSubResponse.srt_content) {
            srtContentForCurrentWorkflow = genSubResponse.srt_content;
            if (windowManager) windowManager.logToRenderer(`[IPC Handler - Workflow] Generated SRT but keeping original ASR segments: ${finalSegmentsForPreview ? finalSegmentsForPreview.length : 0} segments`);
            sendProgress('GenerateSubtitles', 100, 'Original SRT generated for translation.', false, '', { [genSubDataExtractor.responseKey]: genSubResponse, audio_to_text_response: { transcript: textContentForCurrentWorkflow, segments: finalSegmentsForPreview } });
          } else {
            throw new Error('GenerateSubtitles step failed in translation workflow.');
          }

          if (!srtContentForCurrentWorkflow) throw new Error('Original SRT content missing for translation.');
          if (!target_language) throw new Error('Target language missing for translation.');
          
          sendProgress('TranslateSubtitles', 0, `Translating SRT to ${target_language}...`);
          const transSubParams = { 
            subtitle_content: srtContentForCurrentWorkflow, 
            target_language: target_language,
            skip_cache: skipCache
          };
          const transSubResponse = await callGrpcStreamStep(sendProgress, 'translateSubtitles', transSubParams, transSubDataExtractor);
 
          if (transSubResponse && transSubResponse.translated_subtitle_content) {
            const translatedSrt = transSubResponse.translated_subtitle_content;
            activeLastMeaningfulData = transSubResponse;
            activeLastMeaningfulResponseKey = transSubDataExtractor.responseKey;
            
            if (windowManager) {
                windowManager.logToRenderer(`[IPC Handler - Workflow] Keeping original ASR segments for timing: ${finalSegmentsForPreview ? finalSegmentsForPreview.length : 0} segments`);
                windowManager.logToRenderer(`[IPC Handler - Workflow] srtContentForCurrentWorkflow: ${!!srtContentForCurrentWorkflow}`);
                windowManager.logToRenderer(`[IPC Handler - Workflow] textContentForCurrentWorkflow: ${!!textContentForCurrentWorkflow}`);
                windowManager.logToRenderer(`[IPC Handler - Workflow] finalSegmentsForPreview: ${JSON.stringify(finalSegmentsForPreview ? finalSegmentsForPreview.slice(0, 1) : null)}`);
            }
            
            sendProgress('TranslateSubtitles', 100, 'SRT translation complete.', false, '', { [activeLastMeaningfulResponseKey]: activeLastMeaningfulData });
            
            const workflowCompleteData = {
              final_result: activeLastMeaningfulResponseKey,
              [activeLastMeaningfulResponseKey]: activeLastMeaningfulData,
              generate_subtitles_response: { srt_content: srtContentForCurrentWorkflow },
              audio_to_text_response: { 
                transcript: textContentForCurrentWorkflow, 
                segments: finalSegmentsForPreview || [] 
              }
            };
            
            if (windowManager) windowManager.logToRenderer(`[IPC Handler - Workflow] Sending complete data: ${JSON.stringify({
              finalSegmentsCount: finalSegmentsForPreview ? finalSegmentsForPreview.length : 0,
              hasOriginalSRT: !!srtContentForCurrentWorkflow,
              hasTranslatedSRT: !!translatedSrt,
              hasTranscript: !!textContentForCurrentWorkflow
            })}`);
            
            sendProgress('WorkflowComplete', 100, 'Workflow complete: Translated SRT.', false, '', workflowCompleteData);
            // Add a small delay to ensure the message is likely processed by the renderer before the invoke promise resolves
            await new Promise(resolve => setTimeout(resolve, 50)); // e.g., 50ms delay

            // Return the result for the IPC call
            return {
              success: true,
              final_result: activeLastMeaningfulResponseKey,
              [activeLastMeaningfulResponseKey]: activeLastMeaningfulData,
              generate_subtitles_response: { srt_content: srtContentForCurrentWorkflow },
              audio_to_text_response: {
                transcript: textContentForCurrentWorkflow,
                segments: finalSegmentsForPreview || []
              }
            };
          } else {
            throw new Error('TranslateSubtitles step failed.');
          }
          break;
        }
        default:
          if (windowManager) windowManager.logToRenderer(`[IPC Handler - Workflow] Unknown or unhandled workflow_type in switch: ${workflow_type}`);
          throw new Error(`Unknown or unhandled workflow_type: ${workflow_type}`);
      }
    } catch (error) {
      const errorMessage = error.message || 'Unknown error in workflow.';
      const errorStack = error.stack || 'No stack trace available.';
      if (windowManager) windowManager.logToRenderer(`[IPC Handler - Workflow] Error in full-workflow (${workflow_type || 'unknown_workflow'}): ${errorMessage} \nStack: ${errorStack}`);
      
      sendProgress({
        stageName: workflow_type || 'WorkflowError',
        percentage: 100,
        message: 'Workflow failed.',
        isError: true,
        errorMessage: errorMessage,
        status: 'ERROR',
        traceId: traceId,
        errorDetail: {
                      errorCode: 'WORKFLOW_ERROR',
          technicalMessage: errorStack,
          userMessage: errorMessage,
          context: { workflow_type }
        },
        data: { final_error_details: errorMessage }
      });
      
      throw error;
    }
  });

  // IPC Handler for 'subtitler:video-to-audio'
  ipcMain.handle('subtitler:video-to-audio', async (event, filePath) => {
    logger.info(`[IPC Handler - Subtitler] Received subtitler:video-to-audio request for path: ${filePath}`);
    if (windowManager) windowManager.logToRenderer(`[IPC Handler - Subtitler] Received subtitler:video-to-audio request for path: ${filePath}`);
    
    const subtitlerClient = grpcManager.getSubtitlerClient();
    if (!subtitlerClient) {
      if (windowManager) windowManager.logToRenderer('[IPC Handler - Subtitler] SubtitlerClient not initialized for video-to-audio.');
      throw new Error('SubtitlerClient not initialized');
    }

    const currentMainWindow = windowManager.getMainWindow();
    const sendProgressToRenderer = (percentage, message, isError = false, errorMessage = '', data = null) => {
      const progress = { stageName: 'VideoToAudio', percentage, message, isError, errorMessage, ...data };
      if (windowManager) windowManager.logToRenderer(`[VideoToAudio Progress] (${percentage}%): ${message} ${isError ? 'ERROR: ' + errorMessage : ''}`);
      if (currentMainWindow && currentMainWindow.webContents && !currentMainWindow.webContents.isDestroyed()) {
        // Vue store action might not be listening to 'progress-update'.
        // It expects a promise resolution or rejection.
        // For simplicity, we'll rely on the promise result.
        // If detailed progress is needed in Vue, a separate event channel like 'subtitler:video-to-audio-progress' would be better.
        // For now, these logs are for main process visibility.
      }
    };

    try {
      sendProgressToRenderer(0, 'Starting video to audio conversion...');
      const v2aResponse = await callGrpcStreamStep(
        sendProgressToRenderer,
        'videoToAudio',
        { video_path: filePath },
        v2aDataExtractor
      );
 
      if (v2aResponse && v2aResponse.audio_path) {
        sendProgressToRenderer(100, `Audio extracted: ${v2aResponse.audio_path}`, false, '', { audio_path: v2aResponse.audio_path });
        return v2aResponse.audio_path; // Resolve the promise with the audio path
      } else {
        throw new Error('VideoToAudio step failed: Could not extract audio path from gRPC response.');
      }
    } catch (error) {
      const errorMessage = error.message || 'An unknown error occurred during video to audio conversion.';
      if (windowManager) windowManager.logToRenderer(`[IPC Handler - Subtitler] Error in video-to-audio: ${errorMessage}`);
      sendProgressToRenderer(100, 'Video to audio conversion failed.', true, errorMessage);
      throw error; // Reject the promise
    }
  });

  // IPC Handler for 'subtitler:audio-to-text'
  ipcMain.handle('subtitler:audio-to-text', async (event, audioPath, additionalParams) => {
    logger.info(`[IPC Handler - Subtitler] Received subtitler:audio-to-text request for path: ${audioPath}`, additionalParams);
    if (windowManager) windowManager.logToRenderer(`[IPC Handler - Subtitler] Received subtitler:audio-to-text request for path: ${audioPath} with params: ${JSON.stringify(additionalParams)}`);

    const subtitlerClient = grpcManager.getSubtitlerClient();
    if (!subtitlerClient) {
      if (windowManager) windowManager.logToRenderer('[IPC Handler - Subtitler] SubtitlerClient not initialized for audio-to-text.');
      throw new Error('SubtitlerClient not initialized');
    }

    const currentMainWindow = windowManager.getMainWindow();
    const sendProgress = (stageName, percentage, message, isError = false, errorMessage = '', data = null) => {
      const progress = { stageName: `AudioToText-${stageName}`, percentage, message, isError, errorMessage, ...data };
      if (windowManager) windowManager.logToRenderer(`[AudioToText Progress - ${stageName}] (${percentage}%): ${message} ${isError ? 'ERROR: ' + errorMessage : ''}`);
      if (currentMainWindow && currentMainWindow.webContents && !currentMainWindow.webContents.isDestroyed()) {
        currentMainWindow.webContents.send('progress-update', progress);
      }
    };
 
    try {
      const requestParams = {
        audio_path: audioPath,
        request_word_timestamps: additionalParams?.request_word_timestamps || true, // Default to true or use from params
        // language: additionalParams?.language || 'auto', // Example: pass language if provided
        // model_size: additionalParams?.modelSize || 'base' // Example: pass model size if provided
      };
      // Add language and model_size to request if they exist in additionalParams
      if (additionalParams?.language) {
        requestParams.language = additionalParams.language;
      }
      if (additionalParams?.modelSize) {
        requestParams.model_size = additionalParams.modelSize;
      }

      const a2tResponse = await callGrpcStreamStep(
        sendProgress,
        'audioToText',
        requestParams,
        a2tDataExtractor // Use the top-level extractor
      );
 
      if (a2tResponse) { // a2tResponse will be { transcript, segments }
        // The store expects the result directly.
        // It could be the full object or just the transcript string depending on needs.
        // For now, returning the object.
        if (windowManager) windowManager.logToRenderer(`[IPC Handler - Subtitler] Audio-to-text successful. Result: ${JSON.stringify(a2tResponse)}`);
        return a2tResponse;
      } else {
        throw new Error('AudioToText step failed: No response or empty response from gRPC call.');
      }
    } catch (error) {
      const errorMessage = error.message || 'An unknown error occurred during audio to text conversion.';
      if (windowManager) windowManager.logToRenderer(`[IPC Handler - Subtitler] Error in audio-to-text: ${errorMessage}`);
      throw error; // Reject the promise, Vue store action will catch this
    }
  });

  // IPC Handler for 'subtitler:save-edited-segments'
  ipcMain.handle('subtitler:save-edited-segments', async (event, segments) => {
    logger.info('[IPC Handler - Subtitler] Received subtitler:save-edited-segments request.');
    if (windowManager) windowManager.logToRenderer('[IPC Handler - Subtitler] Received subtitler:save-edited-segments request.');

    if (!segments || !Array.isArray(segments) || segments.length === 0) {
      if (windowManager) windowManager.logToRenderer('[IPC Handler - Subtitler] No segments provided to save.');
      throw new Error('No segments provided to save.');
    }

    const mainWindow = windowManager.getMainWindow();
    if (!mainWindow) {
      throw new Error('Main window not available to show save dialog.');
    }

    try {
      const srtContent = subtitlerUtils.segmentsToSrt(segments);
      if (!srtContent) {
        throw new Error('Failed to convert segments to SRT format.');
      }

      const dialogResult = await dialog.showSaveDialog(mainWindow, {
        title: '保存编辑后的字幕',
        defaultPath: 'edited_subtitles.srt',
        filters: [
          { name: 'SubRip Subtitle', extensions: ['srt'] },
          { name: 'All Files', extensions: ['*'] },
        ],
      });

      if (dialogResult.canceled || !dialogResult.filePath) {
        if (windowManager) windowManager.logToRenderer('[IPC Handler - Subtitler] Save dialog cancelled by user.');
        return { success: false, message: '保存操作已取消。' };
      }

      const filePathToSave = dialogResult.filePath;
      await fs.writeFile(filePathToSave, srtContent, 'utf8');

      if (windowManager) windowManager.logToRenderer(`[IPC Handler - Subtitler] Edited subtitles saved to: ${filePathToSave}`);
      return { success: true, message: `字幕已成功保存到 ${filePathToSave}`, filePath: filePathToSave };

    } catch (error) {
      const errorMessage = error.message || '保存编辑后的字幕时发生未知错误。';
      if (windowManager) windowManager.logToRenderer(`[IPC Handler - Subtitler] Error saving edited subtitles: ${errorMessage}`);
      logger.error('[IPC Handler - Subtitler] Error saving edited subtitles:', error);
      throw new Error(errorMessage);
    }
  });

  // IPC Handler for 'subtitler:export-subtitles'
  ipcMain.handle('subtitler:export-subtitles', async (event, exportPayload) => {
    const {
      segments: inputSegments, // These are the segments based on contentSource from the store
      rawContent, // This is raw text, e.g., for transcript_text source
      filename: userSpecifiedFilename,
      format,
      layout,
      contentSource,
      autoSaveToDefault,
    } = exportPayload;

    logger.info('[IPC Handler - Subtitler] Received subtitler:export-subtitles request with payload:', exportPayload);
    if (windowManager) windowManager.logToRenderer(`[IPC Handler - Subtitler] Exporting subtitles. Format: ${format}, Layout: ${layout}, Source: ${contentSource}, AutoSave: ${autoSaveToDefault}`);

    if (!format) {
      const msg = 'No export format specified.';
      if (windowManager) windowManager.logToRenderer(`[IPC Handler - Subtitler] Error: ${msg}`);
      return { error: msg };
    }

    const mainWindow = windowManager.getMainWindow();
    if (!mainWindow && !autoSaveToDefault) { // mainWindow is needed for dialog
      const msg = 'Main window not available to show save dialog.';
      if (windowManager) windowManager.logToRenderer(`[IPC Handler - Subtitler] Error: ${msg}`);
      return { error: msg };
    }
    
    try {
      let segmentsForProcessing = [];
      let fileContent = '';
      const fileExtension = format.toLowerCase();

      // 1. Determine segments to process based on contentSource
      if (contentSource === 'transcript_text' && rawContent) {
        if (format.toLowerCase() === 'txt') {
          fileContent = rawContent; // Use raw content directly for TXT
        } else {
          // For other formats, create a single segment from raw text or treat as error if not TXT
          // This part might need more sophisticated handling based on desired behavior for non-TXT from raw text.
          // For now, creating a single segment.
          segmentsForProcessing = [{ text: rawContent, startTimeMs: 0, endTimeMs: 0, id: 'raw-text-segment' }];
           if (windowManager) windowManager.logToRenderer(`[IPC Handler - Subtitler] Using rawContent for ${format} as a single segment.`);
        }
      } else if (inputSegments && Array.isArray(inputSegments)) {
        segmentsForProcessing = JSON.parse(JSON.stringify(inputSegments)); // Deep copy
      } else {
         const msg = `No valid content to export for source: ${contentSource}. Segments or rawContent missing.`;
         if (windowManager) windowManager.logToRenderer(`[IPC Handler - Subtitler] Error: ${msg}`);
         return { error: msg };
      }
      
      // 2. Apply layout (if not TXT from rawContent, or if TXT also needs layout on segments)
      // Layout is applied to 'segmentsForProcessing' which should contain the text to be laid out.
      // If fileContent is already set (e.g. TXT from rawContent), skip layout and conversion for that specific case.
      let laidOutSegments = segmentsForProcessing;
      if (fileContent === '') { // Only apply layout if fileContent isn't already set (e.g. for TXT from rawContent)
          if (layout && layout !== 'original_only' && segmentsForProcessing.length > 0) { // 'original_only' is default if no translation
            // The `translationField` in `applyLayoutToSegments` will look for `segment.translation`.
            // This field needs to be populated by a translation step if bilingual output is desired.
            // If not present, `applyLayoutToSegments` should gracefully handle it (e.g., original_top becomes original_only).
            if (windowManager) windowManager.logToRenderer(`[IPC Handler - Subtitler] Applying layout: ${layout}`);
            laidOutSegments = subtitlerUtils.applyLayoutToSegments(segmentsForProcessing, layout, 'translation'); // Assuming 'translation' field
          } else {
            if (windowManager) windowManager.logToRenderer(`[IPC Handler - Subtitler] Skipping layout or using default (original_only effectively). Layout: ${layout}`);
          }

          // 3. Convert segments to the specified format
          if (windowManager) windowManager.logToRenderer(`[IPC Handler - Subtitler] Converting to format: ${format}`);
          switch (format.toLowerCase()) {
            case 'srt':
              fileContent = subtitlerUtils.segmentsToSrt(laidOutSegments);
              break;
            case 'vtt':
              fileContent = subtitlerUtils.segmentsToVtt(laidOutSegments);
              break;
            case 'ass':
              fileContent = subtitlerUtils.segmentsToAss(laidOutSegments);
              break;
            case 'txt':
              // This case is for when 'transcript_text' was NOT the source,
              // so we convert laidOutSegments to TXT.
              fileContent = subtitlerUtils.segmentsToTxt(laidOutSegments);
              break;
            case 'json':
              fileContent = subtitlerUtils.segmentsToJson(laidOutSegments);
              break;
            default:
              throw new Error(`Unsupported export format: ${format}`);
          }
      }


      if (!fileContent && laidOutSegments.length > 0 && !(contentSource === 'transcript_text' && format.toLowerCase() === 'txt')) {
        // If there were segments to process, but fileContent is still empty (and it wasn't the special raw TXT case)
        throw new Error(`Failed to convert segments to ${format.toUpperCase()} format. Content is empty.`);
      }
      
      let filePathToSave;
      const baseFilename = userSpecifiedFilename || 'subtitles'; // Fallback filename

      if (autoSaveToDefault) {
        const downloadsPath = app.getPath('downloads');
        const defaultExportDir = path.join(downloadsPath, 'SubtitlesAppExports');
        await fs.mkdir(defaultExportDir, { recursive: true });
        filePathToSave = path.join(defaultExportDir, `${baseFilename}.${fileExtension}`);
        if (windowManager) windowManager.logToRenderer(`[IPC Handler - Subtitler] Auto-saving to: ${filePathToSave}`);
      } else {
        const dialogResult = await dialog.showSaveDialog(mainWindow, {
          title: `导出字幕为 ${format.toUpperCase()}`,
          defaultPath: `${baseFilename}.${fileExtension}`,
          filters: [
            { name: `${format.toUpperCase()} Subtitles`, extensions: [fileExtension] },
            { name: 'All Files', extensions: ['*'] },
          ],
        });

        if (dialogResult.canceled || !dialogResult.filePath) {
          if (windowManager) windowManager.logToRenderer('[IPC Handler - Subtitler] Export dialog cancelled by user.');
          return { cancelled: true };
        }
        filePathToSave = dialogResult.filePath;
      }

      await fs.writeFile(filePathToSave, fileContent, 'utf8');

      if (windowManager) windowManager.logToRenderer(`[IPC Handler - Subtitler] Subtitles exported to: ${filePathToSave}`);
      return { success: true, message: `字幕已成功导出到 ${filePathToSave}`, filePath: filePathToSave };

    } catch (error) {
      const errorMessage = error.message || `导出字幕为 ${format.toUpperCase()} 时发生未知错误。`;
      if (windowManager) windowManager.logToRenderer(`[IPC Handler - Subtitler] Error exporting subtitles: ${errorMessage} \n ${error.stack}`);
      logger.error('[IPC Handler - Subtitler] Error exporting subtitles:', error);
      return { error: errorMessage };
    }
  });


  // IPC Handler for 'subtitler:one-click-workflow'
  ipcMain.handle('subtitler:one-click-workflow', async (event, payload) => {
    const {
      filePath,
      workflowType,
      targetLanguage, // For translation
      exportFormat,
      exportLayout,
      modelSize // Optional: for ASR model selection
    } = payload;

    logger.info(`[IPC Handler - Subtitler] Received subtitler:one-click-workflow. Payload:`, payload);
    if (windowManager) windowManager.logToRenderer(`[IPC Handler - Subtitler] One-Click Workflow started. File: ${filePath}, Workflow: ${workflowType}, Format: ${exportFormat}, Layout: ${exportLayout}, TargetLang: ${targetLanguage}`);

    const subtitlerClient = grpcManager.getSubtitlerClient();
    if (!subtitlerClient) {
      if (windowManager) windowManager.logToRenderer('[IPC Handler - Subtitler] SubtitlerClient not initialized for one-click workflow.');
      return { error: 'SubtitlerClient not initialized' };
    }
    
    const currentMainWindow = windowManager.getMainWindow();
    const sendProgress = (stage, percentage, message, isError = false, data = null) => {
        const progressUpdate = { stageName: `OneClick-${stage}`, percentage, message, isError, ...data };
        if (windowManager) windowManager.logToRenderer(`[OneClick Progress] ${stage} (${percentage}%): ${message} ${isError ? `Error: ${data?.errorDetails || ''}` : ''}`);
        if (currentMainWindow && currentMainWindow.webContents && !currentMainWindow.webContents.isDestroyed()) {
             currentMainWindow.webContents.send('progress-update', progressUpdate);
        }
    };

    try {
      let audioFilePath = filePath;
      let originalSegments = null; // Segments from ASR
      let translatedSegments = null; // Segments after translation
      let finalSegmentsToExport = null;

      // Step 1: Video to Audio (if workflowType indicates video input)
      if (workflowType.startsWith('vid_')) {
        sendProgress('VideoToAudio', 0, 'Converting video to audio...');
        const v2aResponse = await callGrpcStreamStep(sendProgress, 'videoToAudio', { video_path: filePath }, v2aDataExtractor);
        if (v2aResponse && v2aResponse.audio_path) {
          audioFilePath = v2aResponse.audio_path;
          sendProgress('VideoToAudio', 100, `Audio extracted: ${audioFilePath}`);
        } else {
          throw new Error('One-Click: Video to audio conversion failed.');
        }
      } else {
        sendProgress('VideoToAudio', 100, 'Skipping video to audio (assuming audio input or text input workflow).');
      }

      // Step 2: Audio to Text (ASR)
      // This step is always needed if the workflow isn't purely text-based for translation/formatting
      if (!workflowType.startsWith('text_to_')) { // Assuming text_to_srt or text_to_translated_srt might not need ASR if text is provided
        sendProgress('AudioToText', 0, 'Transcribing audio to text...');
        const a2tParams = {
          audio_path: audioFilePath,
          request_word_timestamps: true, // Always request segments for flexibility
          language: payload.language || 'auto', // ASR language, could be different from targetLanguage for translation
          model_size: modelSize || 'base'
        };
        const a2tResponse = await callGrpcStreamStep(sendProgress, 'audioToText', a2tParams, a2tDataExtractor);
        if (a2tResponse && a2tResponse.segments && a2tResponse.segments.length > 0) {
          originalSegments = a2tResponse.segments.map((s, i) => ({ ...s, id: `orig-${i}`})); // Ensure IDs
          sendProgress('AudioToText', 100, `Transcription complete. Segments: ${originalSegments.length}`);
        } else {
          throw new Error('One-Click: Audio to text transcription failed or returned no segments.');
        }
      } else {
         // Handle text_to_... workflows if text is directly provided in payload (not part of this task's scope yet)
         // For now, assume originalSegments would be derived from input text if that path was taken.
         // This task focuses on file-based input primarily.
         sendProgress('AudioToText', 100, 'Skipping ASR (assuming text input workflow or no ASR needed).');
         // If workflowType is e.g. text_to_translated_srt, originalSegments would need to be populated from input text.
         // This part is simplified for now.
         if (!originalSegments) originalSegments = []; // Placeholder
      }
      
      // Ensure originalSegments are in the { text, startTimeMs, endTimeMs, id } format
      // The a2tResponse.segments should already be in a good format, but let's ensure consistency.
      // The alignAndMergeSegments function expects startTimeMs and endTimeMs.
      if (originalSegments) {
        originalSegments = originalSegments.map(s => ({
          id: s.id || `orig-${Date.now()}-${Math.random()}`, // Ensure ID
          text: s.text || '',
          startTimeMs: s.start_time_ms !== undefined ? s.start_time_ms : s.startTimeMs,
          endTimeMs: s.end_time_ms !== undefined ? s.end_time_ms : s.endTimeMs,
        }));
      }

      // Step 3: Translation (if workflowType indicates translation)
      if (workflowType.includes('_trans') || workflowType.includes('_translated_')) {
        if (!originalSegments || originalSegments.length === 0) {
          throw new Error('One-Click: Cannot translate, no original segments available.');
        }
        if (!targetLanguage) {
          throw new Error('One-Click: Target language for translation is not specified.');
        }

        sendProgress('TranslateSubtitles', 0, `Translating to ${targetLanguage}...`);
        const srtContentForTranslation = subtitlerUtils.segmentsToSrt(originalSegments);
        if (!srtContentForTranslation) {
          throw new Error('One-Click: Failed to convert original segments to SRT for translation.');
        }

        const transSubParams = { subtitle_content: srtContentForTranslation, target_language: targetLanguage };
        const transSubResponse = await callGrpcStreamStep(sendProgress, 'translateSubtitles', transSubParams, transSubDataExtractor);

        if (transSubResponse && transSubResponse.translated_subtitle_content) {
          const rawTranslatedSegments = subtitlerUtils.parseSrtToSegments(transSubResponse.translated_subtitle_content);
          if (!rawTranslatedSegments || rawTranslatedSegments.length === 0) {
            sendProgress('TranslateSubtitles', 75, 'Translation completed, but result parsing yielded no segments. Original timing will be used.', true);
            translatedSegments = []; // Set to empty array, alignment logic will handle this
          } else {
            // Ensure translated segments also use startTimeMs, endTimeMs for consistency before alignment
            translatedSegments = rawTranslatedSegments.map((s, i) => ({
              id: `trans-${i}-${Date.now()}`,
              text: s.text || '',
              startTimeMs: s.start_time_ms !== undefined ? s.start_time_ms : s.startTimeMs,
              endTimeMs: s.end_time_ms !== undefined ? s.end_time_ms : s.endTimeMs,
            }));
            sendProgress('TranslateSubtitles', 100, `Translation complete. Raw Translated Segments: ${translatedSegments.length}`);
          }
        } else {
          throw new Error('One-Click: Translation failed or returned no content.');
        }
      } else {
        sendProgress('TranslateSubtitles', 100, 'Skipping translation step.');
      }

      // Step 3.5: Align and Merge Segments
      // This step will re-segment original ASR if needed, and align translations.
      sendProgress('AlignAndMerge', 0, 'Aligning and merging segments...');
      // originalSegments here are from ASR, potentially word-level
      // translatedSegments are from translated SRT, potentially sentence-level
      // workflowType helps decide if translation alignment is needed
      const alignedAndMergedSegments = subtitlerUtils.alignAndMergeSegments(
        originalSegments || [], // Ensure it's an array
        translatedSegments || [], // Ensure it's an array, even if translation was skipped/failed
        workflowType
      );

      if (!alignedAndMergedSegments || alignedAndMergedSegments.length === 0) {
        throw new Error('One-Click: Segment alignment and merging failed or resulted in no segments.');
      }
      sendProgress('AlignAndMerge', 100, `Segments aligned and merged. Count: ${alignedAndMergedSegments.length}`);
      
      // finalSegmentsToExport will be the result of alignment and merging,
      // which now contains `text` (original) and `translatedText` (if applicable) fields.
      finalSegmentsToExport = alignedAndMergedSegments;

      // Step 3.6: Apply Layout
      // applyLayoutToSegments will now use the `text` and `translatedText` fields from `finalSegmentsToExport`
      sendProgress('Layouting', 0, `Applying layout: ${exportLayout}`);
      // The `applyLayoutToSegments` function expects `segments` (with `text` and `translatedText`), `layout`,
      // and optionally the names of the original and translated text fields.
      // Our `alignAndMergeSegments` produces `text` and `translatedText`.
      const laidOutSegments = subtitlerUtils.applyLayoutToSegments(
        finalSegmentsToExport,
        exportLayout,
        'text', // Field name for original text in finalSegmentsToExport
        'translatedText' // Field name for translated text in finalSegmentsToExport
      );
      sendProgress('Layouting', 100, `Layout applied. Segments for export: ${laidOutSegments.length}`);
      finalSegmentsToExport = laidOutSegments; // These segments now have a single 'text' field with the combined layout.


      if (!finalSegmentsToExport || finalSegmentsToExport.length === 0) {
        throw new Error('One-Click: No final segments available for export after layouting.');
      }

      // Step 4: Export Subtitles
      sendProgress('ExportSubtitles', 0, `Exporting subtitles as ${exportFormat}...`);
      let exportedContent = '';
      const targetFormat = exportFormat.toLowerCase();
      let targetExtension = targetFormat;

      switch (targetFormat) {
        case 'srt':
          exportedContent = subtitlerUtils.segmentsToSrt(finalSegmentsToExport);
          break;
        case 'vtt':
          exportedContent = subtitlerUtils.segmentsToVtt(finalSegmentsToExport);
          break;
        case 'ass':
          exportedContent = subtitlerUtils.segmentsToAss(finalSegmentsToExport);
          targetExtension = 'ass'; // Ensure correct extension for ASS
          break;
        case 'txt':
          exportedContent = subtitlerUtils.segmentsToTxt(finalSegmentsToExport);
          break;
        case 'json':
          exportedContent = subtitlerUtils.segmentsToJson(finalSegmentsToExport);
          break;
        default:
          throw new Error(`One-Click: Unsupported export format "${exportFormat}".`);
      }

      if (!exportedContent && finalSegmentsToExport.length > 0) { // ASS might be empty if styling fails, but other text formats should have content
         if (targetFormat !== 'ass') { // ASS can be complex, allow empty if util returns that
            throw new Error(`One-Click: Failed to convert segments to ${targetFormat.toUpperCase()}.`);
         }
      }
      
      const originalFileName = path.basename(filePath, path.extname(filePath));
      const defaultSaveName = `${originalFileName}_${workflowType}_${targetLanguage || 'na'}.${targetExtension}`;
      
      const dialogResult = await dialog.showSaveDialog(currentMainWindow, {
        title: `One-Click Export: Save ${targetFormat.toUpperCase()}`,
        defaultPath: defaultSaveName,
        filters: [
          { name: `${targetFormat.toUpperCase()} Subtitles`, extensions: [targetExtension] },
          { name: 'All Files', extensions: ['*'] },
        ],
      });

      if (dialogResult.canceled || !dialogResult.filePath) {
        sendProgress('ExportSubtitles', 100, 'Export cancelled by user.', false, {cancelled: true}); // Not an error, but a specific status
        return { cancelled: true, message: 'Export cancelled by user.' };
      }

      const exportPath = dialogResult.filePath;
      await fs.writeFile(exportPath, exportedContent, 'utf8');
      sendProgress('ExportSubtitles', 100, `Subtitles exported to: ${exportPath}`);

      return {
        success: true,
        exportPath: exportPath,
        message: 'One-Click operation completed successfully.',
        // Optionally return some of the intermediate data if useful for UI
        transcriptionResult: { segments: originalSegments },
        translationResult: translatedSegments ? { segments: translatedSegments } : null,
      };

    } catch (error) {
      const errorMessage = error.message || 'An unknown error occurred during the one-click workflow.';
      if (windowManager) windowManager.logToRenderer(`[IPC Handler - Subtitler] Error in one-click workflow: ${errorMessage} \n ${error.stack}`);
      logger.error('[IPC Handler - Subtitler] Error in one-click workflow:', error);
      sendProgress('WorkflowError', 100, 'Workflow failed', true, { errorDetails: errorMessage });
      return { error: errorMessage };
    }
  });



}

module.exports = {
  initializeSubtitlerWorkflowIpcHandlers,
  mapOperationStatusToString,
};