const { getMainLogger } = require('../logging/main-logger.js');
const logger = getMainLogger();

const { ipcMain } = require('electron');
const subtitlerUtils = require('../utils/subtitler-utils');

/** @typedef {import('../../types/dto/subtitler/main_process_subtitler_dtos').MainProcessRecognizeRequest} MainProcessRecognizeRequest */
/** @typedef {import('../../types/dto/subtitler/main_process_subtitler_dtos').MainProcessProgressUpdate} MainProcessProgressUpdate */
/** @typedef {import('../../types/dto/subtitler/main_process_subtitler_dtos').MainProcessTranscriptionResult} MainProcessTranscriptionResult */
/** @typedef {import('../../types/dto/subtitler/main_process_subtitler_dtos').MainProcessSubtitleSegment} MainProcessSubtitleSegment */
/** @typedef {import('../../types/dto/subtitler/main_process_subtitler_dtos').MainProcessTimestampedTextSegment} MainProcessTimestampedTextSegment */
/** @typedef {import('../../types/dto/common/main_process_common_dtos').MainProcessErrorDetail} MainProcessErrorDetail */
/** @typedef {import('../../types/dto/subtitler/main_process_subtitler_dtos').MainProcessSaveSubtitleRequest} MainProcessSaveSubtitleRequest */
/** @typedef {import('../../types/dto/subtitler/main_process_subtitler_dtos').MainProcessSaveSubtitleResponse} MainProcessSaveSubtitleResponse */
/** @typedef {import('../../types/dto/subtitler/main_process_subtitler_dtos').MainProcessTranslateRequest} MainProcessTranslateRequest */
/** @typedef {import('../../types/dto/subtitler/main_process_subtitler_dtos').MainProcessTranslateResponse} MainProcessTranslateResponse */
/** @typedef {import('../../types/dto/subtitler/main_process_subtitler_dtos').MainProcessTranslatedSegment} MainProcessTranslatedSegment */

const {
  RecognizeRequest,
  ProgressUpdate: SubtitlerProtoProgressUpdate,
  TranscriptionResult: ProtoTranscriptionResult,
  SubtitleSegment: ProtoSubtitleSegment,
  TimestampedTextSegment: ProtoTimestampedTextSegment,
  SaveSubtitleRequest,
  SaveSubtitleResponse,
  TranslateRequest,
  TranslateResponse,
  TranslatedSegment: ProtoTranslatedSegment, // Renamed to avoid conflict if a DTO helper is named TranslatedSegment
} = require('../../gen/js/api-protos/v1/subtitler/subtitler_pb');
const { ErrorDetail: ProtoErrorDetail } = require('../../gen/js/api-protos/v1/common/common_pb');
const { Struct, Value } = require('google-protobuf/google/protobuf/struct_pb.js');


// Dependencies will be injected.
let grpcManager;
let windowManager;
// let utils; // appUtils will still be passed for now, but parseSrtToSegments comes from subtitlerUtils

// Helper function to convert Proto SubtitleSegment to DTO
/**
 * @param {ProtoSubtitleSegment} protoSegment
 * @returns {MainProcessSubtitleSegment | null}
 */
function convertProtoSubtitleSegmentToDto(protoSegment) {
  if (!protoSegment) return null;
  return {
    text: protoSegment.getText(),
    startTimeMs: protoSegment.getStartTimeMs(),
    endTimeMs: protoSegment.getEndTimeMs(),
  };
}

// Helper function to convert Proto TimestampedTextSegment to DTO
/**
 * @param {ProtoTimestampedTextSegment} protoWordSegment
 * @returns {MainProcessTimestampedTextSegment | null}
 */
function convertProtoTimestampedTextSegmentToDto(protoWordSegment) {
  if (!protoWordSegment) return null;
  return {
    text: protoWordSegment.getText(),
    startTimeMs: protoWordSegment.getStartTimeMs(),
    endTimeMs: protoWordSegment.getEndTimeMs(),
  };
}

// Helper function to convert Proto TranscriptionResult to DTO
/**
 * @param {ProtoTranscriptionResult} protoResult
 * @returns {MainProcessTranscriptionResult | null}
 */
function convertProtoTranscriptionResultToDto(protoResult) {
  if (!protoResult) return null;
  return {
    transcript: protoResult.getTranscript(),
    segments: protoResult.getSegmentsList().map(convertProtoSubtitleSegmentToDto).filter(s => s !== null),
    wordSegments: protoResult.getWordSegmentsList().map(convertProtoTimestampedTextSegmentToDto).filter(ws => ws !== null),
    language: protoResult.getLanguage(),
  };
}

// Helper function to convert Proto ErrorDetail to DTO
/**
 * @param {ProtoErrorDetail} protoError
 * @returns {MainProcessErrorDetail | null}
 */
function convertProtoErrorDetailToDto(protoError) {
  if (!protoError) return null;
  return {
    code: protoError.getCode(),
    message: protoError.getMessage(),
    details: protoError.getDetailsList(),
  };
}

/**
 * Converts a JavaScript object to a google.protobuf.Struct.
 * @param {Record<string, any> | undefined | null} obj The object to convert.
 * @returns {Struct | undefined} The converted Struct, or undefined if input is null/undefined.
 */
function convertObjectToProtoStruct(obj) {
  if (obj == null) { // Handles undefined and null
    return undefined;
  }
  const struct = new Struct();
  const fields = struct.getFieldsMap();
  for (const key in obj) {
    if (Object.hasOwnProperty.call(obj, key)) {
      const value = obj[key];
      const protoValue = new Value();
      if (typeof value === 'string') {
        protoValue.setStringValue(value);
      } else if (typeof value === 'number') {
        protoValue.setNumberValue(value);
      } else if (typeof value === 'boolean') {
        protoValue.setBoolValue(value);
      } else if (value === null) {
        protoValue.setNullValue(0); // google.protobuf.NullValue.NULL_VALUE
      } else if (Array.isArray(value)) {
        // For arrays, we'd need a recursive call to convert array elements to ListValue
        // This is a simplified version; a full implementation would handle nested arrays/objects.
        // logger.warn(`[convertObjectToProtoStruct] Array value for key '${key}' not fully supported yet.`);
        try {
            protoValue.setStringValue(JSON.stringify(value)); // Attempt to stringify
        } catch (e) {
            // logger.warn(`Could not stringify array for key ${key}, skipping`);
            continue;
        }
      } else if (typeof value === 'object') {
        const nestedStruct = convertObjectToProtoStruct(value);
        if (nestedStruct) {
          protoValue.setStructValue(nestedStruct);
        } else {
          protoValue.setNullValue(0);
        }
      } else {
        // logger.warn(`[convertObjectToProtoStruct] Unsupported value type for key '${key}': ${typeof value}, skipping`);
        continue;
      }
      fields.set(key, protoValue);
    }
  }
  return struct;
}

// Helper function to convert DTO TimestampedTextSegment to Proto
/**
 * @param {MainProcessTimestampedTextSegment} dtoSegment
 * @returns {ProtoTimestampedTextSegment}
 */
function convertDtoTimestampedTextSegmentToProto(dtoSegment) {
  const protoSegment = new ProtoTimestampedTextSegment();
  protoSegment.setText(dtoSegment.text);
  protoSegment.setStartTimeMs(dtoSegment.startTimeMs);
  protoSegment.setEndTimeMs(dtoSegment.endTimeMs);
  return protoSegment;
}

// Helper function to convert DTO SubtitleSegment (with textSegments) to Proto for TranslateRequest
/**
 * @param {MainProcessSubtitleSegment} dtoSegment
 * @returns {ProtoSubtitleSegment} // This should be the SubtitleSegment message type expected by TranslateRequest
 */
function convertDtoSubtitleSegmentToProto(dtoSegment) {
  // IMPORTANT: Assumes ProtoSubtitleSegment imported (line 20-26) is the correct one for TranslateRequest,
  // meaning it has a field for 'text_segments' and a method like 'setTextSegmentsList'.
  // If subtitler.proto defines two different SubtitleSegment messages (one for Recognize, one for Translate),
  // ensure the correct one is used/imported here.
  const protoSegment = new ProtoSubtitleSegment();

  protoSegment.setStartTimeMs(dtoSegment.startTimeMs);
  protoSegment.setEndTimeMs(dtoSegment.endTimeMs);

  if (dtoSegment.textSegments && dtoSegment.textSegments.length > 0) {
    const protoTextSegments = dtoSegment.textSegments.map(convertDtoTimestampedTextSegmentToProto);
    // Assuming the method is setTextSegmentsList based on typical protobuf JS generator patterns
    // for a field `repeated TimestampedTextSegment text_segments`.
    if (typeof protoSegment.setTextSegmentsList === 'function') {
      protoSegment.setTextSegmentsList(protoTextSegments);
    } else {
      // This is a critical warning. The ansumption about ProtoSubtitleSegment might be wrong.
      logger.warn("[convertDtoSubtitleSegmentToProto] CRITICAL: ProtoSubtitleSegment may not be the correct type for TranslateRequest or lacks 'setTextSegmentsList'. Translation may fail or be incorrect.");
      // Attempt to set original_text_deprecated as a fallback if text is available and textSegments can't be set.
      if (dtoSegment.text && typeof protoSegment.setOriginalTextDeprecated === 'function') {
        protoSegment.setOriginalTextDeprecated(dtoSegment.text);
      }
    }
  } else if (dtoSegment.text) { // Fallback if only simple text is provided in DTO
    // This path assumes that if textSegments are not provided, the 'text' field should be converted
    // into a single text_segment. This aligns with the DTO structure where 'text' is optional
    // if 'textSegments' are present.
    const singleTextSegment = new ProtoTimestampedTextSegment();
    singleTextSegment.setText(dtoSegment.text);
    singleTextSegment.setStartTimeMs(dtoSegment.startTimeMs);
    singleTextSegment.setEndTimeMs(dtoSegment.endTimeMs);
    if (typeof protoSegment.setTextSegmentsList === 'function') {
      protoSegment.setTextSegmentsList([singleTextSegment]);
    } else if (typeof protoSegment.setOriginalTextDeprecated === 'function') {
      // Fallback to deprecated field if setTextSegmentsList is not available
      protoSegment.setOriginalTextDeprecated(dtoSegment.text);
      logger.warn("[convertDtoSubtitleSegmentToProto] Used deprecated 'original_text_deprecated' as setTextSegmentsList was not available.");
    }
  }
  return protoSegment;
}

// Helper function to convert Proto TranslatedSegment to DTO
/**
 * @param {ProtoTranslatedSegment} protoSegment
 * @returns {MainProcessTranslatedSegment | null}
 */
function convertProtoTranslatedSegmentToDto(protoSegment) {
  if (!protoSegment) return null;
  return {
    startTimeMs: protoSegment.getStartTimeMs(),
    endTimeMs: protoSegment.getEndTimeMs(),
    translatedText: protoSegment.getTranslatedText(),
  };
}


function initializeSubtitlerIoIpcHandlers(gRPCMgr, winMgr, appUtils) {
  grpcManager = gRPCMgr;
  windowManager = winMgr;
  // utils = appUtils; // SRT utils are now directly imported

  // Helper for individual subtitler methods (videoToAudio, audioToText, etc.)
  // This existing handler might still be used by other methods.
  // The 'audioToText' specific part within it will no longer be hit by the refactored IPC call.
  async function handleSubtitlerStreamMethod(methodName, event, request) {
    try {
      const subtitlerClient = grpcManager.getSubtitlerClient();
      if (!subtitlerClient) {
        throw new Error('SubtitlerClient not initialized');
      }
      
      return new Promise((resolve, reject) => {
        const stream = subtitlerClient[methodName](request);
        const results = [];
        stream.on('data', (response) => {
          let normalizedResponse;
          if (typeof response.getStageName === 'function') { // Protobuf object
            normalizedResponse = {
              stageName: response.getStageName(),
              percentage: response.getPercentage(),
              message: response.getMessage(),
              isError: response.getIsError(),
              errorMessage: response.getErrorMessage()
            };
            // Dynamically add specific response part
            const responseMethodName = `get${methodName.charAt(0).toUpperCase() + methodName.slice(1)}Response`; // e.g., getVideoToAudioResponse
            if (typeof response[responseMethodName] === 'function') {
              const specificResponseProto = response[responseMethodName]();
              if (specificResponseProto) {
                if (methodName === 'videoToAudio' && typeof specificResponseProto.getAudioPath === 'function') {
                    normalizedResponse[methodName + 'Response'] = { audioPath: specificResponseProto.getAudioPath() };
                } else if (methodName === 'audioToText' && typeof specificResponseProto.getSegmentsList === 'function') {
                    // This block is for the old audioToText, will not be hit by new recognize-audio flow
                    const segmentsList = specificResponseProto.getSegmentsList();
                    const segments = segmentsList.map(segmentProto => ({
                        text: segmentProto.getText(),
                        start_time_ms: segmentProto.getStartTimeMs(),
                        end_time_ms: segmentProto.getEndTimeMs()
                    }));
                    let transcript = typeof specificResponseProto.getTranscript === 'function' ? specificResponseProto.getTranscript() : '';
                    if (!transcript && segments.length > 0) transcript = segments.map(s => s.text).join(' ');
                    normalizedResponse[methodName + 'Response'] = { transcript: transcript, segments: segments };
                } else if (methodName === 'generateSubtitles' && typeof specificResponseProto.getSubtitleContent === 'function') {
                    normalizedResponse[methodName + 'Response'] = { subtitleContent: specificResponseProto.getSubtitleContent() };
                } else if (methodName === 'translateSubtitles' && typeof specificResponseProto.getTranslatedContent === 'function') {
                    normalizedResponse[methodName + 'Response'] = { translatedContent: specificResponseProto.getTranslatedContent() };
                }
              }
            }
          } else { // Plain JS object (e.g., from mock client)
            normalizedResponse = { ...response };
          }
          results.push(normalizedResponse);
          if (windowManager && windowManager.getMainWindow() && windowManager.getMainWindow().webContents) {
            windowManager.getMainWindow().webContents.send('progress-update', normalizedResponse);
          }
        });
        stream.on('end', () => resolve(results));
        stream.on('error', (err) => {
          logger.error(`[IPC Handler - SubtitlerIO] Error in ${methodName} stream:`, err);
          const errorResponse = {
            stageName: methodName, percentage: 0, message: 'Processing failed',
            isError: true, errorMessage: err.message || 'Unknown error'
          };
          if (windowManager && windowManager.getMainWindow() && windowManager.getMainWindow().webContents) {
            windowManager.getMainWindow().webContents.send('progress-update', errorResponse);
          }
          reject(err);
        });
      });
    } catch (error) {
      logger.error(`[IPC Handler - SubtitlerIO] Error in ${methodName}:`, error);
      throw error;
    }
  }

  async function handleRecognizeAudioStream(event, /** @type {MainProcessRecognizeRequest} */ requestDto) {
    const methodName = 'recognizeAudioStream'; // Actual gRPC method
    try {
      const subtitlerClient = grpcManager.getSubtitlerClient();
      if (!subtitlerClient) {
        throw new Error('SubtitlerClient not initialized');
      }

      const grpcRequest = new RecognizeRequest();
      grpcRequest.setTraceId(requestDto.taskId || '');
      grpcRequest.setFilePath(requestDto.fileIdentifier);
      if (requestDto.language) {
        grpcRequest.setLanguage(requestDto.language);
      }
      grpcRequest.setRequestWordTimestamps(requestDto.requestWordTimestamps || false);
      
      // TODO: Map modelConfig if present in DTO and Proto
      // if (requestDto.modelConfig) {
      //   const protoModelConfig = new subtitler_pb.ModelConfig(); // Assuming ModelConfig is in subtitler_pb
      //   protoModelConfig.setModelName(requestDto.modelConfig.modelName);
      //   // ... set other modelConfig fields ...
      //   grpcRequest.setModelConfig(protoModelConfig);
      // }
      // TODO: Map outputFormat if present in DTO and Proto
      // if (requestDto.outputFormat) {
      //   grpcRequest.setOutputFormat(requestDto.outputFormat); // Assuming OutputFormat is an enum or string
      // }
      
      if (windowManager) windowManager.logToRenderer(`[IPC Handler - SubtitlerIO] Calling ${methodName} with gRPC request: ${JSON.stringify(grpcRequest.toObject())}`);

      return new Promise((resolve, reject) => {
        const stream = subtitlerClient[methodName](grpcRequest);
        
        stream.on('data', (/** @type {SubtitlerProtoProgressUpdate} */ protoProgressUpdate) => {
          /** @type {MainProcessProgressUpdate} */
          const progressUpdateDto = {
            taskId: requestDto.taskId,
            stageName: protoProgressUpdate.getStageName(),
            percentage: protoProgressUpdate.getPercentage(),
            message: protoProgressUpdate.getMessage(),
            transcriptionResult: null,
            errorDetail: null,
          };

          if (protoProgressUpdate.hasTranscriptionResult()) {
            progressUpdateDto.transcriptionResult = convertProtoTranscriptionResultToDto(protoProgressUpdate.getTranscriptionResult());
          } else if (protoProgressUpdate.hasErrorDetail()) {
            progressUpdateDto.errorDetail = convertProtoErrorDetailToDto(protoProgressUpdate.getErrorDetail());
          }
          
          if (windowManager && windowManager.getMainWindow() && windowManager.getMainWindow().webContents) {
            windowManager.getMainWindow().webContents.send('subtitler-recognize-audio-progress', progressUpdateDto);
            if (windowManager.logToRenderer) windowManager.logToRenderer(`[IPC Handler - SubtitlerIO] Sent progress update (task ${requestDto.taskId}): ${progressUpdateDto.stageName} - ${progressUpdateDto.percentage}%`);
          }
        });

        stream.on('end', () => {
          if (windowManager) windowManager.logToRenderer(`[IPC Handler - SubtitlerIO] ${methodName} stream ended for task ${requestDto.taskId}`);
          resolve({ success: true, message: `${methodName} stream completed for task ${requestDto.taskId}.` });
        });

        stream.on('error', (err) => {
          logger.error(`[IPC Handler - SubtitlerIO] Error in ${methodName} stream for task ${requestDto.taskId}:`, err);
          /** @type {MainProcessProgressUpdate} */
          const errorProgressUpdateDto = {
            taskId: requestDto.taskId,
            stageName: methodName,
            percentage: 0,
            message: 'Processing failed due to gRPC error',
            transcriptionResult: null,
            errorDetail: {
              code: String(err.code || 'UNKNOWN_GRPC_ERROR'),
              message: err.details || err.message || 'Unknown gRPC stream error',
              details: err.stack ? [err.stack] : [],
            }
          };
          if (windowManager && windowManager.getMainWindow() && windowManager.getMainWindow().webContents) {
            windowManager.getMainWindow().webContents.send('subtitler-recognize-audio-progress', errorProgressUpdateDto);
            if (windowManager.logToRenderer) windowManager.logToRenderer(`[IPC Handler - SubtitlerIO] Sent error progress update (task ${requestDto.taskId}): ${JSON.stringify(errorProgressUpdateDto.errorDetail)}`);
          }
          reject(err);
        });
      });
    } catch (error) {
      logger.error(`[IPC Handler - SubtitlerIO] Error setting up ${methodName} stream for task ${requestDto.taskId}:`, error);
      /** @type {MainProcessProgressUpdate} */
      const setupErrorDto = {
          taskId: requestDto.taskId,
          stageName: methodName + '_setup_error',
          percentage: 0,
          message: 'Failed to initiate audio recognition stream.',
          transcriptionResult: null,
          errorDetail: {
              code: 'SETUP_FAILURE',
              message: error.message || 'Unknown setup error',
              details: error.stack ? [error.stack] : [],
          }
      };
      if (windowManager && windowManager.getMainWindow() && windowManager.getMainWindow().webContents) {
          windowManager.getMainWindow().webContents.send('subtitler-recognize-audio-progress', setupErrorDto);
      }
      throw error;
    }
  }

  async function handleTranslateSubtitle(event, /** @type {MainProcessTranslateRequest} */ requestDto) {
    const taskId = requestDto.taskId || `translate-subtitle-${Date.now()}`;
    if (windowManager) {
      // Avoid logging full segments array if it's large
      const loggableDto = {...requestDto, segments: `Count: ${requestDto.segments?.length}`};
      windowManager.logToRenderer(`[IPC Handler - SubtitlerIO][${taskId}] Received 'translate-subtitle'. DTO: ${JSON.stringify(loggableDto)}`);
    }
  
    /** @type {MainProcessTranslateResponse} */
    let responseDto = {
      taskId: taskId,
      translatedSegments: [],
      errorDetail: null,
    };
  
    try {
      const subtitlerClient = grpcManager.getSubtitlerClient();
      if (!subtitlerClient) {
        throw new Error('SubtitlerClient not initialized');
      }
  
      // C. gRPC请求准备 (MainProcessTranslateRequest -> Proto TranslateRequest)
      const grpcRequest = new TranslateRequest();
      grpcRequest.setTraceId(taskId); // Use the consistent taskId
      grpcRequest.setSourceLanguage(requestDto.sourceLanguage);
      grpcRequest.setTargetLanguage(requestDto.targetLanguage);
  
      if (requestDto.segments && requestDto.segments.length > 0) {
        const protoSegments = requestDto.segments.map(convertDtoSubtitleSegmentToProto);
        grpcRequest.setSegmentsList(protoSegments);
      }
      
      // Optional: model_config and other fields if they exist in DTO and Proto
      // if (requestDto.modelConfig) {
      //   const protoModelConfig = new ... // Convert DTO modelConfig to Proto modelConfig
      //   grpcRequest.setModelConfig(protoModelConfig);
      // }
  
      if (windowManager) windowManager.logToRenderer(`[IPC Handler - SubtitlerIO][${taskId}] Calling gRPC Translate with request: ${JSON.stringify(grpcRequest.toObject())}`);
  
      const grpcResponse = await new Promise((resolve, reject) => {
        // Assuming 'translate' is the unary gRPC method.
        // If the method is 'translateSubtitles' and it's unary, use that.
        // The original code used 'translateSubtitles' with handleSubtitlerStreamMethod,
        // which implies it might have been streaming. This refactor assumes a unary call
        // is desired for a request/response DTO pattern.
        subtitlerClient.translate(grpcRequest, {}, (error, /** @type {TranslateResponse} */ protoResponse) => {
          if (error) {
            reject(error);
          } else {
            resolve(protoResponse);
          }
        });
      });
  
      // D. gRPC响应处理 (Proto TranslateResponse -> MainProcessTranslateResponse)
      if (grpcResponse.getTranslatedSegmentsList()) {
        responseDto.translatedSegments = grpcResponse.getTranslatedSegmentsList()
          .map(convertProtoTranslatedSegmentToDto)
          .filter(s => s !== null);
      }
  
      if (grpcResponse.hasErrorDetail()) {
        responseDto.errorDetail = convertProtoErrorDetailToDto(grpcResponse.getErrorDetail());
      }
  
      if (windowManager) windowManager.logToRenderer(`[IPC Handler - SubtitlerIO][${taskId}] Translate gRPC call successful. Response DTO: ${JSON.stringify(responseDto)}`);
  
    } catch (error) {
      logger.error(`[IPC Handler - SubtitlerIO][${taskId}] Error in translate-subtitle:`, error);
      if (windowManager) windowManager.logToRenderer(`[IPC Handler - SubtitlerIO][${taskId}] Translate-subtitle processing failed: ${error.message || error}`);
      
      responseDto.errorDetail = {
        code: String(error.code || 'IPC_HANDLER_ERROR'),
        message: error.details || error.message || 'Unknown error during subtitle translation.',
        details: error.stack ? [error.stack] : [],
      };
    }
    
    return responseDto;
  }

  ipcMain.handle('subtitler-video-to-audio', (event, request) => handleSubtitlerStreamMethod('videoToAudio', event, request));
  
  ipcMain.handle('subtitler-audio-to-text', async (event, payload) => {
    /** @type {MainProcessRecognizeRequest} */
    const requestDto = {
      taskId: payload.taskId,
      fileIdentifier: payload.filePath,
      language: payload.language,
      requestWordTimestamps: payload.requestWordTimestamps || false,
      // modelConfig: payload.modelConfig, // Optional: map if provided by renderer and defined in DTO
      // outputFormat: payload.outputFormat, // Optional: map if provided by renderer and defined in DTO
    };

    if (windowManager && windowManager.logToRenderer) {
      windowManager.logToRenderer(`[IPC Handler - SubtitlerIO] Received 'subtitler-audio-to-text' for task ${requestDto.taskId}. DTO: ${JSON.stringify(requestDto)}`);
    }
    return handleRecognizeAudioStream(event, requestDto);
  });

  ipcMain.handle('subtitler-generate-subtitles', (event, request) => handleSubtitlerStreamMethod('generateSubtitles', event, request));
  
  // ipcMain.handle('subtitler-translate-subtitles', (event, request) => handleSubtitlerStreamMethod('translateSubtitles', event, request));
  ipcMain.handle('subtitler-translate-subtitles', async (event, /** @type {any} */ rawRequestPayload) => {
    /** @type {MainProcessTranslateRequest} */
    const requestDto = {
      taskId: rawRequestPayload.taskId,
      segments: rawRequestPayload.segments ? rawRequestPayload.segments.map(seg => ({
        startTimeMs: seg.startTimeMs,
        endTimeMs: seg.endTimeMs,
        text: seg.text, // Optional, if textSegments is the primary source from renderer
        textSegments: seg.textSegments ? seg.textSegments.map(ts => ({
          text: ts.text,
          startTimeMs: ts.startTimeMs,
          endTimeMs: ts.endTimeMs,
        })) : (seg.text ? [{ text: seg.text, startTimeMs: seg.startTimeMs, endTimeMs: seg.endTimeMs }] : []), // Construct textSegments if only text is given by renderer
      })) : [],
      sourceLanguage: rawRequestPayload.sourceLanguage,
      targetLanguage: rawRequestPayload.targetLanguage,
      // modelConfig: rawRequestPayload.modelConfig, // Optional
    };
    return handleTranslateSubtitle(event, requestDto);
  });

  // 保存字幕功能
  ipcMain.handle('subtitler-save-subtitle', async (event, /** @type {any} */ rawRequestPayload) => {
    const taskId = rawRequestPayload.taskId || `save-subtitle-${Date.now()}`;
    if (windowManager) {
      const payloadSummary = { ...rawRequestPayload };
      if (payloadSummary.segments) payloadSummary.segments = `Count: ${payloadSummary.segments?.length}`;
      if (payloadSummary.original_content) payloadSummary.original_content = payloadSummary.original_content.substring(0,100) + (payloadSummary.original_content.length > 100 ? '...' : '');
      if (payloadSummary.translated_content) payloadSummary.translated_content = payloadSummary.translated_content.substring(0,100) + (payloadSummary.translated_content.length > 100 ? '...' : '');
      if (payloadSummary.subtitle_content) payloadSummary.subtitle_content = payloadSummary.subtitle_content.substring(0,100) + (payloadSummary.subtitle_content.length > 100 ? '...' : '');
      windowManager.logToRenderer(`[IPC Handler - SubtitlerIO][${taskId}] Received 'subtitler-save-subtitle'. Payload (summary): ${JSON.stringify(payloadSummary)}`);
    }

    /** @type {MainProcessSaveSubtitleResponse} */
    let responseDto = {
      taskId: taskId,
      filePath: null,
      fileName: null,
      savedToDefault: false,
      errorDetail: null,
    };

    try {
      const subtitlerClient = grpcManager.getSubtitlerClient();
      if (!subtitlerClient) {
        throw new Error('SubtitlerClient not initialized');
      }

      // B. IPC请求参数处理 (渲染进程数据 -> MainProcessSaveSubtitleRequest)
      /** @type {MainProcessSaveSubtitleRequest} */
      const requestDto = {
        taskId: taskId,
        targetPath: rawRequestPayload.filePath, // filePath from renderer is targetPath for saving
        fileNamePrefix: rawRequestPayload.fileNamePrefix,
        subtitleContent: rawRequestPayload.subtitle_content, // Might be empty if segments are provided
        format: rawRequestPayload.format || 'srt',
        layout: rawRequestPayload.layout, // e.g., "仅原文", "仅译文", "原文优先", "译文优先"
        originalContent: rawRequestPayload.original_content,
        translatedContent: rawRequestPayload.translated_content,
        autoSaveToDefault: typeof rawRequestPayload.auto_save_to_default === 'boolean' ? rawRequestPayload.auto_save_to_default : true,
        segments: [], // Will be populated below
        assStyleOptions: rawRequestPayload.assStyleOptions, // Pass through, will be converted to Struct
        translationEnabled: rawRequestPayload.translationEnabled !== false, // Default to true
      };

      // Populate segments for DTO (similar logic to before, but mapping to DTO structure)
      if (rawRequestPayload.segments && Array.isArray(rawRequestPayload.segments) && rawRequestPayload.segments.length > 0) {
        if (windowManager) windowManager.logToRenderer(`[IPC Handler - SubtitlerIO][${taskId}] Using existing segments from payload: ${rawRequestPayload.segments.length} segments`);
        requestDto.segments = rawRequestPayload.segments.map((seg) => {
          const originalText = seg.original_text || seg.text || seg.originalText || "";
          let translatedText = "";
          if (requestDto.translationEnabled) {
            translatedText = seg.translated_text || seg.translatedText || seg.translation || "";
          }
          let startTimeMs = 0;
          if (seg.start_time_ms !== undefined) startTimeMs = parseInt(String(seg.start_time_ms), 10);
          else if (seg.startTimeMs !== undefined) startTimeMs = parseInt(String(seg.startTimeMs), 10);
          else if (seg.start_time !== undefined) startTimeMs = parseInt(String(seg.start_time), 10); // Assuming ms if not specified

          let endTimeMs = 0;
          if (seg.end_time_ms !== undefined) endTimeMs = parseInt(String(seg.end_time_ms), 10);
          else if (seg.endTimeMs !== undefined) endTimeMs = parseInt(String(seg.endTimeMs), 10);
          else if (seg.end_time !== undefined) endTimeMs = parseInt(String(seg.end_time), 10); // Assuming ms

          return {
            startTimeMs: startTimeMs,
            endTimeMs: endTimeMs,
            originalText: originalText,
            translatedText: translatedText,
            // Other segment fields if any in MainProcessSubtitleSegment DTO (currently not)
          };
        });
      } else if (subtitlerUtils && typeof subtitlerUtils.parseSrtToSegments === 'function' &&
                 (requestDto.originalContent || requestDto.translatedContent)) {
        if (windowManager) windowManager.logToRenderer(`[IPC Handler - SubtitlerIO][${taskId}] No segments in payload, attempting to parse from SRT content`);
        const originalSegmentsParsed = requestDto.originalContent ? subtitlerUtils.parseSrtToSegments(requestDto.originalContent) : [];
        const translatedSegmentsParsed = requestDto.translatedContent ? subtitlerUtils.parseSrtToSegments(requestDto.translatedContent) : [];
        
        const maxLength = Math.max(originalSegmentsParsed.length, translatedSegmentsParsed.length);
        for (let i = 0; i < maxLength; i++) {
          const oSeg = originalSegmentsParsed[i];
          const tSeg = translatedSegmentsParsed[i];
          requestDto.segments.push({
            startTimeMs: oSeg?.start_time_ms ?? tSeg?.start_time_ms ?? 0,
            endTimeMs: oSeg?.end_time_ms ?? tSeg?.end_time_ms ?? 0,
            originalText: oSeg?.text || "",
            translatedText: requestDto.translationEnabled && tSeg ? (tSeg.text || "") : "",
          });
        }
        if (windowManager) windowManager.logToRenderer(`[IPC Handler - SubtitlerIO][${taskId}] Generated ${requestDto.segments.length} segments from SRT parsing.`);
      }

      // C. gRPC请求准备 (MainProcessSaveSubtitleRequest -> Proto SaveSubtitleRequest)
      const grpcRequest = new SaveSubtitleRequest();
      grpcRequest.setTraceId(requestDto.taskId);
      if(requestDto.targetPath) grpcRequest.setTargetPath(requestDto.targetPath);
      if(requestDto.fileNamePrefix) grpcRequest.setFileNamePrefix(requestDto.fileNamePrefix);
      if(requestDto.subtitleContent) grpcRequest.setSubtitleContent(requestDto.subtitleContent);
      grpcRequest.setFormat(requestDto.format);
      if(requestDto.layout) grpcRequest.setLayout(requestDto.layout);
      if(requestDto.originalContent) grpcRequest.setOriginalContent(requestDto.originalContent);
      if(requestDto.translatedContent) grpcRequest.setTranslatedContent(requestDto.translatedContent);
      grpcRequest.setAutoSaveToDefault(requestDto.autoSaveToDefault);

      if (requestDto.segments) {
        const protoSegments = requestDto.segments.map(dtoSeg => {
          const protoSeg = new ProtoSubtitleSegment(); // Assuming ProtoSubtitleSegment is the correct type for SaveSubtitleRequest.segments
          protoSeg.setStartTimeMs(dtoSeg.startTimeMs);
          protoSeg.setEndTimeMs(dtoSeg.endTimeMs);
          // The SaveSubtitleRequest.Segment in proto expects 'text' and 'translated_text'
          // Let's assume ProtoSubtitleSegment has setText and setTranslatedText or similar
          // Based on subtitler.proto, SaveSubtitleRequest.Segment has original_text and translated_text
          // However, ProtoSubtitleSegment (used in RecognizeAudioStream) has text, start_time_ms, end_time_ms.
          // We need to use the correct segment type for SaveSubtitleRequest.
          // Let's assume SaveSubtitleRequest.Segment is defined in subtitler_pb and has original_text, translated_text, start_time_ms, end_time_ms
          // For now, we'll use ProtoSubtitleSegment and map originalText to text, and if translatedText exists, we might need a different structure or a combined text.
          // Re-checking subtitler.proto: SaveSubtitleRequest has repeated Segment segments.
          // message Segment { int64 start_time_ms = 1; int64 end_time_ms = 2; string original_text = 3; string translated_text = 4; }
          // So we need to create instances of this specific Segment message.
          // This means we cannot directly use ProtoSubtitleSegment from Recognize response.
          // Let's assume there's a `subtitler_pb.Segment` or similar.
          // For now, let's assume `SaveSubtitleRequest.Segment` is the correct type.
          // The generated JS code would be `new subtitler_pb.SaveSubtitleRequest.Segment()` if Segment is nested,
          // or `new subtitler_pb.Segment()` if it's a top-level message.
          // Looking at the import: `const { ..., SaveSubtitleRequest, ... } = require('...');`
          // Let's assume `subtitler_pb.Segment` is available if it's a top-level message, or we construct it appropriately.
          // Given the proto, it's likely `new proto.v1.subtitler.Segment()` or similar.
          // For simplicity, if `ProtoSubtitleSegment` is the only segment type available from imports,
          // and it only has `text`, this part needs careful adjustment based on actual generated code.
          // Let's assume `SaveSubtitleRequest` has a method `addSegments` that takes an object matching the `Segment` message structure,
          // or we create `new subtitler_pb.Segment()` (if that's its name).
          // For now, creating a plain object and hoping the gRPC library handles it, or using a specific constructor if known.
          // The `SaveSubtitleRequest` class itself should have an `addSegments` method or `setSegmentsList` that takes an array of these segment protos.
          
          // Correct approach: Create instances of the specific Segment message defined in subtitler.proto for SaveSubtitleRequest
          const segmentMessage = new SaveSubtitleRequest.Segment(); // Assuming Segment is a nested class of SaveSubtitleRequest
          segmentMessage.setStartTimeMs(dtoSeg.startTimeMs);
          segmentMessage.setEndTimeMs(dtoSeg.endTimeMs);
          segmentMessage.setOriginalText(dtoSeg.originalText || "");
          if (dtoSeg.translatedText) {
            segmentMessage.setTranslatedText(dtoSeg.translatedText);
          }
          return segmentMessage;
        });
        grpcRequest.setSegmentsList(protoSegments);
      }

      if (requestDto.assStyleOptions) {
        const assStyleOptionsProto = convertObjectToProtoStruct(requestDto.assStyleOptions);
        if (assStyleOptionsProto) {
          grpcRequest.setAssStyleOptions(assStyleOptionsProto);
        }
      }
      
      if (windowManager) windowManager.logToRenderer(`[IPC Handler - SubtitlerIO][${taskId}] Calling gRPC SaveSubtitle with request: ${JSON.stringify(grpcRequest.toObject())}`);

      const grpcResponse = await new Promise((resolve, reject) => {
        subtitlerClient.saveSubtitle(grpcRequest, {}, (error, /** @type {SaveSubtitleResponse} */ protoResponse) => {
          if (error) {
            reject(error);
          } else {
            resolve(protoResponse);
          }
        });
      });

      // D. gRPC响应处理 (Proto SaveSubtitleResponse -> MainProcessSaveSubtitleResponse)
      responseDto.filePath = grpcResponse.getFilePath();
      responseDto.fileName = grpcResponse.getFileName();
      responseDto.savedToDefault = grpcResponse.getSavedToDefault();
      if (grpcResponse.hasErrorDetail()) {
        responseDto.errorDetail = convertProtoErrorDetailToDto(grpcResponse.getErrorDetail());
      }
      if (windowManager) windowManager.logToRenderer(`[IPC Handler - SubtitlerIO][${taskId}] SaveSubtitle gRPC call successful. Response DTO: ${JSON.stringify(responseDto)}`);

    } catch (error) {
      logger.error(`[IPC Handler - SubtitlerIO][${taskId}] Error in save-subtitle:`, error);
      if (windowManager) windowManager.logToRenderer(`[IPC Handler - SubtitlerIO][${taskId}] Save-subtitle processing failed: ${error.message || error}`);
      
      responseDto.errorDetail = {
        code: String(error.code || 'IPC_HANDLER_ERROR'),
        message: error.details || error.message || 'Unknown error during save subtitle processing.',
        details: error.stack ? [error.stack] : [],
      };
    }
    
    // Send the DTO back to the renderer process
    // event.reply('subtitler-save-subtitle-response', responseDto); // If using event.reply
    return responseDto; // For ipcMain.handle
  });

  // 批量保存字幕功能
  ipcMain.handle('subtitler-batch-save-subtitle', async (event, request) => {
    if (windowManager) {
      const requestSummary = {
        ...request,
        segments: `Count: ${request.segments?.length}, First segment (if any): ${request.segments?.[0] ? JSON.stringify(request.segments[0]) : 'N/A'}`,
        original_content: request.original_content?.substring(0,100) + (request.original_content?.length > 100 ? '...' : ''),
        translated_content: request.translated_content?.substring(0,100) + (request.translated_content?.length > 100 ? '...' : '')
      };
      windowManager.logToRenderer(`[IPC Handler - SubtitlerIO] Received batch-save-subtitle request (summary): ${JSON.stringify(requestSummary)}`);
    }
    let translatedTextsArray = [];
    if (request.translated_content && typeof request.translated_content === 'string') {
      const srtBlocks = request.translated_content.trim().split(/\r?\n\r?\n/);
      srtBlocks.forEach(block => {
        const lines = block.split(/\r?\n/);
        if (lines.length > 2) {
          const textContent = lines.slice(2).join(' ').trim();
          if (textContent) translatedTextsArray.push(textContent);
        }
      });
      if (windowManager) windowManager.logToRenderer(`[IPC Handler - SubtitlerIO] Parsed ${translatedTextsArray.length} translated texts from SRT. First item: '${translatedTextsArray.length > 0 ? translatedTextsArray[0] : "N/A"}'`);
    } else if (windowManager) {
      windowManager.logToRenderer(`[IPC Handler - SubtitlerIO] No translated_content found in request, or it's not a string. translated_text will be empty for all segments.`);
    }
    
    if (request.segments && Array.isArray(request.segments)) {
      if (windowManager) windowManager.logToRenderer(`[IPC Handler - SubtitlerIO] Converting ${request.segments.length} segments to protobuf format`);
      request.segments = request.segments.map((seg, index) => {
        const originalText = seg.original_text || seg.text || "";
        const translatedText = (index < translatedTextsArray.length) ? translatedTextsArray[index] : "";
        const convertedSeg = {
          start_time: seg.start_time || seg.start_time_ms || 0,
          end_time: seg.end_time || seg.end_time_ms || 0,
          original_text: originalText,
          translated_text: translatedText
        };
        if (index === 0 && windowManager) {
          windowManager.logToRenderer(`[IPC Handler - SubtitlerIO] Example converted segment (index 0): ${JSON.stringify(convertedSeg)}`);
        }
        return convertedSeg;
      });
    }
    
    const subtitlerClient = grpcManager.getSubtitlerClient();
    if (!subtitlerClient) {
      if (windowManager) windowManager.logToRenderer('[IPC Handler - SubtitlerIO] SubtitlerClient not initialized for batch-save-subtitle.');
      throw new Error('SubtitlerClient not initialized');
    }

    try {
      const response = await new Promise((resolve, reject) => {
        subtitlerClient.batchSaveSubtitle(request, {}, (error, grpcResponse) => {
          if (error) {
            if (windowManager) windowManager.logToRenderer(`[IPC Handler - SubtitlerIO] Error in batch-save-subtitle: ${error.message}`);
            reject(error);
          } else {
            if (windowManager) {
              const grpcResponseSummary = {
                success_count: grpcResponse.getSuccessCount ? grpcResponse.getSuccessCount() : grpcResponse.success_count, // Handle both proto and plain obj
                failure_count: grpcResponse.getFailureCount ? grpcResponse.getFailureCount() : grpcResponse.failure_count,
                responses_count: grpcResponse.getResponsesList ? grpcResponse.getResponsesList().length : (grpcResponse.responses ? grpcResponse.responses.length : 0),
                first_response_summary: null
              };
              const responsesList = grpcResponse.getResponsesList ? grpcResponse.getResponsesList() : (grpcResponse.responses || []);
              if (responsesList.length > 0) {
                const firstResp = responsesList[0];
                grpcResponseSummary.first_response_summary = {
                  file_path: firstResp.getFilePath ? firstResp.getFilePath() : firstResp.file_path,
                  file_name: firstResp.getFileName ? firstResp.getFileName() : firstResp.file_name,
                  saved_to_default: firstResp.getSavedToDefault ? firstResp.getSavedToDefault() : firstResp.saved_to_default
                };
              }
              windowManager.logToRenderer(`[IPC Handler - SubtitlerIO] Batch-save-subtitle successful (summary): ${JSON.stringify(grpcResponseSummary)}`);
            }
            resolve(grpcResponse);
          }
        });
      });
      return response;
    } catch (error) {
      if (windowManager) windowManager.logToRenderer(`[IPC Handler - SubtitlerIO] Batch-save-subtitle failed: ${error.message}`);
      throw error;
    }
  });
}

module.exports = {
  initializeSubtitlerIoIpcHandlers,
};