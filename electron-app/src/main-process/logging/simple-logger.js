/**
 * 简化的日志系统 - 避免递归和复杂性问题
 */
class SimpleLogger {
    constructor() {
        this.source = 'frontend-electron-main';
        this.environment = process.env.NODE_ENV || 'development';
    }
    
    generateTraceId() {
        return Math.random().toString(36).substring(2, 15);
    }
    
    formatMessage(level, message, context = {}) {
        const timestamp = new Date().toISOString().substring(11, 23);
        const traceId = context.trace_id || this.generateTraceId();
        return `[${level}] ${timestamp} [${this.source}] [${traceId.substring(0, 8)}] ${message}`;
    }
    
    log(level, message, context = {}) {
        try {
            const formatted = this.formatMessage(level, message, context);
            console.log(formatted);
            
            // 简单的额外信息
            if (context && Object.keys(context).length > 0) {
                const safeContext = {};
                for (const [key, value] of Object.entries(context)) {
                    if (typeof value === 'string' || typeof value === 'number' || typeof value === 'boolean') {
                        safeContext[key] = value;
                    } else if (value && typeof value === 'object') {
                        safeContext[key] = '[Object]';
                    }
                }
                if (Object.keys(safeContext).length > 0) {
                    console.log('  Context:', JSON.stringify(safeContext));
                }
            }
        } catch (error) {
            console.log(`[ERROR] Logger failed: ${message}`);
        }
    }
    
    debug(message, context = {}) {
        this.log('DEBUG', message, context);
    }
    
    info(message, context = {}) {
        this.log('INFO', message, context);
    }
    
    warn(message, context = {}) {
        this.log('WARN', message, context);
    }
    
    error(message, context = {}) {
        this.log('ERROR', message, context);
    }
    
    fatal(message, context = {}) {
        this.log('FATAL', message, context);
    }
    
    // 与复杂logger兼容的方法
    logOperation(operation, level = 'INFO', context = {}) {
        this.log(level, `Operation: ${operation}`, { operation, ...context });
    }
    
    logPerformance(operation, duration, context = {}) {
        this.log('INFO', `Performance: ${operation} (${duration.toFixed(2)}s)`, {
            operation,
            duration,
            ...context
        });
    }
    
    logError(error, operation = null, context = {}) {
        const errorMessage = error.message || String(error);
        this.log('ERROR', `Error in ${operation || 'unknown operation'}: ${errorMessage}`, {
            operation,
            error_name: error.name,
            ...context
        });
    }
    
    setupIPC() {
        // 简化版，不做复杂操作
        this.info('Simple logger IPC setup (no-op)');
    }
    
    createChildLogger(module) {
        return {
            debug: (message, context = {}) => this.debug(message, { module, ...context }),
            info: (message, context = {}) => this.info(message, { module, ...context }),
            warn: (message, context = {}) => this.warn(message, { module, ...context }),
            error: (message, context = {}) => this.error(message, { module, ...context }),
            fatal: (message, context = {}) => this.fatal(message, { module, ...context }),
            logOperation: (operation, level, context = {}) => this.logOperation(operation, level, { module, ...context }),
            logPerformance: (operation, duration, context = {}) => this.logPerformance(operation, duration, { module, ...context }),
            logError: (error, operation, context = {}) => this.logError(error, operation, { module, ...context })
        };
    }
}

// 全局单例
let simpleLogger = null;

function getSimpleLogger() {
    if (!simpleLogger) {
        simpleLogger = new SimpleLogger();
    }
    return simpleLogger;
}

function setupSimpleLogging(ipcMain) {
    const logger = getSimpleLogger();
    if (ipcMain) {
        logger.setupIPC(ipcMain);
    }
    return logger;
}

module.exports = {
    SimpleLogger,
    getSimpleLogger,
    setupSimpleLogging
}; 