/**
 * Electron主进程统一日志器 - 基于《通用日志标准》v1.1
 * 
 * 功能特性：
 * - ✅ 符合企业级JSON日志格式
 * - 🔗 支持trace_id全链路追踪
 * - 📡 提供IPC接口接收渲染进程日志
 * - 🎨 环境感知的输出格式控制
 * - 🔄 自动日志轮转和压缩
 * - 🛡️ 敏感数据自动脱敏
 */

const electronLog = require('electron-log');
const fs = require('fs');
const path = require('path');
const crypto = require('crypto');
const { app } = require('electron');

class UniversalMainLogger {
    constructor() {
        this.isInitialized = false;
        this.environment = process.env.NODE_ENV || 'development';
        this.config = this.loadConfig();
        this.sensitiveFields = this.config.security?.sensitive_fields || ['password', 'token', 'api_key', 'secret'];
        this.maskPattern = '***MASKED***';
        
        // 系统信息
        this.source = 'frontend-electron-main';
        this.instanceId = `${require('os').hostname()}-${process.pid}`;
        
        this.setupLogger();
    }
    
    loadConfig() {
        try {
            const configPath = path.join(__dirname, '../../../../config/logging.json');
            const configData = JSON.parse(fs.readFileSync(configPath, 'utf8'));
            const envConfig = configData.environments[this.environment] || configData.environments.development;
            
            return {
                level: envConfig.level || 'INFO',
                console: envConfig.console || { enabled: true, format: 'colored' },
                file: envConfig.file || { enabled: true, path: 'logs/app-dev.log', format: 'json' },
                rotation: configData.rotation || {},
                security: configData.security || {}
            };
        } catch (error) {
            console.error('Failed to load logging config, using defaults:', error.message);
            return {
                level: 'INFO',
                console: { enabled: true, format: 'colored' },
                file: { enabled: true, path: 'logs/app-dev.log', format: 'json' },
                rotation: { maxSize: 52428800, backupCount: 10 },
                security: { sensitive_fields: ['password', 'token', 'api_key', 'secret'] }
            };
        }
    }
    
    setupLogger() {
        // 配置日志目录
        const logDir = this.environment === 'development' 
            ? path.join(process.cwd(), 'logs') 
            : path.join(app.getPath('userData'), 'logs');
        
        if (!fs.existsSync(logDir)) {
            fs.mkdirSync(logDir, { recursive: true });
        }
        
        // 配置electron-log
        electronLog.transports.file.level = this.config.level.toLowerCase();
        electronLog.transports.file.maxSize = this.config.rotation.maxSize || 52428800;
        electronLog.transports.file.resolvePathFn = () => path.join(logDir, 'electron-main.log');
        
        // 自定义JSON格式化器
        electronLog.transports.file.format = (info) => {
            return this.formatLogEntry(info);
        };
        
        // 配置控制台输出
        if (this.config.console.enabled) {
            electronLog.transports.console.level = this.config.level.toLowerCase();
            if (this.config.console.format === 'colored') {
                electronLog.transports.console.format = (info) => {
                    return this.formatColoredConsole(info);
                };
            } else {
                electronLog.transports.console.format = (info) => {
                    return this.formatLogEntry(info);
                };
            }
        } else {
            electronLog.transports.console.level = false;
        }
        
        this.isInitialized = true;
        this.info('Universal main logger initialized', {
            source: this.source,
            environment: this.environment,
            config: this.config
        });
    }
    
    generateTraceId() {
        // 生成UUID v4 (使用Node.js内置crypto模块)
        return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
            const r = Math.random() * 16 | 0;
            const v = c === 'x' ? r : (r & 0x3 | 0x8);
            return v.toString(16);
        });
    }
    
    validateTraceId(traceId) {
        if (!traceId) return false;
        try {
            // 简单的UUID v4 格式验证
            const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
            return uuidRegex.test(traceId);
        } catch {
            return false;
        }
    }
    
    maskSensitiveData(data) {
        // 防止递归深度过大
        if (this._maskingDepth && this._maskingDepth > 10) {
            return '[深度限制]';
        }
        this._maskingDepth = (this._maskingDepth || 0) + 1;
        
        try {
            if (typeof data === 'string') {
                // 检查敏感模式
                const patterns = [
                    /\b\d{4}[-\s]?\d{4}[-\s]?\d{4}[-\s]?\d{4}\b/g, // 信用卡号
                    /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g, // 邮箱
                    /\b\d{3}-\d{2}-\d{4}\b/g // SSN
                ];
                
                let masked = data;
                patterns.forEach(pattern => {
                    masked = masked.replace(pattern, this.maskPattern);
                });
                return masked;
            }
            
            if (typeof data === 'object' && data !== null) {
                // 检查是否是Electron内部对象，跳过处理
                if (data.constructor && data.constructor.name && 
                    (data.constructor.name.includes('Electron') || 
                     data.constructor.name.includes('Screen') ||
                     data.constructor.name === 'EventEmitter')) {
                    return '[Electron对象]';
                }
                
                const masked = Array.isArray(data) ? [] : {};
                const sensitiveFields = this.sensitiveFields || ['password', 'token', 'api_key', 'secret'];
                
                try {
                    for (const [key, value] of Object.entries(data)) {
                        if (sensitiveFields.includes(key.toLowerCase())) {
                            masked[key] = this.maskPattern;
                        } else {
                            masked[key] = this.maskSensitiveData(value);
                        }
                    }
                } catch (entriesError) {
                    // 如果Object.entries失败，返回安全的替代
                    return '[无法访问的对象]';
                }
                return masked;
            }
            
            return data;
        } catch (error) {
            return '[掩码错误]';
        } finally {
            this._maskingDepth--;
        }
    }
    
    formatLogEntry(info) {
        const entry = {
            timestamp: new Date().toISOString(),
            level: info.level.toUpperCase(),
            source: this.source,
            env: this.environment,
            trace_id: info.trace_id || this.generateTraceId(),
            message: info.message,
            instance_id: this.instanceId
        };
        
        // 添加可选字段
        if (info.module) entry.module = info.module;
        if (info.function) entry.function = info.function;
        if (info.user_id) entry.user_id = info.user_id;
        if (info.duration !== undefined) entry.duration = info.duration;
        if (info.error) entry.error = info.error;
        
        // 添加额外数据到payload
        const extraData = { ...info };
        delete extraData.level;
        delete extraData.message;
        delete extraData.trace_id;
        delete extraData.module;
        delete extraData.function;
        delete extraData.user_id;
        delete extraData.duration;
        delete extraData.error;
        
        if (Object.keys(extraData).length > 0) {
            entry.payload = this.maskSensitiveData(extraData);
        }
        
        return JSON.stringify(entry);
    }
    
    formatColoredConsole(info) {
        const colors = {
            DEBUG: '\x1b[36m',   // 青色
            INFO: '\x1b[32m',    // 绿色
            WARN: '\x1b[33m',    // 黄色
            ERROR: '\x1b[31m',   // 红色
            FATAL: '\x1b[35m',   // 紫色
            RESET: '\x1b[0m'
        };
        
        const sourceColors = {
            'frontend': '\x1b[94m',  // 蓝色
            'backend': '\x1b[92m',   // 亮绿色
            'system': '\x1b[95m'     // 亮紫色
        };
        
        const level = info.level.toUpperCase();
        const levelColor = colors[level] || colors.RESET;
        const sourceColor = sourceColors[this.source.split('-')[0]] || colors.RESET;
        const reset = colors.RESET;
        
        const timestamp = new Date().toISOString().substring(11, 23); // HH:mm:ss.SSS
        const traceId = (info.trace_id || '').substring(0, 8);
        
        let formatted = `${levelColor}[${level}]${reset} ${timestamp} ${sourceColor}[${this.source}]${reset} [${traceId}] ${info.message}`;
        
        if (info.duration !== undefined) {
            formatted += ` (${info.duration.toFixed(2)}s)`;
        }
        
        return formatted;
    }
    
    log(level, message, context = {}) {
        // 临时使用简单的控制台日志避免electron-log问题
        try {
            const timestamp = new Date().toISOString().substring(11, 23);
            const traceId = (context.trace_id || this.generateTraceId()).substring(0, 8);
            const formatted = `[${level}] ${timestamp} [${this.source}] [${traceId}] ${message}`;
            console.log(formatted);
            
            // 如果有额外上下文，也简单显示
            if (context && Object.keys(context).length > 0) {
                const safeContext = {};
                for (const [key, value] of Object.entries(context)) {
                    if (typeof value === 'string' || typeof value === 'number' || typeof value === 'boolean') {
                        safeContext[key] = value;
                    }
                }
                if (Object.keys(safeContext).length > 0) {
                    console.log('  Context:', JSON.stringify(safeContext));
                }
            }
        } catch (error) {
            console.log(`[ERROR] Logger failed: ${message}`);
        }
    }
    
    debug(message, context = {}) {
        this.log('DEBUG', message, context);
    }
    
    info(message, context = {}) {
        this.log('INFO', message, context);
    }
    
    warn(message, context = {}) {
        this.log('WARN', message, context);
    }
    
    error(message, context = {}) {
        this.log('ERROR', message, context);
    }
    
    fatal(message, context = {}) {
        this.log('FATAL', message, context);
    }
    
    // 操作日志方法
    logOperation(operation, level = 'INFO', context = {}) {
        this.log(level, `Operation: ${operation}`, {
            operation,
            ...context
        });
    }
    
    // 性能日志方法
    logPerformance(operation, duration, context = {}) {
        this.log('INFO', `Performance: ${operation}`, {
            operation,
            duration,
            slow_operation: duration > 1.0,
            ...context
        });
    }
    
    // 错误日志方法
    logError(error, operation = null, context = {}) {
        const errorInfo = {
            name: error.name || 'Error',
            message: error.message || String(error),
            stack: error.stack
        };
        
        this.log('ERROR', `Error in ${operation || 'unknown operation'}`, {
            operation,
            error: errorInfo,
            ...context
        });
    }
    
    // IPC 接口 - 接收渲染进程日志
    setupIPC(ipcMain) {
        if (!ipcMain) return;
        
        ipcMain.handle('log-from-renderer', (event, logData) => {
            try {
                const { level, message, context } = logData;
                
                // 修改source为渲染进程
                const rendererContext = {
                    ...context,
                    source: 'frontend-electron-renderer',
                    renderer_id: event.sender.id
                };
                
                this.log(level, message, rendererContext);
                
                return { success: true };
            } catch (error) {
                this.error('Failed to handle renderer log', { 
                    error: { name: error.name, message: error.message },
                    operation: 'handle-renderer-log'
                });
                return { success: false, error: error.message };
            }
        });
        
        this.info('IPC logging interface setup complete', {
            operation: 'setup_ipc'
        });
    }
    
    // 创建子logger（用于不同模块）
    createChildLogger(module) {
        return {
            debug: (message, context = {}) => this.debug(message, { module, ...context }),
            info: (message, context = {}) => this.info(message, { module, ...context }),
            warn: (message, context = {}) => this.warn(message, { module, ...context }),
            error: (message, context = {}) => this.error(message, { module, ...context }),
            fatal: (message, context = {}) => this.fatal(message, { module, ...context }),
            logOperation: (operation, level, context = {}) => this.logOperation(operation, level, { module, ...context }),
            logPerformance: (operation, duration, context = {}) => this.logPerformance(operation, duration, { module, ...context }),
            logError: (error, operation, context = {}) => this.logError(error, operation, { module, ...context })
        };
    }
}

// 全局单例
let mainLogger = null;

function getMainLogger() {
    if (!mainLogger) {
        mainLogger = new UniversalMainLogger();
    }
    return mainLogger;
}

function setupMainLogging(ipcMain) {
    const logger = getMainLogger();
    if (ipcMain) {
        logger.setupIPC(ipcMain);
    }
    return logger;
}

module.exports = {
    UniversalMainLogger,
    getMainLogger,
    setupMainLogging
};