const { getMainLogger } = require('./logging/main-logger.js');
const logger = getMainLogger();

const net = require('net');
const { exec } = require('child_process');
const { dialog } = require('electron');
// logTo<PERSON><PERSON><PERSON> will be imported from window-manager or passed as a dependency.
// For now, let's assume it will be passed or set via an init function.
let logTo<PERSON>enderer; // To be set by an initializer or passed into functions

function initializeLogger(logFunction) {
    logToRenderer = logFunction;
}

function extractPort(addressString) {
  if (typeof addressString !== 'string') return null;
  const parts = addressString.split(':');
  if (parts.length === 2) {
    const portNum = parseInt(parts[1], 10);
    if (!isNaN(portNum)) {
      return portNum;
    }
  }
  return null;
}

function checkPort(port) {
  return new Promise((resolve) => {
    const server = net.createServer();
    server.once('error', (err) => {
      if (err.code === 'EADDRINUSE') {
        resolve(true); // Port is definitely in use
      } else {
        if (logTo<PERSON><PERSON>er) logToRenderer(`[Port Check] Error checking port ${port}: ${err.message}`);
        else logger.info(`[Port Check - No Logger] Error checking port ${port}: ${err.message}`);
        resolve(true); // Consider other errors as port unavailable/problematic
      }
      server.close(() => {});
    });
    server.once('listening', () => {
      server.close(() => {
        resolve(false); // Port is available
      });
    });
    server.listen(port, '127.0.0.1', () => {});
  });
}

function getPIDFromPort_macOS(port) {
  return new Promise((resolve) => {
    if (process.platform !== 'darwin') {
      if (logToRenderer) logToRenderer('[Port Management] getPIDFromPort_macOS is only supported on macOS.');
      else logger.info('[Port Management - No Logger] getPIDFromPort_macOS is only supported on macOS.');
      resolve(null);
      return;
    }
    exec(`lsof -i tcp:${port} -sTCP:LISTEN -P -n | awk 'NR>1 {print $2}'`, (error, stdout, stderr) => {
      if (error) {
        if (error.code !== 1) { // Error code 1 means lsof found nothing, which is not an execution error.
            if (logToRenderer) logToRenderer(`[Port Management] Error executing lsof for port ${port}: ${error.message}`);
            else logger.info(`[Port Management - No Logger] Error executing lsof for port ${port}: ${error.message}`);
        }
        resolve(null);
        return;
      }
      if (stderr) {
        if (logToRenderer) logToRenderer(`[Port Management] Stderr executing lsof for port ${port}: ${stderr}`);
        else logger.info(`[Port Management - No Logger] Stderr executing lsof for port ${port}: ${stderr}`);
      }
      const pid = parseInt(stdout.trim(), 10);
      if (!isNaN(pid) && pid > 0) {
        resolve(pid);
      } else {
        resolve(null);
      }
    });
  });
}

function killProcessWithPID(pid) { // Renamed to avoid conflict with process.kill
  return new Promise((resolve) => {
    try {
      if (logToRenderer) logToRenderer(`[Port Management] Attempting to kill process with PID ${pid} using SIGKILL.`);
      else logger.info(`[Port Management - No Logger] Attempting to kill process with PID ${pid} using SIGKILL.`);
      process.kill(pid, 'SIGKILL'); // Node.js built-in process.kill
      resolve(true);
    } catch (err) {
      if (logToRenderer) logToRenderer(`[Port Management] Failed to kill process ${pid}: ${err.message}`);
      else logger.info(`[Port Management - No Logger] Failed to kill process ${pid}: ${err.message}`);
      resolve(false);
    }
  });
}

async function checkAndManagePort(port, serviceName, mainWindowInstance, updateServiceStatusCallback) {
  if (!logToRenderer && console.log) logToRenderer = console.log; // Basic fallback if not initialized

  if (port === null || isNaN(port)) {
    logToRenderer(`[Port Management] Invalid port ${port} for ${serviceName}. Cannot check.`);
    dialog.showErrorBox('Port Check Error', `Invalid port configured for ${serviceName}.`);
    if (updateServiceStatusCallback) updateServiceStatusCallback(serviceName.toLowerCase().split(' ')[0], 'Port Error', 'bg-red-500');
    return false;
  }

  const isOccupied = await checkPort(port);

  if (!isOccupied) {
    logToRenderer(`[Port Management] Port ${port} for ${serviceName} is free.`);
    return true;
  }

  logToRenderer(`[Port Management] Port ${port} for ${serviceName} is occupied.`);
  if (!mainWindowInstance) {
    logToRenderer(`[Port Management] No mainWindow instance provided to show dialog for ${serviceName} on port ${port}.`);
    if (updateServiceStatusCallback) updateServiceStatusCallback(serviceName.toLowerCase().split(' ')[0], 'Port Conflict (No UI)', 'bg-red-500');
    return false; // Cannot proceed without UI interaction
  }

  const { response } = await dialog.showMessageBox(mainWindowInstance, {
    type: 'warning',
    title: 'Port Conflict',
    message: `${serviceName}'s designated port ${port} is currently in use.`,
    detail: `Another application might be using port ${port}. What would you like to do?`,
    buttons: ['Try to Terminate Occupying Process', 'Cancel Launching ' + serviceName],
    defaultId: 0,
    cancelId: 1,
  });

  if (response === 1) { // User chose to cancel
    logToRenderer(`[Port Management] User cancelled launch of ${serviceName} due to port ${port} being occupied.`);
    if (updateServiceStatusCallback) updateServiceStatusCallback(serviceName.toLowerCase().split(' ')[0], 'Cancelled (Port)', 'bg-yellow-500');
    return false;
  }

  if (process.platform !== 'darwin') {
    dialog.showErrorBox('Unsupported Action', `Automatic process termination by port is currently only supported on macOS. Please free up port ${port} manually.`);
    if (updateServiceStatusCallback) updateServiceStatusCallback(serviceName.toLowerCase().split(' ')[0], 'Port Conflict (Manual)', 'bg-red-500');
    return false;
  }

  logToRenderer(`[Port Management] Attempting to find and kill process on port ${port} for ${serviceName}.`);
  const pid = await getPIDFromPort_macOS(port);

  if (!pid) {
    logToRenderer(`[Port Management] Could not find PID for process on port ${port}.`);
    dialog.showErrorBox('Termination Failed', `Could not identify the process using port ${port}. Please close it manually and try again.`);
    if (updateServiceStatusCallback) updateServiceStatusCallback(serviceName.toLowerCase().split(' ')[0], 'Port Conflict (PID Fail)', 'bg-red-500');
    return false;
  }

  const killed = await killProcessWithPID(pid);
  if (!killed) {
    logToRenderer(`[Port Management] Failed to kill process with PID ${pid} on port ${port}.`);
    dialog.showErrorBox('Termination Failed', `Failed to terminate process (PID: ${pid}) using port ${port}. It might require manual intervention or administrator privileges.`);
    if (updateServiceStatusCallback) updateServiceStatusCallback(serviceName.toLowerCase().split(' ')[0], 'Port Conflict (Kill Fail)', 'bg-red-500');
    return false;
  }

  logToRenderer(`[Port Management] Process ${pid} on port ${port} presumably killed. Re-checking port status after a short delay...`);
  await new Promise(resolveDelay => setTimeout(resolveDelay, 500)); 

  const isNowFree = !(await checkPort(port));
  if (isNowFree) {
    logToRenderer(`[Port Management] Port ${port} is now free. ${serviceName} can proceed.`);
    return true;
  } else {
    logToRenderer(`[Port Management] Port ${port} is still occupied after attempting to kill PID ${pid}.`);
    dialog.showErrorBox('Termination Failed', `Port ${port} is still in use even after attempting to terminate the occupying process (PID: ${pid}). Please check manually.`);
    if (updateServiceStatusCallback) updateServiceStatusCallback(serviceName.toLowerCase().split(' ')[0], 'Port Conflict (Still Used)', 'bg-red-500');
    return false;
  }
}

module.exports = {
  initializeLogger,
  extractPort,
  checkPort,
  getPIDFromPort_macOS,
  killProcessWithPID,
  checkAndManagePort,
};