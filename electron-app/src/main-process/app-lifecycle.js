const { getMainLogger } = require('./logging/main-logger.js');
const logger = getMainLogger();

const { app, dialog, ipcMain } = require('electron');
const windowManager = require('./window-manager');
const backendManager = require('./backend-manager');
const grpcManager = require('./grpc-manager');
// const ipcHandlers = require('./ipc-handlers'); // No longer needed
const { initializeGreeterIpcHandlers } = require('./ipc/greeter-handlers');
const { initializeFilesystemIpcHandlers } = require('./ipc/filesystem-handlers');
const { initializeSubtitlerIoIpcHandlers } = require('./ipc/subtitler-io-handlers');
const { initializeSubtitlerWorkflowIpcHandlers } = require('./ipc/subtitler-workflow-handlers');
const { initializeGrpcTestHandlers } = require('./ipc/grpc-test-handlers'); // Added
const { initializeStorageIpcHandlers } = require('./ipc/storage-handlers'); // Added for storage and operations
const appUtils = require('./utils'); // General utilities
const aiConfigHandler = require('./ai-config-handler'); // Added for AI Config

// This function will be called from the main.js entry point
function initializeAppLifecycleEvents() {
  // Register a simple debug log handler from renderer directly here
  ipcMain.on('debug-log-from-renderer', (event, message) => {
    logger.info(`[AppLifecycle - Renderer Debug] ${message}`);
  });

  // Register navigation handlers
  ipcMain.handle('open-ai-settings', async () => {
    // Send message to renderer to navigate to AI settings
    const mainWindow = windowManager.getMainWindow();
    if (mainWindow) {
      mainWindow.webContents.send('navigate-to-view', 'AISettingsView');
      return { success: true };
    }
    return { success: false, message: 'Main window not available' };
  });

  // Initialize managers with dependencies
  grpcManager.initializeGrpcManager(windowManager); 
  backendManager.initializeBackendManager(windowManager, grpcManager); 
  
  initializeGreeterIpcHandlers(grpcManager, windowManager);
  initializeFilesystemIpcHandlers(windowManager);
  initializeSubtitlerIoIpcHandlers(grpcManager, windowManager, appUtils);
  initializeSubtitlerWorkflowIpcHandlers(grpcManager, windowManager, appUtils);
  initializeGrpcTestHandlers(grpcManager, windowManager); 
  initializeStorageIpcHandlers(windowManager); // Added for storage and operations
  aiConfigHandler.setupAIConfigIPC(); // Added for AI Config

  app.on('ready', async () => {
    windowManager.createWindow(
        () => { /* onWindowClosedCallback */ },
        grpcManager.initializeSubtitlerClientAfterWindowLoad 
    );

    try {
      await grpcManager.setupGrpcConnections();
      logger.info('[AppLifecycle] gRPC connections setup completed.');

      // Start backend processes first
      logger.info('[AppLifecycle] Starting backend processes...');

      // Check environment variables to control which backends to start
      const skipPython = process.env.SKIP_PYTHON_BACKEND === 'true';
      const skipGo = process.env.SKIP_GO_BACKEND === 'true';
      const skipJava = process.env.SKIP_JAVA_BACKEND === 'true';

      if (skipPython) {
        logger.info('[AppLifecycle] Skipping Python backend startup (SKIP_PYTHON_BACKEND=true)');
      } else {
        setTimeout(backendManager.startPythonBackend, 500);  // Start Python backend earlier
      }

      if (skipGo) {
        logger.info('[AppLifecycle] Skipping Go backend startup (SKIP_GO_BACKEND=true)');
      } else {
        setTimeout(backendManager.startGoBackend, 1500);
      }

      if (skipJava) {
        logger.info('[AppLifecycle] Skipping Java backend startup (SKIP_JAVA_BACKEND=true)');
      } else {
        setTimeout(backendManager.startJavaBackend, 2000);
      }

      // After backend processes are started, wait longer before pushing initial configs
      // to ensure the Python gRPC server is fully ready
      setTimeout(async () => {
        if (aiConfigHandler && typeof aiConfigHandler.pushInitialConfigsToBackend === 'function') {
          logger.info('[AppLifecycle] Attempting to push initial AI configs to backend after extended delay.');
          await aiConfigHandler.pushInitialConfigsToBackend();
        } else {
          logger.error('[AppLifecycle] aiConfigHandler.pushInitialConfigsToBackend is not available.');
        }
      }, 5000); // Increased to 5-second delay to give Python backend more time

    } catch (error) {
      dialog.showErrorBox('gRPC Initialization Error', `Failed to initialize gRPC services: ${error.message}. Some features may not work.`);
    }
  });

  app.on('window-all-closed', () => {
    if (process.platform !== 'darwin') {
      app.quit();
    }
  });

  app.on('will-quit', () => {
    backendManager.killAllBackendProcesses();
  });

  app.on('activate', () => {
    if (windowManager.getMainWindow() === null) {
      windowManager.createWindow(
          () => {}, 
          grpcManager.initializeSubtitlerClientAfterWindowLoad
      );
    }
  });
}

module.exports = {
  initializeAppLifecycleEvents,
};