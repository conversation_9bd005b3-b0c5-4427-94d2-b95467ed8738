// electron-app/src/main-process/types/dto/subtitler/main_process_subtitler_dtos.ts
import { MainProcessMainProcessOperationStatusEnum, MainProcessErrorDetail, StageSpecificErrorDetail, AggregatedErrorSummary } from '../common/main_process_common_dtos';

// ========== Basic DTOs ==========

export interface MainProcessVideoToAudioResponse {
  audioPath: string;
  audioData: Buffer;
  traceId: string;
}

export interface MainProcessTimestampedTextSegment {
  text: string;
  startTimeMs?: number;
  endTimeMs?: number;
  status?: MainProcessOperationStatusEnum;
  errorDetail?: MainProcessErrorDetail;
}

export interface MainProcessAudioToTextResponse {
  segments: MainProcessTimestampedTextSegment[];
  traceId: string;
  totalSegmentsProcessed: number;
  successfulSegments: number;
  failedSegments: number;
}

export interface MainProcessGenerateSubtitlesResponse {
  srtContent: string;
  assContent: string;
  traceId: string;
}

export interface MainProcessTranslatedSegment {
  segmentId: string;
  originalText: string;
  translatedText: string;
  status?: MainProcessOperationStatusEnum;
  errorDetail?: MainProcessErrorDetail;
  startTimeMs?: number;
  endTimeMs?: number;
}

export interface MainProcessTranslateSubtitlesResponse {
  translatedSubtitleContent: string;
  traceId: string;
  segmentResults: MainProcessTranslatedSegment[];
  totalSegmentsProcessed: number;
  successfulSegments: number;
  failedSegments: number;
}

export interface MainProcessTextToTranslate {
  segmentId: string;
  textToTranslate: string;
}

export interface MainProcessTranslateRequest {
  taskId: string;
  textSegments: MainProcessTextToTranslate[];
  sourceLanguage?: string;
  targetLanguage: string;
  traceId: string;
}

export interface MainProcessTranslateResponse {
  taskId: string;
  translatedSegments: MainProcessTranslatedSegment[];
  errorDetail?: MainProcessErrorDetail;
  traceId: string;
}

export interface MainProcessProcessVideoToTranslatedSubtitlesResponse {
  translatedSubtitleContent: string;
  traceId: string;
}

// ========== Enhanced DTOs with Error Aggregation ==========

export interface MainProcessVideoMetadata {
  duration?: number;
  resolution?: string;
  codec?: string;
  bitrate?: number;
  framerate?: number;
  error?: StageSpecificErrorDetail;
  isFallbackData: boolean;
  fallbackReason?: string;
}

export interface MainProcessKeyframeExtractionResult {
  keyframeTimestamps?: number[];
  previewImagePaths?: string[];
  extractedCount: number;
  targetCount: number;
  error?: StageSpecificErrorDetail;
  isFallbackData: boolean;
  fallbackReason?: string;
}

export interface MainProcessAudioExtractionResult {
  audioPath?: string;
  duration?: number;
  sampleRate?: number;
  channels?: number;
  format?: string;
  error?: StageSpecificErrorDetail;
  isFallbackData: boolean;
  fallbackReason?: string;
}

// ========== Progress Update DTO ==========

export interface MainProcessProgressUpdate {
  traceId: string;
  stageName: string;
  percentage: number;
  message: string;
  status?: MainProcessOperationStatusEnum;
  errorDetail?: MainProcessErrorDetail;
  // 注意：移除了 data 字段，所有结果数据现在通过 oneof final_result 字段传递

  // For oneof final_result - all optional
  videoToAudioResponse?: MainProcessVideoToAudioResponse;
  audioToTextResponse?: MainProcessAudioToTextResponse;
  generateSubtitlesResponse?: MainProcessGenerateSubtitlesResponse;
  translateSubtitlesResponse?: MainProcessTranslateSubtitlesResponse;
  processVideoToTranslatedSubtitlesResponse?: MainProcessProcessVideoToTranslatedSubtitlesResponse;

  // Deprecated fields
  isError?: boolean;
  errorMessage?: string;

  // Enhanced fields for structured data and error aggregation
  structuredData?: 
    | MainProcessVideoMetadata
    | MainProcessKeyframeExtractionResult
    | MainProcessAudioExtractionResult
    | MainProcessAudioToTextResponse
    | Record<string, any>;
  dataType?: string;
  dataStatus?: string; // "PARTIAL_SUCCESS_WITH_FALLBACK", "STAGE_ERROR", "COMPLETE"
  aggregatedError?: AggregatedErrorSummary;

  // Additional main-process specific fields
  currentStage?: string;
  totalStages?: number;
}

// ========== Request DTOs ==========

export interface MainProcessVideoToAudioRequest {
  videoPath: string;
  traceId: string;
}

export interface MainProcessAudioToTextRequest {
  audioPath: string;
  audioData: Buffer;
  requestWordTimestamps: boolean;
  skipCache: boolean;
  traceId: string;
}

export interface MainProcessRecognizeRequest {
  audioPath: string;
  audioData: Buffer;
  requestWordTimestamps: boolean;
  skipCache: boolean;
  traceId: string;
}

export interface MainProcessGenerateSubtitlesRequest {
  text: string;
  audioPath: string;
  skipCache: boolean;
  traceId: string;
}

export interface MainProcessTranslateSubtitlesRequest {
  subtitleContent: string;
  targetLanguage: string;
  skipCache: boolean;
  traceId: string;
}

export interface MainProcessProcessVideoToTranslatedSubtitlesRequest {
  videoPath: string;
  targetLanguage: string;
  traceId: string;
}

export interface MainProcessSubtitleSegment {
  startTime: number;
  endTime: number;
  originalText: string;
  translatedText: string;
  status?: MainProcessOperationStatusEnum;
  errorDetail?: MainProcessErrorDetail;
}

export interface MainProcessSaveSubtitleRequest {
  subtitleContent: string;
  format: string;
  layout: string;
  fileName: string;
  originalContent: string;
  translatedContent: string;
  segments: MainProcessSubtitleSegment[];
  autoSaveToDefault: boolean;
  assStyleOptions?: Record<string, any>;
  traceId: string;
}

export interface MainProcessSaveSubtitleResponse {
  filePath: string;
  fileData: Buffer;
  fileName: string;
  fileSize: number;
  savedToDefault: boolean;
  format: string;
  layout: string;
  contentSource: string;
  originalFilenameOrTitle: string;
  traceId: string;
  status?: MainProcessOperationStatusEnum;
  errorDetail?: MainProcessErrorDetail;
}

export interface MainProcessBatchSaveSubtitleRequest {
  formats: string[];
  layouts: string[];
  contentSources: string[];
  fileNamePrefix: string;
  originalContent: string;
  translatedContent: string;
  segments: MainProcessSubtitleSegment[];
  autoSaveToDefault: boolean;
  translationRequested: boolean;
  assStyleOptions?: Record<string, any>;
  traceId: string;
}

export interface MainProcessBatchSaveSubtitleResponse {
  files: MainProcessSaveSubtitleResponse[];
  traceId: string;
  status?: MainProcessOperationStatusEnum;
  errorDetail?: MainProcessErrorDetail;
}

export interface MainProcessClearCacheRequest {
  cacheType: string;
  traceId: string;
}

export interface MainProcessClearCacheResponse {
  success: boolean;
  message: string;
  traceId: string;
  status?: MainProcessOperationStatusEnum;
  errorDetail?: MainProcessErrorDetail;
}

// ========== Language Support DTOs ==========

export interface MainProcessLanguageInfo {
  code: string;
  name: string;
}

export interface MainProcessGetTranscriptionLanguagesRequest {
  // Empty for now
}

export interface MainProcessGetTranscriptionLanguagesResponse {
  languages: MainProcessLanguageInfo[];
  errorDetail?: MainProcessErrorDetail;
}

export interface MainProcessLanguagePair {
  sourceLanguage: MainProcessLanguageInfo;
  targetLanguages: MainProcessLanguageInfo[];
}

export interface MainProcessGetTranslationLanguagesRequest {
  // Empty for now
}

export interface MainProcessGetTranslationLanguagesResponse {
  languagePairs: MainProcessLanguagePair[];
  errorDetail?: MainProcessErrorDetail;
}