/**
 * 渲染进程统一日志工具 - 基于《通用日志标准》v1.1
 * 
 * 功能特性：
 * - ✅ 通过IPC将日志发送到主进程
 * - 🔗 支持trace_id传播和管理
 * - 📊 自动收集Vue组件上下文
 * - 🎯 支持用户操作追踪
 * - 🛡️ 自动敏感数据脱敏
 * - ⚡ 异步日志发送，不阻塞UI
 */

class UniversalRendererLogger {
    constructor() {
        this.source = 'frontend-electron-renderer';
        this.environment = process.env.NODE_ENV || 'development';
        this.currentTraceId = null;
        this.userId = null;
        this.batchQueue = [];
        this.batchTimer = null;
        this.batchInterval = 100; // 100ms批量发送
        this.maxBatchSize = 10;
        
        // 敏感字段配置
        this.sensitiveFields = ['password', 'token', 'api_key', 'secret'];
        this.maskPattern = '***MASKED***';
        
        this.initializeTraceId();
    }
    
    initializeTraceId() {
        // 如果没有当前trace_id，生成一个新的
        if (!this.currentTraceId) {
            this.currentTraceId = this.generateTraceId();
        }
    }
    
    generateTraceId() {
        // 简单的UUID v4生成
        return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
            const r = Math.random() * 16 | 0;
            const v = c === 'x' ? r : (r & 0x3 | 0x8);
            return v.toString(16);
        });
    }
    
    validateTraceId(traceId) {
        if (!traceId) return false;
        const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
        return uuidRegex.test(traceId);
    }
    
    setTraceId(traceId) {
        if (this.validateTraceId(traceId)) {
            this.currentTraceId = traceId;
        } else {
            console.warn('Invalid trace_id format, generating new one');
            this.currentTraceId = this.generateTraceId();
        }
    }
    
    getTraceId() {
        return this.currentTraceId;
    }
    
    setUserId(userId) {
        this.userId = userId;
    }
    
    getUserId() {
        return this.userId;
    }
    
    maskSensitiveData(data) {
        if (typeof data === 'string') {
            // 检查敏感模式
            const patterns = [
                /\b\d{4}[-\s]?\d{4}[-\s]?\d{4}[-\s]?\d{4}\b/g, // 信用卡号
                /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g, // 邮箱
                /\b\d{3}-\d{2}-\d{4}\b/g // SSN
            ];
            
            let masked = data;
            patterns.forEach(pattern => {
                masked = masked.replace(pattern, this.maskPattern);
            });
            return masked;
        }
        
        if (typeof data === 'object' && data !== null) {
            const masked = Array.isArray(data) ? [] : {};
            for (const [key, value] of Object.entries(data)) {
                if (this.sensitiveFields.includes(key.toLowerCase())) {
                    masked[key] = this.maskPattern;
                } else {
                    masked[key] = this.maskSensitiveData(value);
                }
            }
            return masked;
        }
        
        return data;
    }
    
    getCallerInfo() {
        try {
            const error = new Error();
            const stack = error.stack?.split('\n') || [];
            
            // 寻找非日志工具的调用者
            for (let i = 3; i < stack.length; i++) {
                const line = stack[i];
                if (line && !line.includes('logger.js') && !line.includes('at Object.')) {
                    const match = line.match(/at\s+(.+?)\s+\((.+?):(\d+):\d+\)/);
                    if (match) {
                        return {
                            function: match[1],
                            file: match[2].split('/').pop(),
                            line: parseInt(match[3])
                        };
                    }
                }
            }
        } catch (e) {
            // 忽略错误
        }
        
        return {};
    }
    
    async sendLogToMain(level, message, context = {}) {
        try {
            const logEntry = {
                level: level.toUpperCase(),
                message,
                context: {
                    trace_id: context.trace_id || this.currentTraceId,
                    user_id: context.user_id || this.userId,
                    source: this.source,
                    env: this.environment,
                    ...this.getCallerInfo(),
                    ...this.maskSensitiveData(context)
                }
            };
            
            // 批量发送优化
            this.batchQueue.push(logEntry);
            
            if (this.batchQueue.length >= this.maxBatchSize) {
                this.flushBatch();
            } else if (!this.batchTimer) {
                this.batchTimer = setTimeout(() => {
                    this.flushBatch();
                }, this.batchInterval);
            }
            
        } catch (error) {
            console.error('Failed to send log to main process:', error);
        }
    }
    
    async flushBatch() {
        if (this.batchQueue.length === 0) return;
        
        const batch = [...this.batchQueue];
        this.batchQueue = [];
        
        if (this.batchTimer) {
            clearTimeout(this.batchTimer);
            this.batchTimer = null;
        }
        
        try {
            if (window.electronAPI && window.electronAPI.ipcRenderer) {
                for (const logEntry of batch) {
                    await window.electronAPI.ipcRenderer.invoke('log-from-renderer', logEntry);
                }
            } else {
                // 回退到console输出
                batch.forEach(entry => {
                    console[entry.level.toLowerCase()](`[${entry.level}] ${entry.message}`, entry.context);
                });
            }
        } catch (error) {
            console.error('Failed to flush log batch:', error);
            // 回退输出
            batch.forEach(entry => {
                console[entry.level.toLowerCase()](`[${entry.level}] ${entry.message}`, entry.context);
            });
        }
    }
    
    // 标准日志方法
    debug(message, context = {}) {
        this.sendLogToMain('DEBUG', message, context);
    }
    
    info(message, context = {}) {
        this.sendLogToMain('INFO', message, context);
    }
    
    warn(message, context = {}) {
        this.sendLogToMain('WARN', message, context);
    }
    
    error(message, context = {}) {
        this.sendLogToMain('ERROR', message, context);
    }
    
    fatal(message, context = {}) {
        this.sendLogToMain('FATAL', message, context);
    }
    
    // 业务方法
    logUserAction(action, details = {}) {
        this.info(`User action: ${action}`, {
            user_action: action,
            action_details: details,
            timestamp: new Date().toISOString()
        });
    }
    
    logOperation(operation, level = 'INFO', context = {}) {
        this.sendLogToMain(level, `Operation: ${operation}`, {
            operation,
            ...context
        });
    }
    
    logPerformance(operation, duration, context = {}) {
        this.info(`Performance: ${operation}`, {
            operation,
            duration,
            slow_operation: duration > 1000, // >1秒为慢操作
            ...context
        });
    }
    
    logError(error, operation = null, context = {}) {
        const errorInfo = {
            name: error.name || 'Error',
            message: error.message || String(error),
            stack: error.stack
        };
        
        this.error(`Error in ${operation || 'unknown operation'}`, {
            operation,
            error: errorInfo,
            ...context
        });
    }
    
    logApiCall(url, method, duration, status, context = {}) {
        const level = status >= 400 ? 'ERROR' : 'INFO';
        this.sendLogToMain(level, `API call: ${method} ${url}`, {
            api_url: url,
            api_method: method,
            api_status: status,
            duration,
            ...context
        });
    }
    
    // Vue.js集成方法
    logVueComponent(componentName, action, context = {}) {
        this.debug(`Vue component ${action}: ${componentName}`, {
            component: componentName,
            vue_action: action,
            ...context
        });
    }
    
    logVueError(error, componentName, context = {}) {
        this.error(`Vue component error in ${componentName}`, {
            component: componentName,
            error: {
                name: error.name || 'VueError',
                message: error.message || String(error),
                stack: error.stack
            },
            ...context
        });
    }
    
    logStoreAction(action, payload, context = {}) {
        this.debug(`Store action: ${action}`, {
            store_action: action,
            payload: this.maskSensitiveData(payload),
            ...context
        });
    }
    
    logStoreError(error, action, context = {}) {
        this.error(`Store action error: ${action}`, {
            store_action: action,
            error: {
                name: error.name || 'StoreError',
                message: error.message || String(error),
                stack: error.stack
            },
            ...context
        });
    }
    
    // 上下文管理
    createOperationContext(operation, traceId = null) {
        const operationTraceId = traceId || this.generateTraceId();
        this.setTraceId(operationTraceId);
        
        return {
            traceId: operationTraceId,
            startTime: Date.now(),
            
            log: (level, message, context = {}) => {
                this.sendLogToMain(level, message, {
                    trace_id: operationTraceId,
                    operation,
                    ...context
                });
            },
            
            complete: (result = null) => {
                const duration = Date.now() - this.startTime;
                this.logPerformance(operation, duration, {
                    trace_id: operationTraceId,
                    result: result ? 'success' : 'completed'
                });
            },
            
            error: (error) => {
                const duration = Date.now() - this.startTime;
                this.logError(error, operation, {
                    trace_id: operationTraceId,
                    duration
                });
            }
        };
    }
    
    // 清理资源
    cleanup() {
        if (this.batchTimer) {
            clearTimeout(this.batchTimer);
            this.batchTimer = null;
        }
        this.flushBatch();
    }
}

// 全局单例
let rendererLogger = null;

function getRendererLogger() {
    if (!rendererLogger) {
        rendererLogger = new UniversalRendererLogger();
    }
    return rendererLogger;
}

// 便捷导出
const logger = getRendererLogger();

// 页面卸载时清理（仅在浏览器环境中）
if (typeof window !== 'undefined') {
    window.addEventListener('beforeunload', () => {
        logger.cleanup();
    });
}

// 兼容性导出（支持Node.js和ES6模块）
if (typeof module !== 'undefined' && module.exports) {
    // CommonJS导出方式（兼容Node.js直接运行）
    module.exports = logger;
    module.exports.getRendererLogger = getRendererLogger;
    module.exports.UniversalRendererLogger = UniversalRendererLogger;
}