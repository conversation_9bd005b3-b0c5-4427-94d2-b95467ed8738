<template>
  <div class="example-component">
    <h2>日志集成示例组件</h2>
    <button @click="handleUserAction">测试用户操作日志</button>
    <button @click="handleApiCall">测试API调用日志</button>
    <button @click="handleError">测试错误日志</button>
  </div>
</template>

<script>
import logger from '@/utils/logger.js';

export default {
  name: 'LoggingExampleComponent',
  
  data() {
    return {
      componentTraceId: null
    };
  },
  
  created() {
    // 组件创建时记录日志
    this.componentTraceId = logger.generateTraceId();
    logger.setTraceId(this.componentTraceId);
    
    logger.logVueComponent(this.$options.name, 'created', {
      trace_id: this.componentTraceId
    });
  },
  
  mounted() {
    // 组件挂载时记录日志
    logger.logVueComponent(this.$options.name, 'mounted', {
      trace_id: this.componentTraceId
    });
  },
  
  beforeDestroy() {
    // 组件销毁前记录日志
    logger.logVueComponent(this.$options.name, 'beforeDestroy', {
      trace_id: this.componentTraceId
    });
  },
  
  methods: {
    handleUserAction() {
      // 用户操作日志
      logger.logUserAction('button_click', {
        component: this.$options.name,
        action: 'test_user_action',
        trace_id: this.componentTraceId
      });
    },
    
    async handleApiCall() {
      // API调用日志示例
      const operation = logger.createOperationContext('api_test_call', this.componentTraceId);
      
      try {
        operation.log('INFO', 'Starting API call test');
        
        // 模拟API调用
        const startTime = Date.now();
        await new Promise(resolve => setTimeout(resolve, 1000));
        const duration = Date.now() - startTime;
        
        logger.logApiCall('/api/test', 'GET', duration, 200, {
          trace_id: this.componentTraceId
        });
        
        operation.complete('success');
        
      } catch (error) {
        operation.error(error);
        logger.logVueError(error, this.$options.name, {
          trace_id: this.componentTraceId
        });
      }
    },
    
    handleError() {
      try {
        // 故意制造错误
        throw new Error('This is a test error for logging demonstration');
      } catch (error) {
        // 错误日志示例
        logger.logVueError(error, this.$options.name, {
          trace_id: this.componentTraceId,
          user_action: 'test_error_handling'
        });
      }
    }
  },
  
  // Vue错误处理钩子
  errorCaptured(err, instance, info) {
    logger.logVueError(err, this.$options.name, {
      trace_id: this.componentTraceId,
      error_info: info,
      child_component: instance.$options.name
    });
    
    return false; // 继续传播错误
  }
};
</script>

<style scoped>
.example-component {
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
  margin: 10px;
}

button {
  margin: 5px;
  padding: 10px 15px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

button:hover {
  background: #0056b3;
}
</style> 