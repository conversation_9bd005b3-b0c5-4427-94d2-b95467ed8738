{"name": "electron-python-grpc-pex-demo", "version": "1.0.0", "description": "Electron app with Python and Go/Java gRPC backends", "author": "MonkeyFX", "main": "main.js", "scripts": {"start": "npm run dev", "watch": "electron . --watch", "dev": "npm run build:check && npm run kill-grpc && npm run build:css:once && vite", "build:vite": "vite build", "build:electron": "electron-builder", "build": "npm run build:vite && npm run build:electron", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "test:run": "vitest run", "build:css": "tailwindcss -i ./src/css/input.css -o ./src/css/output.css --watch", "build:css:once": "tailwindcss -i ./src/css/input.css -o ./src/css/output.css", "build:css:minify": "tailwindcss -i ./src/css/input.css -o ./src/css/output.css --minify", "build:check": "node ../scripts/check-dependencies.js", "build:proto": "cd .. && ./scripts/proto_sync.sh --all", "build:backends": "cd .. && ./scripts/proto_sync.sh --all", "build:all": "npm run build:proto && npm run build:css:once", "build:incremental": "cd .. && ./scripts/proto_sync.sh --all", "build:complete": "cd .. && ./scripts/build-all.sh", "build:cross-platform": "cd .. && ./scripts/build-cross-platform.sh", "build:verify": "cd .. && ./scripts/verify-build.sh", "clean": "npm run kill-grpc", "fresh": "npm run clean && npm run build:all", "force-refresh": "npm run kill-grpc && npm run sync-protos && npm run build:css:once && echo 'Force refresh completed! You can now run npm start'", "kill-grpc": "lsof -ti:50051,50052,50053 | xargs -r kill -9 || echo 'No gRPC processes found on ports 50051, 50052, 50053'", "sync-protos": "cd .. && ./scripts/proto_sync.sh --all --force", "lint": "eslint src/**/*.{vue,js,ts}", "lint:fix": "eslint src/**/*.{vue,js,ts} --fix", "format": "prettier --write \"src/**/*.{vue,js,ts,json,css,scss,md}\"", "format:check": "prettier --check \"src/**/*.{vue,js,ts,json,css,scss,md}\"", "type-check": "vue-tsc --noEmit", "prepare": "husky install"}, "build": {"appId": "com.monkeyfx.subtitle-processor", "productName": "字幕处理工具", "directories": {"output": "dist-electron"}, "files": ["dist/**/*", "main.js", "preload.js", "package.json", "src/main-process/**/*", "src/js/**/*", "src/node-grpc-client.js", "node_modules/**/*", "!**/*.proto", "!**/*.py", "!**/*.go", "!**/*.java", "!**/*.git*", "!**/node_modules/*/{CHANGELOG.md,README.md,README,readme.md,readme}", "!**/node_modules/*/{test,__tests__,tests,powered-test,example,examples}", "!**/node_modules/*.d.ts", "!**/node_modules/.bin", "!**/*.{iml,o,hprof,orig,pyc,pyo,rbc,swp,csproj,sln,xproj}", "!**/._*", "!**/{.DS_Store,.git,.hg,.svn,CVS,RCS,SCCS,.gitignore,.gitattributes}", "!*.env", "!**/.env", "!src/vue/**/*", "!src/css/**/*", "!dist-electron/**/*"], "extraResources": [{"from": "../dist", "to": "dist", "filter": ["**/*"]}, {"from": "../api-protos", "to": "api-protos", "filter": ["**/*.proto"]}, {"from": "../config", "to": "config", "filter": ["**/*"]}], "mac": {"category": "public.app-category.productivity", "target": [{"target": "dmg", "arch": ["x64", "arm64"]}, {"target": "zip", "arch": ["x64", "arm64"]}]}, "win": {"target": [{"target": "nsis", "arch": ["x64"]}, {"target": "zip", "arch": ["x64"]}]}, "linux": {"target": [{"target": "AppImage", "arch": ["x64"]}, {"target": "tar.gz", "arch": ["x64"]}]}}, "dependencies": {"@grpc/grpc-js": "1.9.13", "@grpc/proto-loader": "0.7.10", "dotenv": "^16.5.0", "electron-log": "^5.4.0", "electron-store": "^8.1.0", "google-protobuf": "3.21.4", "pinia": "^2.1.7", "vue": "^3.4.27"}, "devDependencies": {"@eslint/js": "^9.28.0", "@types/node": "^22.15.30", "@typescript-eslint/eslint-plugin": "^8.33.1", "@typescript-eslint/parser": "^8.33.1", "@vitejs/plugin-vue": "^5.0.5", "@vitest/coverage-v8": "^1.3.1", "@vue/eslint-config-prettier": "^10.2.0", "@vue/eslint-config-typescript": "^14.5.0", "@vue/test-utils": "^2.4.5", "@vue/tsconfig": "^0.7.0", "autoprefixer": "^10.4.19", "electron": "^29.0.1", "electron-builder": "^24.9.1", "eslint": "^9.28.0", "eslint-plugin-vue": "^10.2.0", "grpc-tools": "1.12.4", "husky": "^9.1.7", "jsdom": "^24.0.0", "lint-staged": "^16.1.0", "prettier": "^3.5.3", "tailwindcss": "^3.4.1", "ts-node": "^10.9.2", "typescript": "^5.8.3", "vite": "^5.2.11", "vite-plugin-electron": "^0.28.6", "vite-plugin-electron-renderer": "^0.14.5", "vitest": "^1.3.1", "vue-tsc": "^2.2.10"}}