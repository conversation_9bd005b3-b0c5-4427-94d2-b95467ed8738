#!/usr/bin/env python3
"""
测试音频提取修复的脚本
"""

import sys
import os
sys.path.append('backend')

from subtitle.workflow.transcriptThread import TranscriptThread
from subtitle.utils.progress_factory import create_success_progress
from subtitle.constants import ResponseFieldNames, ProgressKeys

def test_audio_extraction():
    print("🧪 开始测试音频提取修复...")
    
    # 测试视频路径
    test_video = "/Users/<USER>/Downloads/12/7.other-techniques.mp4"
    
    if not os.path.exists(test_video):
        print(f"❌ 测试视频文件不存在: {test_video}")
        return False
    
    thread = TranscriptThread()
    
    # 捕获所有进度更新
    progress_updates = []
    
    try:
        for progress in thread.extract_audio_from_video(test_video, "test-trace-id"):
            progress_updates.append(progress)
            print(f"📝 进度更新: {progress.get('stage_name', 'Unknown')} - {progress.get('percentage', 0)}% - {progress.get('message', '')}")
            
            # 检查是否有最终结果
            final_result = progress.get(ProgressKeys.FINAL_RESULT, {})
            if final_result:
                print(f"🎯 找到最终结果: {final_result}")
                if ResponseFieldNames.VIDEO_TO_AUDIO_RESPONSE in final_result:
                    v2a_response = final_result[ResponseFieldNames.VIDEO_TO_AUDIO_RESPONSE]
                    print(f"✅ 成功找到 video_to_audio_response: {v2a_response}")
                    audio_path = v2a_response.get('audio_path')
                    if audio_path and os.path.exists(audio_path):
                        print(f"✅ 音频文件存在: {audio_path}")
                        return True
                    else:
                        print(f"❌ 音频文件不存在: {audio_path}")
                        return False
    
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    print("❌ 没有找到 video_to_audio_response")
    return False

def test_progress_factory():
    print("🧪 测试 progress_factory...")

    # 测试创建包含 final_result 的成功进度
    test_payload = {
        "audio_path": "/test/path/audio.wav",
        "audio_data": b"",
        "trace_id": "test-trace"
    }

    success_progress = create_success_progress(
        stage_name="TestStage",
        message="测试完成",
        trace_id="test-trace",
        final_result={ResponseFieldNames.VIDEO_TO_AUDIO_RESPONSE: test_payload}
    )

    print(f"📝 创建的成功进度: {success_progress}")

    # 检查结构
    final_result = success_progress.get(ProgressKeys.FINAL_RESULT, {})
    if ResponseFieldNames.VIDEO_TO_AUDIO_RESPONSE in final_result:
        print("✅ progress_factory 正确创建了 video_to_audio_response")
        return True
    else:
        print("❌ progress_factory 没有正确创建 video_to_audio_response")
        return False

def test_backend_conversion():
    """测试后端 DTO 到 protobuf 的转换"""
    print("🧪 测试后端 DTO 到 protobuf 转换...")

    try:
        from backend.data_models.dtos.subtitler_dtos import ProgressUpdate, VideoToAudioResponse
        from backend.utils.conversion_utils import convert_dto_to_proto
        from backend.api_protos.v1.subtitler import subtitler_pb2

        # 创建测试数据
        video_to_audio_response_dto = VideoToAudioResponse(
            audio_path="/test/path/audio.wav",
            audio_data=b"",
            trace_id="test-trace"
        )

        progress_dto = ProgressUpdate(
            trace_id="test-trace",
            stage_name="ExtractAudio",
            percentage=100,
            message="音频提取完成",
            status="OPERATION_STATUS_SUCCESS",
            video_to_audio_response=video_to_audio_response_dto
        )

        print(f"📝 创建的 ProgressUpdateDto: {progress_dto}")
        print(f"📝 video_to_audio_response: {progress_dto.video_to_audio_response}")

        # 转换为 protobuf
        proto_message = convert_dto_to_proto(progress_dto, subtitler_pb2.ProgressUpdate)

        print(f"📝 转换后的 protobuf 消息类型: {type(proto_message)}")
        print(f"📝 protobuf 消息字段: {[f.name for f in proto_message.DESCRIPTOR.fields]}")

        # 检查 oneof 字段
        if hasattr(proto_message, 'video_to_audio_response'):
            vtar = proto_message.video_to_audio_response
            print(f"📝 protobuf video_to_audio_response: {vtar}")
            if vtar and hasattr(vtar, 'audio_path'):
                print(f"✅ protobuf 包含 audio_path: {vtar.audio_path}")
                return True
            else:
                print("❌ protobuf video_to_audio_response 没有 audio_path")
                return False
        else:
            print("❌ protobuf 消息没有 video_to_audio_response 字段")
            return False

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("=" * 50)
    print("🔧 测试音频提取修复")
    print("=" * 50)
    
    # 测试进度工厂
    factory_ok = test_progress_factory()
    print()

    # 测试后端转换
    conversion_ok = test_backend_conversion()
    print()

    # 测试音频提取
    if factory_ok and conversion_ok:
        extraction_ok = test_audio_extraction()
    else:
        print("⚠️ 跳过音频提取测试，因为前置测试有问题")
        extraction_ok = False
    
    print()
    print("=" * 50)
    if factory_ok and extraction_ok:
        print("✅ 所有测试通过！修复应该有效。")
    else:
        print("❌ 测试失败，需要进一步修复。")
    print("=" * 50) 