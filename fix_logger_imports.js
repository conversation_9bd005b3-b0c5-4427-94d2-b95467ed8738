const fs = require('fs');
const path = require('path');

const filesToFix = [
    'electron-app/src/main-process/ipc/greeter-handlers.js',
    'electron-app/src/main-process/ipc/storage-handlers.js',
    'electron-app/src/main-process/utils/subtitler-utils.js',
    'electron-app/src/main-process/ipc/subtitler-io-handlers.js',
    'electron-app/src/main-process/window-manager.js',
    'electron-app/src/main-process/ipc/filesystem-handlers.js',
    'electron-app/src/main-process/grpc-manager.js',
    'electron-app/src/main-process/port-manager.js',
    'electron-app/src/main-process/ipc/subtitler-workflow-handlers.js'
];

filesToFix.forEach(filePath => {
    if (fs.existsSync(filePath)) {
        let content = fs.readFileSync(filePath, 'utf8');
        
        // Replace ES6 import with CommonJS require
        content = content.replace(
            /import logger from '\.\.\/utils\/logger\.js';/g,
            "const logger = require('../logging/main-logger.js');"
        );
        
        // Also handle other potential variations
        content = content.replace(
            /import logger from '\.\.\/\.\.\/utils\/logger\.js';/g,
            "const logger = require('../logging/main-logger.js');"
        );
        
        fs.writeFileSync(filePath, content, 'utf8');
        console.log(`✅ Fixed: ${filePath}`);
    } else {
        console.log(`❌ File not found: ${filePath}`);
    }
});

console.log('✨ All logger imports have been fixed!'); 