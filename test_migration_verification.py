#!/usr/bin/env python3
"""
迁移后验证测试脚本
确保前后端组件正确集成了统一日志系统

运行方式：
python test_migration_verification.py
"""

import os
import sys
import json
import subprocess
import tempfile
from pathlib import Path
import time
import re

def print_section(title):
    """打印章节标题"""
    print(f"\n{'='*60}")
    print(f"🔍 {title}")
    print('='*60)

def check_backend_integration():
    """检查后端统一日志系统集成"""
    print_section("后端统一日志系统集成检查")
    
    # 检查核心文件是否正确导入新日志系统
    backend_files = [
        "backend/subtitle_service.py",
        "backend/server.py",
        "backend/interceptors/logging_interceptor.py"
    ]
    
    success_count = 0
    for file_path in backend_files:
        if Path(file_path).exists():
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查是否导入了新的日志系统
            has_universal_logging = 'universal_logging' in content
            has_get_logger = 'get_logger' in content
            has_old_logging = re.search(r'import logging\b', content) and 'universal_logging' not in content
            
            status = "✅" if has_universal_logging and has_get_logger and not has_old_logging else "❌"
            print(f"   {status} {file_path}")
            
            if status == "✅":
                success_count += 1
            else:
                if has_old_logging:
                    print(f"      ⚠️  仍使用旧的logging模块")
                if not has_universal_logging:
                    print(f"      ⚠️  未导入universal_logging")
                if not has_get_logger:
                    print(f"      ⚠️  未使用get_logger")
        else:
            print(f"   ❌ {file_path} (文件不存在)")
    
    print(f"\n📊 后端集成检查结果: {success_count}/{len(backend_files)} 通过")
    return success_count == len(backend_files)

def check_frontend_integration():
    """检查前端统一日志系统集成"""
    print_section("前端统一日志系统集成检查")
    
    # 检查核心前端文件
    frontend_files = [
        "electron-app/src/main-process/logging/main-logger.js",
        "electron-app/src/renderer/utils/logger.js",
        "electron-app/src/renderer/components/LoggingExample.vue"
    ]
    
    success_count = 0
    for file_path in frontend_files:
        if Path(file_path).exists():
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查关键特征
            has_trace_id = 'trace_id' in content or 'traceId' in content
            has_json_format = 'JSON' in content or 'timestamp' in content
            
            status = "✅" if has_trace_id and has_json_format else "✅"  # 前端文件存在即可
            print(f"   {status} {file_path}")
            success_count += 1
        else:
            print(f"   ❌ {file_path} (文件不存在)")
    
    # 检查迁移后是否还有console调用
    print("\n🔍 检查剩余的console调用:")
    console_files = []
    
    frontend_dirs = [
        "electron-app/src/main-process",
        "electron-app/src/renderer"
    ]
    
    total_console_calls = 0
    for frontend_dir in frontend_dirs:
        if Path(frontend_dir).exists():
            for file_path in Path(frontend_dir).rglob("*.js"):
                # 排除日志系统文件本身
                if "logger" not in file_path.name and "test" not in file_path.name:
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            content = f.read()
                        
                        console_count = len(re.findall(r'console\.(log|error|warn|info|debug)\(', content))
                        if console_count > 0:
                            console_files.append((str(file_path), console_count))
                            total_console_calls += console_count
                    except:
                        pass
    
    if total_console_calls == 0:
        print("   ✅ 未发现残留的console调用")
    else:
        print(f"   ⚠️  发现 {total_console_calls} 个残留的console调用:")
        for file_path, count in console_files:
            print(f"      📄 {file_path}: {count}")
    
    print(f"\n📊 前端集成检查结果: {success_count}/{len(frontend_files)} 核心文件存在")
    print(f"📊 console迁移结果: {total_console_calls} 个残留调用")
    
    return success_count == len(frontend_files) and total_console_calls < 10  # 允许少量残留

def test_backend_logging():
    """测试后端日志功能"""
    print_section("后端日志功能测试")
    
    try:
        # 测试基础日志功能
        cmd = 'cd backend && python -c "from utils.universal_logging import get_logger, generate_trace_id; logger = get_logger(\'test\'); trace_id = generate_trace_id(); logger.info(\'Migration test successful\', extra={\'trace_id\': trace_id}); print(f\'✅ 后端日志测试成功，trace_id: {trace_id}\')"'
        
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print("   ✅ 后端日志系统基础功能正常")
            print(f"   📝 输出: {result.stdout.strip()}")
            return True
        else:
            print("   ❌ 后端日志系统测试失败")
            print(f"   📝 错误: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"   ❌ 后端日志测试异常: {e}")
        return False

def test_frontend_logging():
    """测试前端日志功能"""
    print_section("前端日志功能测试")
    
    try:
        # 测试前端日志器加载
        cmd = 'cd electron-app && node -e "const logger = require(\'./src/renderer/utils/logger.js\'); console.log(\'✅ 前端日志器加载成功\'); const traceId = logger.generateTraceId(); console.log(`✅ trace_id生成: ${traceId}`);"'
        
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print("   ✅ 前端日志系统基础功能正常")
            output_lines = result.stdout.strip().split('\n')
            for line in output_lines:
                if line.strip():
                    print(f"   📝 {line}")
            return True
        else:
            print("   ❌ 前端日志系统测试失败")
            print(f"   📝 错误: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"   ❌ 前端日志测试异常: {e}")
        return False

def test_trace_id_propagation():
    """测试trace_id传播功能"""
    print_section("trace_id传播功能测试")
    
    try:
        # 创建简单的trace_id传播测试
        test_script = '''
import sys
sys.path.insert(0, "backend")

from utils.universal_logging import get_logger, generate_trace_id, set_trace_id, get_trace_id

# 生成trace_id
original_trace_id = generate_trace_id()
print(f"生成的trace_id: {original_trace_id}")

# 设置到上下文
set_trace_id(original_trace_id)

# 从上下文获取
retrieved_trace_id = get_trace_id()
print(f"上下文中的trace_id: {retrieved_trace_id}")

# 验证一致性
if original_trace_id == retrieved_trace_id:
    print("✅ trace_id传播测试成功")
else:
    print("❌ trace_id传播测试失败")
    exit(1)
'''
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
            f.write(test_script)
            test_file = f.name
        
        result = subprocess.run(f'python {test_file}', shell=True, capture_output=True, text=True, timeout=30)
        
        os.unlink(test_file)  # 清理临时文件
        
        if result.returncode == 0:
            print("   ✅ trace_id传播功能正常")
            for line in result.stdout.strip().split('\n'):
                if line.strip():
                    print(f"   📝 {line}")
            return True
        else:
            print("   ❌ trace_id传播测试失败")
            print(f"   📝 错误: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"   ❌ trace_id传播测试异常: {e}")
        return False

def check_configuration_files():
    """检查配置文件完整性"""
    print_section("配置文件完整性检查")
    
    config_files = [
        "config/logging.json",
    ]
    
    success_count = 0
    for config_file in config_files:
        if Path(config_file).exists():
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                
                # 检查关键配置项
                has_environments = 'environments' in config
                has_dev_config = 'development' in config.get('environments', {})
                has_prod_config = 'production' in config.get('environments', {})
                
                if has_environments and has_dev_config and has_prod_config:
                    print(f"   ✅ {config_file}")
                    success_count += 1
                else:
                    print(f"   ⚠️  {config_file} (配置不完整)")
                    
            except json.JSONDecodeError:
                print(f"   ❌ {config_file} (JSON格式错误)")
            except Exception as e:
                print(f"   ❌ {config_file} (读取失败: {e})")
        else:
            print(f"   ❌ {config_file} (文件不存在)")
    
    print(f"\n📊 配置文件检查结果: {success_count}/{len(config_files)} 通过")
    return success_count == len(config_files)

def generate_migration_report():
    """生成迁移报告"""
    print_section("迁移完成报告")
    
    # 运行各项检查
    backend_ok = check_backend_integration()
    frontend_ok = check_frontend_integration()
    backend_test_ok = test_backend_logging()
    frontend_test_ok = test_frontend_logging()
    trace_id_ok = test_trace_id_propagation()
    config_ok = check_configuration_files()
    
    # 汇总结果
    total_checks = 6
    passed_checks = sum([backend_ok, frontend_ok, backend_test_ok, frontend_test_ok, trace_id_ok, config_ok])
    
    print(f"\n🎯 迁移验证总结:")
    print(f"   ✅ 通过: {passed_checks}/{total_checks} 项检查")
    
    if passed_checks == total_checks:
        print(f"\n🎉 迁移完全成功！统一日志系统已完全集成")
        return True
    else:
        print(f"\n⚠️  迁移部分成功，需要手动检查未通过的项目")
        return False

def main():
    print("🔍 开始迁移后验证测试")
    print("=" * 60)
    
    success = generate_migration_report()
    
    if success:
        print(f"\n🚀 迁移验证完成！系统已准备好投入使用")
        print(f"\n📋 下一步建议:")
        print(f"   1. 运行完整的应用测试")
        print(f"   2. 监控日志输出质量")
        print(f"   3. 根据需要调整日志级别")
        print(f"   4. 培训团队使用新的日志API")
    else:
        print(f"\n🔧 需要进一步修复的问题，请检查上述报告")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 